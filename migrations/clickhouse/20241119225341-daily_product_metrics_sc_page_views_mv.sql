CREATE MATERIALIZED VIEW goteacher.daily_product_metrics_sc_page_views_mv ON CLUSTER '{cluster_multiple_shard}'
TO goteacher.daily_product_metrics_sc_local (
    `day` DateTime,
    `schoolYear` String,
    `productId` String,        
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID),
    `page_views_student` AggregateFunction(count, Int64),
    `unique_views_student` AggregateFunction(uniq, UUID),
    `page_views_teacher` AggregateFunction(count, Int64),
    `unique_views_teacher` AggregateFunction(uniq, UUID),
    `page_views_admin` AggregateFunction(count, Int64),
    `unique_views_admin` AggregateFunction(uniq, UUID)
) AS
SELECT
    day,
    schoolYear,
    productId,
    orgId,
    schoolId,
    grade,
    countMergeState(page_views) as page_views,
    uniqMergeState(unique_views) as unique_views,
    countMergeState(page_views_student) as page_views_student,
    uniqMergeState(unique_views_student) as unique_views_student,
    countMergeState(page_views_teacher) as page_views_teacher,
    uniqMergeState(unique_views_teacher) as unique_views_teacher,
    countMergeState(page_views_admin) as page_views_admin,
    uniqMergeState(unique_views_admin) as unique_views_admin
FROM goteacher.daily_page_views_sc_local
GROUP BY
    day,
    schoolYear,
    productId,    
    orgId,
    schoolId,
    grade;