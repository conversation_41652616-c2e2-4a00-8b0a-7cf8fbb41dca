CREATE TABLE goteacher.visits_sc_local ON CLUSTER '{cluster_multiple_shard}' (
    `schoolYear` String,
    `sessionId` UUID,
    `tabId` UUID,
    `userId` UUID,
    `role` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `domain` String,
    `productId` String,
    `fullDomain` String,
    `url` String,
    `visit_start` SimpleAggregateFunction(min, DateTime),
    `visit_end` SimpleAggregateFunction(max, DateTime),
    `visit_timezone` SimpleAggregateFunction(any, String)
) ENGINE = ReplicatedAggregatingMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/visits_sc_local',
    '{multiple_replica}'
) PARTITION BY (schoolYear, toYYYYMM(visit_start), orgId)
ORDER BY
    (
        schoolYear,
        sessionId,
        tabId,
        userId,
        role,
        orgId,
        schoolId,
        grade,
        domain,
        productId,
        url
    ) SETTINGS index_granularity = 8192;
