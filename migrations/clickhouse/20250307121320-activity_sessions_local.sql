CREATE TABLE goteacher.activity_sessions_local ON CLUSTER '{cluster_multiple_shard}' (
    `id` UUID DEFAULT generateUUIDv4(),
    `schoolYear` String,
    `sessionId` UUID,
    `tabId` UUID,
    `userId` UUID,
    `role` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `domain` String,
    `productId` String,
    `fullDomain` String,
    `url` String,
    `startTime` DateTime,
    `endTime` DateTime,
    `duration` Int32, -- duration in seconds
    `clientTimezone` String,
    `serverTimestamp` DateTime DEFAULT now(),
    `createdAt` DateTime DEFAULT now()
) ENGINE = ReplicatedMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/activity_sessions_local',
    '{multiple_replica}'
) PARTITION BY (schoolYear, toYYYYMM(startTime), orgId)
ORDER BY
    (
        startTime,
        domain,
        url,
        userId,
        orgId,
        schoolId,
        grade,
        role,
        schoolYear,
        sessionId,
        tabId
    ) SETTINGS index_granularity = 8192;