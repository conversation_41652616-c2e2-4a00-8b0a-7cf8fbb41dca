CREATE MATERIALIZED VIEW goteacher.all_product_metrics_sc_active_users_mv ON CLUSTER '{cluster_multiple_shard}'
TO goteacher.all_product_metrics_sc_local (
    `productId` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `active_users` AggregateFunction(uniq, UUID),
    `active_users_student` AggregateFunction(uniq, UUID),
    `active_users_teacher` AggregateFunction(uniq, UUID),
    `active_users_admin` AggregateFunction(uniq, UUID),
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID),
    `page_views_student` AggregateFunction(count, Int64),
    `unique_views_student` AggregateFunction(uniq, UUID),
    `page_views_teacher` AggregateFunction(count, Int64),
    `unique_views_teacher` AggregateFunction(uniq, UUID),
    `page_views_admin` AggregateFunction(count, Int64),
    `unique_views_admin` AggregateFunction(uniq, UUID)
) AS
SELECT
    dau.productId AS productId,
    dau.orgId as orgId,
    dau.schoolId as schoolId,
    dau.grade as grade,
    uniqMergeState(dau.active_users) AS active_users,
    uniqMergeState(dau.active_users_student) AS active_users_student,
    uniqMergeState(dau.active_users_teacher) AS active_users_teacher,
    uniqMergeState(dau.active_users_admin) AS active_users_admin
FROM
    goteacher.weekly_product_metrics_sc_local AS dau
GROUP BY
    productId,    
    orgId,
    schoolId,
    grade;
