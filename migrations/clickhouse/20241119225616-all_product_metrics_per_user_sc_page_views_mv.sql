CREATE MATERIALIZED VIEW goteacher.all_product_metrics_per_user_sc_page_views_mv ON CLUSTER '{cluster_multiple_shard}'
TO goteacher.all_product_metrics_per_user_sc_local (
    `productId` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID)
) AS
SELECT
    productId,
    orgId,
    schoolId,
    grade,
    userId,
    countState(page_views) as page_views,    
    uniqMergeState(unique_views) AS unique_views
FROM goteacher.weekly_product_metrics_per_user_sc_local
GROUP BY
    productId,
    orgId,
    schoolId,
    grade,
    userId;
