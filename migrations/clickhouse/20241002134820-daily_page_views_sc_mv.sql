CREATE MATERIALIZED VIEW goteacher.daily_page_views_sc_mv ON CLUSTER '{cluster_multiple_shard}' TO goteacher.daily_page_views_sc_local (
    `day` DateTime,
    `schoolYear` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `domain` String,
    `productId` String,
    `fullDomain` String,
    `url` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID),
    `page_views_student` AggregateFunction(count, Int64),
    `unique_views_student` AggregateFunction(uniq, UUID),
    `page_views_teacher` AggregateFunction(count, Int64),
    `unique_views_teacher` AggregateFunction(uniq, UUID),
    `page_views_admin` AggregateFunction(count, Int64),
    `unique_views_admin` AggregateFunction(uniq, UUID)
) AS
SELECT
    toStartOfDay(cae.`clientTimestamp`) AS day,
    cae.schoolYear as schoolYear,
    cae.`orgId` AS orgId,
    cae.`schoolId` AS schoolId,
    cae.`grade` AS grade,
    cae.`domain` AS domain,
    cae.`productId` AS productId,
    cae.`fullDomain` AS fullDomain,
    cae.`url` AS url,
    cae.`userId` AS userId,
    countStateIf(cae.`eventName` = 'PAGE_OPEN') AS page_views,
    uniqStateIf(cae.`userId`, cae.`eventName` = 'PAGE_OPEN') AS unique_views,
    countStateIf(
        cae.`eventName` = 'PAGE_OPEN'
        AND cae.`role` = 'student'
    ) AS page_views_student,
    uniqStateIf(
        cae.`userId`,
        cae.`eventName` = 'PAGE_OPEN'
        AND cae.`role` = 'student'
    ) AS unique_views_student,
    countStateIf(
        cae.eventName = 'PAGE_OPEN'
        AND cae.`role` = 'teacher'
    ) AS page_views_teacher,
    uniqStateIf(
        cae.userId,
        cae.eventName = 'PAGE_OPEN'
        AND cae.`role` = 'teacher'
    ) AS unique_views_teacher,
    countStateIf(
        cae.eventName = 'PAGE_OPEN'
        AND cae.`role` = 'administrator'
    ) AS page_views_admin,
    uniqStateIf(
        cae.userId,
        cae.eventName = 'PAGE_OPEN'
        AND cae.`role` = 'administrator'
    ) AS unique_views_admin
FROM
    goteacher.enriched_events_local AS cae
WHERE cae.orgId IS NOT NULL
GROUP BY
    day,
    schoolYear,
    orgId,
    schoolId,
    grade,
    domain,
    url,
    userId,
    productId,
    fullDomain;
