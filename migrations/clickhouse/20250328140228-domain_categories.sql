CREATE TABLE IF NOT EXISTS goteacher.domain_categories ON CLUSTER '{cluster_single_shard}' (    
    domain String,
    fullDomain String DEFAULT '',
    category String,
    riskLevel String,
    orgId UUID DEFAULT '00000000-0000-0000-0000-000000000000'
) ENGINE = ReplicatedMergeTree(
    '/clickhouse/tables/{uuid}/{single_shard}/domain_categories',
    '{single_replica}'
)
ORDER BY (domain, orgId);
