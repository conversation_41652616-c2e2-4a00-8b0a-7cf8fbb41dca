CREATE MATERIALIZED VIEW goteacher.daily_product_metrics_sc_active_users_mv ON CLUSTER '{cluster_multiple_shard}' 
TO goteacher.daily_product_metrics_sc_local (
    `day` DateTime,
    `schoolYear` String,
    `productId` String,        
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `active_users` AggregateFunction(uniq, UUID),
    `active_users_student` AggregateFunction(uniq, UUID),
    `active_users_teacher` AggregateFunction(uniq, UUID),
    `active_users_admin` AggregateFunction(uniq, UUID)
) AS
SELECT
    dau.day AS day,
    dau.schoolYear AS schoolYear,
    dau.productId AS productId,        
    dau.orgId as orgId,
    dau.schoolId as schoolId,
    dau.grade as grade,
    uniqMergeState(dau.active_users) AS active_users,
    uniqMergeState(dau.active_users_student) AS active_users_student,
    uniqMergeState(dau.active_users_teacher) AS active_users_teacher,
    uniqMergeState(dau.active_users_admin) AS active_users_admin
FROM
    goteacher.daily_active_users_sc_local AS dau
GROUP BY
    day,
    schoolYear,
    productId,
    orgId,
    schoolId,
    grade;
