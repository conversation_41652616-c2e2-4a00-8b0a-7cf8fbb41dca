CREATE TABLE goteacher.daily_page_views_sc_local ON CLUSTER '{cluster_multiple_shard}' (
    `day` DateTime,
    `schoolYear` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `domain` String,
    `productId` String,
    `fullDomain` String,
    `url` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID),
    `page_views_student` AggregateFunction(count, Int64),
    `unique_views_student` AggregateFunction(uniq, UUID),
    `page_views_teacher` AggregateFunction(count, Int64),
    `unique_views_teacher` AggregateFunction(uniq, UUID),
    `page_views_admin` AggregateFunction(count, Int64),
    `unique_views_admin` AggregateFunction(uniq, UUID)
) ENGINE = ReplicatedAggregatingMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/daily_page_views_sc_local',
    '{multiple_replica}'
) PARTITION BY (schoolYear, toYYYYMM(day), orgId)
ORDER BY
    (
        day,
        schoolYear,
        domain,
        url,
        orgId,
        schoolId,
        grade,
        userId
    ) SETTINGS index_granularity = 8192;