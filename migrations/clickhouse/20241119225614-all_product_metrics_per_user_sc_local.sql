CREATE TABLE goteacher.all_product_metrics_per_user_sc_local ON CLUSTER '{cluster_multiple_shard}' (
    `productId` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID)
) ENGINE = ReplicatedAggregatingMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/all_product_metrics_per_user_sc_local',
    '{multiple_replica}'
) PARTITION BY (orgId)
ORDER BY (productId, orgId, schoolId, grade, userId)
SETTINGS index_granularity = 8192;
