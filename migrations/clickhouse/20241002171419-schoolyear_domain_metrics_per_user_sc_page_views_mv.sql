CREATE MATERIALIZED VIEW goteacher.schoolyear_domain_metrics_per_user_sc_page_views_mv ON CLUSTER '{cluster_multiple_shard}' TO goteacher.schoolyear_domain_metrics_per_user_sc_local (
    `schoolYear` String,
    `domain` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID),
    `page_views_student` AggregateFunction(count, Int64),
    `unique_views_student` AggregateFunction(uniq, UUID),
    `page_views_teacher` AggregateFunction(count, Int64),
    `unique_views_teacher` AggregateFunction(uniq, UUID),
    `page_views_admin` AggregateFunction(count, Int64),
    `unique_views_admin` AggregateFunction(uniq, UUID)
) AS
SELECT
    pvd.schoolYear AS schoolYear,
    pvd.domain AS domain,
    pvd.orgId as orgId,
    pvd.schoolId as schoolId,
    pvd.grade as grade,
    pvd.userId AS userId,
    countMergeState(pvd.page_views) AS page_views,
    uniqMergeState(pvd.unique_views) AS unique_views,
    countMergeState(pvd.page_views_student) AS page_views_student,
    uniqMergeState(pvd.unique_views_student) AS unique_views_student,
    countMergeState(pvd.page_views_teacher) AS page_views_teacher,
    uniqMergeState(pvd.unique_views_teacher) AS unique_views_teacher,
    countMergeState(pvd.page_views_admin) AS page_views_admin,
    uniqMergeState(pvd.unique_views_admin) AS unique_views_admin
FROM
    goteacher.daily_page_views_sc_local AS pvd
GROUP BY
    schoolYear,
    domain,
    orgId,
    schoolId,
    grade,
    userId;