CREATE MATERIALIZED VIEW goteacher.daily_product_metrics_per_user_sc_page_views_mv ON CLUSTER '{cluster_multiple_shard}'
TO goteacher.daily_product_metrics_per_user_sc_local (
    `day` DateTime,
    `schoolYear` String,
    `productId` String,    
    `domain` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID)
) AS
SELECT
    day,
    schoolYear,
    productId,    
    orgId,
    schoolId,
    grade,
    userId,
    countMergeState(page_views) as page_views,
    uniqMergeState(unique_views) as unique_views
FROM goteacher.daily_page_views_sc_local
GROUP BY
    day,
    schoolYear,
    productId,    
    orgId,
    schoolId,
    grade,
    userId;
