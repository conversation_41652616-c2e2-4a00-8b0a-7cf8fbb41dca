CREATE TABLE goteacher.daily_active_users_sc_local ON CLUSTER '{cluster_multiple_shard}' (
    `day` DateTime,
    `schoolYear` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `domain` String,
    `productId` String,
    `fullDomain` String,
    `url` String,
    `active_users` AggregateFunction(uniq, UUID),
    `active_users_student` AggregateFunction(uniq, UUID),
    `active_users_teacher` AggregateFunction(uniq, UUID),
    `active_users_admin` AggregateFunction(uniq, UUID)
) ENGINE = ReplicatedAggregatingMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/daily_active_users_sc_local',
    '{multiple_replica}'
) PARTITION BY (schoolYear, toYYYYMM(day), orgId)
ORDER BY
    (
        day,
        schoolYear,
        orgId,
        schoolId,
        grade,
        domain,
        productId,
        url
    ) SETTINGS index_granularity = 8192;
