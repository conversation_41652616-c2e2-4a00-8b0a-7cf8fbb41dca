CREATE TABLE goteacher.all_url_metrics_per_user_sc_local ON CLUSTER '{cluster_multiple_shard}' (
    `domain` String,
    `url` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID),
    `page_views_student` AggregateFunction(count, Int64),
    `unique_views_student` AggregateFunction(uniq, UUID),
    `page_views_teacher` AggregateFunction(count, Int64),
    `unique_views_teacher` AggregateFunction(uniq, UUID),
    `page_views_admin` AggregateFunction(count, Int64),
    `unique_views_admin` AggregateFunction(uniq, UUID)
) ENGINE = ReplicatedAggregatingMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/all_url_metrics_per_user_sc_local',
    '{multiple_replica}'
) PARTITION BY orgId
ORDER BY
    (domain, url, orgId, schoolId, grade, userId) SETTINGS index_granularity = 8192;