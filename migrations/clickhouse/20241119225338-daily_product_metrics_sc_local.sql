CREATE TABLE IF NOT EXISTS goteacher.daily_product_metrics_sc_local ON CLUSTER '{cluster_multiple_shard}' (
    `day` DateTime,
    `schoolYear` String,
    `productId` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `active_users` AggregateFunction(uniq, UUID),
    `active_users_student` AggregateFunction(uniq, UUID),
    `active_users_teacher` AggregateFunction(uniq, UUID),
    `active_users_admin` AggregateFunction(uniq, UUID),
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID),
    `page_views_student` AggregateFunction(count, Int64),
    `unique_views_student` AggregateFunction(uniq, UUID),
    `page_views_teacher` AggregateFunction(count, Int64),
    `unique_views_teacher` AggregateFunction(uniq, UUID),
    `page_views_admin` AggregateFunction(count, Int64),
    `unique_views_admin` AggregateFunction(uniq, UUID)
) ENGINE = ReplicatedAggregatingMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/daily_product_metrics_sc_local',
    '{multiple_replica}'
) PARTITION BY (schoolYear, toYYYYMM(day), orgId)
ORDER BY (day, schoolYear, productId, orgId, schoolId, grade)
SETTINGS index_granularity = 8192;
