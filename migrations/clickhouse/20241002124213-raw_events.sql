CREATE TABLE IF NOT EXISTS goteacher.raw_events ON CLUSTER '{cluster_single_shard}' (
    `id` UUID DEFAULT generateUUIDv4(),
    `sessionId` UUID,
    `userId` UUID,
    `clientTimestamp` DateTime DEFAULT now(),
    `clientTimezone` String DEFAULT timeZone(),
    `serverTimestamp` DateTime DEFAULT now(),
    `eventName` String,
    `ip` String DEFAULT '0.0.0.0',
    `browserType` String DEFAULT 'unknown',
    `platform` String DEFAULT 'unknown',
    `language` String DEFAULT 'unknown',
    `tabId` UUID DEFAULT generateUUIDv4(),
    `domain` String DEFAULT '',
    `url` String DEFAULT '',
    `ref` String DEFAULT '',
    `clickSelector` String DEFAULT '',
    `clickText` String DEFAULT '',
    `clickXCoordinate` Int32 DEFAULT 0,
    `clickYCoordinate` Int32 DEFAULT 0,
    `scrollDirection` String DEFAULT 'DOWN',
    `scrollDistance` Int32 DEFAULT 0,
    `scrollCurrentPosition` Int32 DEFAULT 0,
    `scrollTotalPageHeight` Int32 DEFAULT 0,
    `scrollViewPortSize` String DEFAULT '0x0',
    `mediaSource` String DEFAULT '',
    `mediaSeek` Int32 DEFAULT 0,
    `mediaSeekFrom` Int32 DEFAULT 0,
    `mediaSeekTo` Int32 DEFAULT 0,
    `mediaLength` Int32 DEFAULT 0,
    `mediaType` String DEFAULT 'VIDEO',
    `mediaVolume` Int32 DEFAULT 0,
    `mediaSpeed` String DEFAULT '1X',
    `mouseXCoordinate` Int32 DEFAULT 0,
    `mouseYCoordinate` Int32 DEFAULT 0,
    `createdAt` DateTime DEFAULT now(),
    `traceId` String DEFAULT 'unknown'
) ENGINE = ReplicatedMergeTree(
    '/clickhouse/tables/{uuid}/{single_shard}/raw_events',
    '{single_replica}'
) PARTITION BY toYYYYMM(clientTimestamp)
ORDER BY
    (
        clientTimestamp,
        domain,
        url,
        userId,
        sessionId,
        tabId,
        eventName
    ) SETTINGS index_granularity = 8192;