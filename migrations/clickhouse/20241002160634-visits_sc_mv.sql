CREATE MATERIALIZED VIEW goteacher.visits_sc_mv ON CLUSTER '{cluster_multiple_shard}' TO goteacher.visits_sc_local (
    `schoolYear` String,
    `sessionId` UUID,
    `tabId` UUID,
    `userId` UUID,
    `role` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `domain` String,
    `productId` String,
    `fullDomain` String,
    `url` String,
    `visit_start` DateTime,
    `visit_end` DateTime,
    `visit_timezone` String
) AS
SELECT
    cae.schoolYear AS schoolYear,
    cae.sessionId AS sessionId,
    cae.tabId AS tabId,
    cae.userId AS userId,
    cae.role AS role,
    cae.orgId AS orgId,
    cae.schoolId AS schoolId,
    cae.grade AS grade,
    cae.domain AS domain,
    cae.productId AS productId,
    cae.fullDomain AS fullDomain,
    cae.url AS url,
    min(cae.clientTimestamp) AS visit_start,
    max(cae.clientTimestamp) AS visit_end,
    any(cae.clientTimezone) AS visit_timezone
FROM
    goteacher.enriched_events_local AS cae
WHERE
    cae.orgId IS NOT NULL
GROUP BY
    schoolYear,
    sessionId,
    tabId,
    userId,
    role,
    orgId,
    schoolId,
    grade,
    domain,
    url,
    productId,
    fullDomain;