CREATE TABLE goteacher.weekly_domain_metrics_per_user_sc_local ON CLUSTER '{cluster_multiple_shard}' (
    `day` DateTime,
    `schoolYear` String,
    `domain` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID),
    `page_views_student` AggregateFunction(count, Int64),
    `unique_views_student` AggregateFunction(uniq, UUID),
    `page_views_teacher` AggregateFunction(count, Int64),
    `unique_views_teacher` AggregateFunction(uniq, UUID),
    `page_views_admin` AggregateFunction(count, Int64),
    `unique_views_admin` AggregateFunction(uniq, UUID)
) ENGINE = ReplicatedAggregatingMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/weekly_domain_metrics_per_user_sc_local',
    '{multiple_replica}'
) PARTITION BY (schoolYear, toYYYYMM(day), orgId)
ORDER BY
    (day, domain, orgId, schoolId, grade, userId) SETTINGS index_granularity = 8192;