CREATE MATERIALIZED VIEW goteacher.weekly_product_metrics_per_user_sc_page_views_mv ON CLUSTER '{cluster_multiple_shard}'
TO goteacher.weekly_product_metrics_per_user_sc_local (
    `day` DateTime,
    `schoolYear` String,
    `productId` String,    
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `userId` UUID,
    `page_views` AggregateFunction(count, Int64),
    `unique_views` AggregateFunction(uniq, UUID)
) AS
SELECT    
    toDateTime(toStartOfWeek(day)) AS day,
    schoolYear,
    productId,    
    orgId,
    schoolId,
    grade,
    userId,
    countState(page_views) as page_views,    
    uniqMergeState(unique_views) AS unique_views
FROM goteacher.daily_product_metrics_per_user_sc_local
GROUP BY
    day,
    schoolYear,
    productId,    
    orgId,
    schoolId,
    grade,
    userId;
