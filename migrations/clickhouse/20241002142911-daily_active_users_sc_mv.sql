CREATE MATERIALIZED VIEW goteacher.daily_active_users_sc_mv ON CLUSTER '{cluster_multiple_shard}' TO goteacher.daily_active_users_sc_local (
    `day` DateTime,
    `schoolYear` String,
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String,
    `domain` String,
    `productId` String,
    `fullDomain` String,
    `url` String,
    `active_users` AggregateFunction(uniq, UUID),
    `active_users_student` AggregateFunction(uniq, UUID),
    `active_users_teacher` AggregateFunction(uniq, UUID),
    `active_users_admin` AggregateFunction(uniq, UUID)
) AS
SELECT
    toStartOfDay(cae.clientTimestamp) AS day,
    cae.schoolYear AS schoolYear,
    cae.orgId AS orgId,
    cae.schoolId AS schoolId,
    cae.grade AS grade,
    cae.domain AS domain,
    cae.productId AS productId,
    cae.fullDomain AS fullDomain,
    cae.url AS url,
    uniqState(cae.userId) AS active_users,
    uniqStateIf(cae.userId, cae.role = 'student') AS active_users_student,
    uniqStateIf(cae.userId, cae.role = 'teacher') AS active_users_teacher,
    uniqStateIf(cae.userId, cae.role = 'administrator') AS active_users_admin
FROM
    goteacher.enriched_events_local AS cae
WHERE cae.orgId IS NOT NULL
GROUP BY
    day,
    schoolYear,
    orgId,
    schoolId,
    grade,
    domain,
    url,
    productId,
    fullDomain;