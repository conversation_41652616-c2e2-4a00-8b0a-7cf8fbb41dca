import { createClient } from '@clickhouse/client';
import * as fs from 'fs';
import * as path from 'path';
import { MigrationParams, Umzug } from 'umzug';

import { ClickHouseClient } from '@clickhouse/client';
import { UmzugStorage } from 'umzug';

export interface ClickHouseMigrationRow {
  id: string;
  name: string;
  created_at: Date;
}

class ClickHouseStorage implements UmzugStorage {
  private client: ClickHouseClient;
  private tableName: string;
  private databaseName: string;

  constructor(
    client: ClickHouseClient,
    tableName: string,
    databaseName?: string,
  ) {
    this.client = client;
    this.tableName = tableName;
    this.databaseName = databaseName;
  }

  public async init(): Promise<void> {
    console.log('Initializing ClickHouse storage...');

    const crt_tbl_query = `
      CREATE TABLE IF NOT EXISTS ${this.tableName} ON CLUSTER '{cluster_single_shard}' (
        id String,
        name String,
        created_at DateTime DEFAULT now()
      ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{uuid}/{single_shard}/${this.tableName}', '{single_replica}')
      ORDER BY (id);
    `;
    await this.client.query({ query: crt_tbl_query });
    console.log('ClickHouse storage initialized!');
  }

  async logMigration(params: MigrationParams<any>): Promise<void> {
    await this.client.insert({
      table: this.tableName,
      values: [{ id: params.path, name: params.name }],
      format: 'JSONEachRow',
    });
  }

  // Unlog a migration (when it's rolled back)
  async unlogMigration(params: MigrationParams<any>): Promise<void> {
    const query = `
      DELETE FROM ${this.tableName} WHERE id = '${params.path}';
    `;
    await this.client.query({
      query,
    });
  }

  async executed(): Promise<string[]> {
    const query = `
      SELECT name FROM ${this.tableName} ORDER BY created_at ASC
    `;
    const result = await (
      await this.client.query({
        query,
        format: 'JSON',
      })
    ).json<ClickHouseMigrationRow[]>();
    return result.data.map((row: any) => row.name);
  }
}

const clickhouse = createClient({
  host: process.env.CH_MIGRATIONS_HOST,
  database: process.env.CH_MIGRATIONS_DB,
  username: process.env.CH_MIGRATIONS_USER,
  password: process.env.CH_MIGRATIONS_PASSWORD,
});

const clickHouseStorage = new ClickHouseStorage(
  clickhouse,
  'migrations_umzug',
  process.env.CH_MIGRATIONS_DB,
);

const umzug = new Umzug({
  migrations: {
    glob: path.join(__dirname, 'clickhouse/*.sql'),
    resolve: ({ name, path }: MigrationParams<any>) => {
      return {
        name,
        up: async () => {
          const sql = fs.readFileSync(path, 'utf8');
          return await clickhouse.query({ query: sql });
        },
        down: async () => {},
      };
    },
  },
  storage: clickHouseStorage,
  logger: console,
});

clickHouseStorage
  .init()
  .then(() => {
    umzug
      .up()
      .then((migrations) => {
        if (!migrations.length) {
          console.log('No migrations to run');
        }

        migrations.forEach((migration) => {
          console.log('Migration', migration.name, 'ran successfully!');
        });
      })
      .catch((err) => {
        console.error('Failed to run migrations!');
        console.error(err);
        process.exit(1);
      });
  })
  .catch((err) => {
    console.error('Failed to initialize storage!');
    console.error(err);
    process.exit(1);
  });
