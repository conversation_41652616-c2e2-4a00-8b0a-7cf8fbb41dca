import fs from 'fs';
import path from 'path';
import { Sequelize } from 'sequelize';
import { SequelizeStorage, Umzug } from 'umzug';

const sequelize = new Sequelize({
  host: process.env.DATABASE_HOST,
  port: parseInt(process.env.DATABASE_PORT),
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  dialect: 'postgres',
  dialectOptions: {
    useUTC: true,
    dateStrings: true,
  },
  timezone: '+00:00',
  logging: false,
});

const umzug = new Umzug({
  migrations: {
    glob: path.join(__dirname, 'sequelize/*.sql'),
    resolve: ({ name, path }) => {
      return {
        name,
        up: async () => {
          const sql = fs.readFileSync(path, 'utf8');
          return sequelize.query(sql);
        },
        down: async () => {},
      };
    },
  },
  storage: new SequelizeStorage({ sequelize }),
  context: sequelize.getQueryInterface(),
  logger: console,
});

umzug
  .up()
  .then((migrations) => {
    if (!migrations.length) {
      console.log('No migrations to run');
    }

    migrations.forEach((migration) => {
      console.log('Migration', migration.name, 'ran successfully!');
    });
  })
  .catch((err) => {
    console.error('Failed to run migrations!');
    console.error(err);
    process.exit(1);
  });
