DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_type WHERE typname = 'SchoolStatus'
    ) THEN
        CREATE TYPE "SchoolStatus" AS ENUM (
            'APPROVED',
            'PENDING',
            'REJECTED'
        );
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS school (
	id text NOT NULL,
	"name" text NOT NULL,
	"displayName" text NOT NULL,
	status "SchoolStatus" DEFAULT 'PENDING'::"SchoolStatus" NOT NULL,
	"organisationId" text NULL,
	"addressId" text NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	CONSTRAINT school_pkey PRIMARY KEY (id)
);



DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'school_addressId_fkey'
    ) THEN
        ALTER TABLE school
        ADD CONSTRAINT "school_addressId_fkey"
        FOREIGN KEY ("addressId")
        REFERENCES address(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'school_organisationId_fkey'
    ) THEN
        ALTER TABLE school
        ADD CONSTRAINT "school_organisationId_fkey"
        FOREIGN KEY ("organisationId")
        REFERENCES organisation(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
    END IF;
END $$;