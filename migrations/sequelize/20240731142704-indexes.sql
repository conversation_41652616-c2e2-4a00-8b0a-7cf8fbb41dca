DO $$
BEGIN
   -- Create index for school(organisationId)
   IF NOT EXISTS (SELECT 1 FROM pg_class c
                  JOIN pg_namespace n ON n.oid = c.relnamespace
                  WHERE c.relname = 'idx_school_organisationid') THEN
       EXECUTE 'CREATE INDEX idx_school_organisationId ON school("organisationId")';
   END IF;

   -- Create index for userSchool(userId)
   IF NOT EXISTS (SELECT 1 FROM pg_class c
                  JOIN pg_namespace n ON n.oid = c.relnamespace
                  WHERE c.relname = 'idx_userschool_userid'
                  ) THEN
       EXECUTE 'CREATE INDEX idx_userSchool_userId ON "userSchool"("userId")';
   END IF;

   -- Create index for userSchool(schoolId)
   IF NOT EXISTS (SELECT 1 FROM pg_class c
                  JOIN pg_namespace n ON n.oid = c.relnamespace
                  WHERE c.relname = 'idx_userschool_schoolid'
                  ) THEN
       EXECUTE 'CREATE INDEX idx_userSchool_schoolId ON "userSchool"("schoolId")';
   END IF;

   -- Create composite index for userSchool(userId, schoolId)
   IF NOT EXISTS (SELECT 1 FROM pg_class c
                  JOIN pg_namespace n ON n.oid = c.relnamespace
                  WHERE c.relname = 'idx_userschool_userid_schoolid'
                  ) THEN
       EXECUTE 'CREATE INDEX idx_userSchool_userId_schoolId ON "userSchool"("userId", "schoolId")';
   END IF;
END $$;