
CREATE TABLE IF NOT EXISTS users (
	id text NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	email text NOT NULL,
	"firstName" text NULL,
	"lastName" text NULL,
	picture text NULL,
	"isRegistrationCompleted" bool DEFAULT false NULL,
	"isOnboardingCompleted" bool DEFAULT false NULL,
	"role" text DEFAULT ''::text NULL,
	"Metadata" jsonb NULL,
	"Synced" bool DEFAULT false NULL,
	CONSTRAINT users_pkey PRIMARY KEY (id)
);

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'users_email_key' AND tablename = 'users'
    ) THEN
        EXECUTE 'CREATE UNIQUE INDEX IF NOT EXISTS users_email_key ON public.users USING btree (email)';
    END IF;
END $$;