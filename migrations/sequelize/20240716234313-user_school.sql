DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_type WHERE typname = 'UserSchoolStatus'
    ) THEN
        CREATE TYPE "UserSchoolStatus" AS ENUM (
            'APPROVED',
            'PENDING',
            'REJECTED'
        );
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS "userSchool" (
	id text NOT NULL,
	status "UserSchoolStatus" DEFAULT 'PENDING'::"UserSchoolStatus" NOT NULL,
	"userId" text NOT NULL,
	"schoolId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	CONSTRAINT "userSchool_pkey" PRIMARY KEY (id)
);

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'userSchool_schoolId_fkey'
    ) THEN
        ALTER TABLE "userSchool"
        ADD CONSTRAINT "userSchool_schoolId_fkey"
        FOREIGN KEY ("schoolId")
        REFERENCES school(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'userSchool_userId_fkey'
    ) THEN
        ALTER TABLE "userSchool"
        ADD CONSTRAINT "userSchool_userId_fkey"
        FOREIGN KEY ("userId")
        REFERENCES users(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
    END IF;
END $$;