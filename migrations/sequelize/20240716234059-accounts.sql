CREATE TABLE IF NOT EXISTS accounts (
	id text NOT NULL,
	"userId" text NOT NULL,
	platform text NOT NULL,
	credentials jsonb NOT NULL,
	"externalId" text NULL,
	"createdAt" timestamptz(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamptz(6) NOT NULL,
	CONSTRAINT accounts_pkey PRIMARY KEY (id)
);

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'accounts_userId_fkey'
    ) THEN
        ALTER TABLE accounts
        ADD CONSTRAINT "accounts_userId_fkey"
        FOREIGN KEY ("userId")
        REFERENCES users(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
    END IF;
END $$;