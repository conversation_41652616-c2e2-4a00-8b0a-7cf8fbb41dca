CREATE TABLE IF NOT EXISTS extension_config (
	id uuid NOT NULL,
	"orgId" varchar(255) NOT NULL,
	"config" jsonb NOT NULL,
	"description" varchar(255) NULL,
	"isActive" boolean DEFAULT true NOT NULL,
	"createdAt" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT extension_config_pkey PRIMARY KEY (id)
);

-- Create index for efficient lookups by orgId
CREATE INDEX IF NOT EXISTS extension_config_org_id_idx ON extension_config("orgId");

-- Create index for active configurations
CREATE INDEX IF NOT EXISTS extension_config_active_idx ON extension_config("isActive") WHERE "isActive" = true;

-- Create unique constraint to ensure only one active config per organization
CREATE UNIQUE INDEX IF NOT EXISTS extension_config_org_active_unique 
ON extension_config("orgId") WHERE "isActive" = true;
