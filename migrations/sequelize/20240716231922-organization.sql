DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_type WHERE typname = 'OrganisationStatus'
    ) THEN
        CREATE TYPE "OrganisationStatus" AS ENUM (
            'APPROVED',
            'PENDING',
            'REJECTED'
        );
    END IF;
END $$;


CREATE TABLE IF NOT EXISTS organisation (
    id text NOT NULL,
    "name" text NOT NULL,
    "displayName" text NOT NULL,
    domains text NOT NULL,
    status "OrganisationStatus" DEFAULT 'PENDING' :: "OrganisationStatus" NOT NULL,
    deleted bool DEFAULT false NOT NULL,
    "createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) NOT NULL,
    CONSTRAINT organisation_pkey PRIMARY KEY (id)
);

