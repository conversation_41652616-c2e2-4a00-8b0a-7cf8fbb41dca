CREATE TABLE IF NOT EXISTS address (
	id text NOT NULL,
	"addressLine1" text NULL,
	"addressLine2" text NULL,
	"postalCode" text NULL,
	city text NULL,
	"countryId" text NOT NULL,
	"stateId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	CONSTRAINT address_pkey PRIMARY KEY (id)
);

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'address_countryId_fkey'
    ) THEN
        ALTER TABLE address
        ADD CONSTRAINT "address_countryId_fkey"
        FOREIGN KEY ("countryId")
        REFERENCES country(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'address_stateId_fkey'
    ) THEN
        ALTER TABLE address
        ADD CONSTRAINT "address_stateId_fkey"
        FOREIGN KEY ("stateId")
        REFERENCES state(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
    END IF;
END $$;