DROP TABLE IF EXISTS blacklist;CREATE TABLE IF NOT EXISTS blacklist (
    id uuid NOT NULL,
    "domain" varchar(255) NOT NULL,
    "url" varchar(255) NULL,
    "urlPattern" varchar(255) NULL,
    "createdAt" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "orgId" varchar(255) DEFAULT '*' :: character varying NOT NULL,
    CONSTRAINT blacklist_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_blacklist_domain_orgId ON blacklist("domain", "orgId");