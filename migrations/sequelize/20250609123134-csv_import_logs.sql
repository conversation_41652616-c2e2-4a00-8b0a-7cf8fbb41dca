-- Migration: Create csv_import_logs table
-- Description: Track CSV imports per organization and school year for enhanced decommissioning logic

-- Up migration
CREATE TABLE IF NOT EXISTS csv_import_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id TEXT NOT NULL,
    school_year VARCHAR(20) NOT NULL,
    import_type VARCHAR(20) NOT NULL,
    imported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    file_name VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_csv_import_logs_organization 
        FOREIGN KEY (organization_id) 
        REFERENCES organisation(id) 
        ON DELETE CASCADE,
    
    -- constraints
    CONSTRAINT chk_csv_import_logs_import_type 
        CHECK (import_type IN ('student', 'staff')),
    
    CONSTRAINT chk_csv_import_logs_school_year_format 
        CHECK (school_year ~ '^\d{4}_\d{4}$')
);

-- indexes
CREATE INDEX IF NOT EXISTS idx_csv_import_logs_org_year_type 
    ON csv_import_logs(organization_id, school_year, import_type);

CREATE INDEX IF NOT EXISTS idx_csv_import_logs_imported_at 
    ON csv_import_logs(imported_at);

CREATE INDEX IF NOT EXISTS idx_csv_import_logs_organization_id 
    ON csv_import_logs(organization_id);