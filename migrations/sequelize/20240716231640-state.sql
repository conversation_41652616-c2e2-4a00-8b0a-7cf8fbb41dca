CREATE TABLE IF NOT EXISTS state (
	id text NOT NULL,
	"stateName" text NOT NULL,
	"stateInitials" text NOT NULL,
	"countryId" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	CONSTRAINT state_pkey PRIMARY KEY (id)
);

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'state_countryId_fkey'
    ) THEN
        ALTER TABLE state
        ADD CONSTRAINT "state_countryId_fkey"
        FOREIGN KEY ("countryId")
        REFERENCES country(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE;
    END IF;
END $$;