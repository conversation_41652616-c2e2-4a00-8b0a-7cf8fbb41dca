{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
    {
      "name": "API Docker: Attach to Node",
      "type": "node",
      "request": "attach",
      "port": 9229,
      "address": "localhost",
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/usr/src/app",
      "restart": true,
      "skipFiles": [
          "<node_internals>/**"
      ]
    },
    {
      "name": "SEEDS Docker: Attach to Node",
      "type": "node",
      "request": "attach",
      "port": 9230,
      "address": "localhost",
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/usr/src/app",
      "restart": true,
      "skipFiles": [
          "<node_internals>/**"
      ]
    },
    {
      "name": "EXPORT Docker: Attach to Node",
      "type": "node",
      "request": "attach",
      "port": 9231,
      "address": "localhost",
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/usr/src/app",
      "restart": true,
      "skipFiles": [
          "<node_internals>/**"
      ]
    },
    {
      "name": "INGESTION Docker: Attach to Node",
      "type": "node",
      "request": "attach",
      "port": 9232,
      "address": "localhost",
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/usr/src/app",
      "restart": true,
      "skipFiles": [
          "<node_internals>/**"
      ]
    },
    {
      "name": "ENRICHMENT Docker: Attach to Node",
      "type": "node",
      "request": "attach",
      "port": 9237,
      "address": "localhost",
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/usr/src/app",
      "restart": true,
      "skipFiles": [
          "<node_internals>/**"
      ]
    },
  ]
}