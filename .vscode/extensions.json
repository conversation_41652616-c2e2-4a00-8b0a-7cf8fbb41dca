{"recommendations": ["iliazeus.vscode-ansi", "ms-vscode-remote.remote-containers", "ms-azuretools.vscode-docker", "mikestead.dotenv", "dbaeumer.vscode-eslint", "ms-vscode.vscode-js-profile-flame", "waderyan.gitblame", "donjayamanne.git-extension-pack", "donjayamanne.githistory", "github.vscode-github-actions", "me-dutour-mathieu.vscode-github-actions", "codezombiech.gitignore", "quentinguidee.gitignore-ultimate", "eamodio.gitlens", "ms-vscode.vscode-typescript-next", "ms-vscode.js-debug-nightly", "orta.vscode-jest", "ziyasal.vscode-open-in-github", "csstools.postcss", "elagil.pre-commit-helper", "esbenp.prettier-vscode", "rvest.vs-code-prettier-eslint", "prisma.prisma", "mechatroner.rainbow-csv", "si<PERSON><PERSON>-s.vscode-scss-formatter", "mrmlnc.vscode-scss", "timonwong.shellcheck", "cymonk.sql-formatter", "supermaven.supermaven", "jock.svg", "tchoupinax.tilt", "tilt-dev.tiltfile", "redhat.vscode-yaml"]}