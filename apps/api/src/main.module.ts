import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APIAppModule } from 'apps/api/src/app/app.module';

import { HttpContextInterceptor } from '@goteacher/app/common/logging';
import { APP_INTERCEPTOR } from '@nestjs/core';
import aws from './config/aws.config';
import clickhouse from './config/clickhouse.config';
import core from './config/core.config';
import email from './config/email.config';
import google from './config/google.config';
import jwt from './config/jwt.config';
import kafka from './config/kafka.config';
import mongo from './config/mongo.config';
import opensearch from './config/opensearch.config';
import postgres from './config/postgres.config';
import redis from './config/redis.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        aws,
        clickhouse,
        core,
        email,
        google,
        jwt,
        kafka,
        mongo,
        postgres,
        redis,
        opensearch,
      ],
    }),
    APIAppModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpContextInterceptor,
    },
  ],
})
export class APIMainModule {}
