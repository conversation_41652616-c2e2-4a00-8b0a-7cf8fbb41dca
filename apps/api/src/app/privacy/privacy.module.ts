import {
  Get<PERSON>g<PERSON>ment<PERSON><PERSON><PERSON>,
  GetAgreements<PERSON>andler,
  GetAllAgreementsHandler,
} from '@goteacher/app/privacy';
import { GetCompaniesHandler } from '@goteacher/app/privacy/query/get-companies/handler';
import { GetSoftwareHandler } from '@goteacher/app/privacy/query/get-software/handler';
import { RequestConnectionHandler } from '@goteacher/app/privacy/query/request-connection/handler';
import { Module } from '@nestjs/common';
import { PrivacyController } from '../../api/privacy/privacy.controller';

@Module({
  imports: [],
  controllers: [PrivacyController],
  providers: [
    GetAgreementsHandler,
    GetCompaniesHandler,
    GetSoftwareHandler,
    GetAllAgreementsHandler,
    GetAgreementHandler,
    RequestConnectionHandler],
  exports: [],
})
export class APIPrivacyModule { }
