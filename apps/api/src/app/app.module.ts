import { CommonModule } from '@goteacher/app/common/common.module';
import { Module } from '@nestjs/common';
import { APIAnalyticModule } from 'apps/api/src/app/analytic/analytic.module';
import { APIAuthModule } from 'apps/api/src/app/auth/auth.module';
import { APICoreModule } from 'apps/api/src/app/core/core.module';
import { APIFinancialModule } from 'apps/api/src/app/financial/financial.module';
import { APIMetadataModule } from 'apps/api/src/app/metadata/metadata.module';
import { APIOrganizationModule } from 'apps/api/src/app/organization/organization.module';
import { APIPrivacyModule } from 'apps/api/src/app/privacy/privacy.module';
import { SchedulerModule } from 'apps/api/src/app/scheduler/scheduler.module';
import { APISchoolModule } from 'apps/api/src/app/school/school.module';
import { APIOAuthAppsModule } from 'apps/api/src/app/oauth-apps/oauth-apps.module';

@Module({
  imports: [
    CommonModule,
    APICoreModule,
    APIAuthModule,
    APISchoolModule,
    APIAnalyticModule,
    APIFinancialModule,
    APIMetadataModule,
    APIOrganizationModule,
    APIPrivacyModule,
    SchedulerModule,
    APIOAuthAppsModule,
  ],
  exports: [
    CommonModule,
    APICoreModule,
    APIAuthModule,
    APISchoolModule,
    APIAnalyticModule,
    APIFinancialModule,
    APIMetadataModule,
    APIOrganizationModule,
    APIPrivacyModule,
    SchedulerModule,
    APIOAuthAppsModule,
  ],
})
export class APIAppModule {}
