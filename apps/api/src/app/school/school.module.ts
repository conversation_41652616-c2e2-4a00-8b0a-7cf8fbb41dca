import { Join<PERSON><PERSON>olCommandHandler } from '@goteacher/app/school';
import { GetSchoolsHandler } from '@goteacher/app/school/query';
import { Module } from '@nestjs/common';
import { SchoolController } from 'apps/api/src/api/school/school.controller';

@Module({
  imports: [],
  controllers: [SchoolController],
  providers: [GetSchoolsHandler, JoinSchoolCommandHandler],
  exports: [],
})
export class APISchoolModule {}
