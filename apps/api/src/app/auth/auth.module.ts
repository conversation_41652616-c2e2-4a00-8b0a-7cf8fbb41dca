import { Global, Module } from '@nestjs/common';
import {
  GoogleAuthorizationHandler,
  GoogleOAuthModule,
  JWTService,
} from '@goteacher/app/auth';
import {
  GoogleExchangeCode<PERSON>andler,
  GoogleRefreshCommand<PERSON>andler,
  Refresh<PERSON>okenHandler,
  RegisterUserCommandHandler,
} from '@goteacher/app/auth/command';

import { AppleLoginHandler } from '@goteacher/app/auth/command/apple/login/handler';
import { AuthMeHandler } from '@goteacher/app/auth/query/goteacher';
import { ConfigService } from '@nestjs/config';
import { GoogleExtensionTokenHandler } from '@goteacher/app/auth/command/google/extension-token/handler';
import { JwtModule } from '@nestjs/jwt';
import { OAuthController } from 'apps/api/src/api/oauth/oauth.controller';
import { OAuthGoogleController } from 'apps/api/src/api/oauth/oauth.google.controller';
import { PassportModule } from '@nestjs/passport';

@Global()
@Module({
  imports: [
    JwtModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          secret: configService.get<string>('JWT_SECRET'),
          signOptions: {
            expiresIn: configService.get<string>('JWT_EXPIRES_IN'),
          },
        };
      },
      inject: [ConfigService],
    }),
    PassportModule.register({ session: false }),
    GoogleOAuthModule.forRoot({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        clientId: configService.get<string>('GOOGLE_OAUTH_CLIENT_ID'),
        clientSecret: configService.get<string>('GOOGLE_OAUTH_CLIENT_SECRET'),
        redirectUri: configService.get<string>('GOOGLE_OAUTH_REDIRECT_URI'),
      }),
    }),
  ],
  providers: [
    JWTService,
    GoogleAuthorizationHandler,
    GoogleExchangeCodeHandler,
    GoogleRefreshCommandHandler,
    GoogleExtensionTokenHandler,
    AuthMeHandler,
    RefreshTokenHandler,
    RegisterUserCommandHandler,
    AppleLoginHandler,
  ],
  exports: [JWTService],
  controllers: [OAuthController, OAuthGoogleController],
})
export class APIAuthModule {}
