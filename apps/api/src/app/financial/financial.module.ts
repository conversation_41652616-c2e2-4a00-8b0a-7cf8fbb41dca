import { EnrichmentService } from '@goteacher/app/analytics';
import {
  CreateContract<PERSON>and<PERSON>,
  GetContractQuery<PERSON>and<PERSON>,
  GetGradesTopSpendQuery<PERSON>and<PERSON>,
  GetLicensesQuery<PERSON>andler,
  GetSchoolsTopSpendQuery<PERSON>and<PERSON>,
  GetSummaryQueryHandler,
} from '@goteacher/app/financial';
import { DeleteContractHandler } from '@goteacher/app/financial/command/delete-contract';
import { UpdateContractCommandHandler } from '@goteacher/app/financial/command/update-contract/handler';
import { GetAudienceQueryHandler } from '@goteacher/app/financial/query/calculate-audience';
import { GetContractUsersQueryHandler } from '@goteacher/app/financial/query/get-contract-users';
import { GetContractUsersCsvQueryHandler } from '@goteacher/app/financial/query/get-contract-users-csv';
import { ListContractQueryHandler } from '@goteacher/app/financial/query/list-contract';
import { FinancialService } from '@goteacher/app/financial/service/financial.service';
import { Module } from '@nestjs/common';
import { FinancialContractController } from 'apps/api/src/api/financial/contract/contract.controller';
import { FinancialCostController } from 'apps/api/src/api/financial/cost/cost.controller';

@Module({
  imports: [],
  controllers: [FinancialContractController, FinancialCostController],
  providers: [
    FinancialService,
    EnrichmentService,

    CreateContractHandler,
    DeleteContractHandler,
    UpdateContractCommandHandler,
    GetContractQueryHandler,
    ListContractQueryHandler,

    GetAudienceQueryHandler,
    GetLicensesQueryHandler,
    GetSummaryQueryHandler,
    GetSchoolsTopSpendQueryHandler,
    GetGradesTopSpendQueryHandler,

    GetContractUsersQueryHandler,
    GetContractUsersCsvQueryHandler,
  ],
})
export class APIFinancialModule { }
