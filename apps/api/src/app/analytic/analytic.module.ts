import {
  EnrichmentService,
  GetActiveInactiveStudentsQueryHandler,
  GetActiveUsersBucketQueryHandler,
  GetFilterableGradesHandler,
  GetFilterableSchoolsHandler,
  GetFilterableStudentsHandler,
  GetFilterableSubjectsHandler,
  GetInOutClassQueryHandler,
  GetStaticDataQueryHandler,
  GetTopProductsQueryHandler,
  GetTopUsersQueryHandler,
  GetUserByBucketQueryHandler,
  GetUserUsageCategoriesHandler,
} from '@goteacher/app/analytics';

import { GetTopDomainsQueryHandler } from '@goteacher/app/analytics/query/data/get-analytics-list-domains/handler';
import { GetDataQueryHandler } from '@goteacher/app/analytics/query/data/get-data/handler';
import { GetDomainDataQueryHandler } from '@goteacher/app/analytics/query/data/get-domain-data/handler';
import { GetGradesAnalyticsQueryHandler } from '@goteacher/app/analytics/query/data/get-grades-analytics/handler';
import { GetSessionDurationsQueryHandler } from '@goteacher/app/analytics/query/data/get-sessions-duration/handler';
import { GetTaggedYoutubeDataQueryHandler } from '@goteacher/app/analytics/query/data/get-tagged-youtube-data';
import { GetTopGradesByDomainQueryHandler } from '@goteacher/app/analytics/query/data/get-top-grades-by-domain/handler';
import { GetTopSchoolsByDomainQueryHandler } from '@goteacher/app/analytics/query/data/get-top-schools-by-domain/handler';
import { GetTopUrlsByDomainQueryHandler } from '@goteacher/app/analytics/query/data/get-top-urls-by-domain/handler';
import { GetTopUsersCsvQueryHandler } from '@goteacher/app/analytics/query/data/get-top-users-csv';
import { GetUserDataHandler } from '@goteacher/app/analytics/query/data/get-user-data';
import { GetUserSessionsHandler } from '@goteacher/app/analytics/query/data/get-user-sessions/handler';
import { GetUserTableQueryHandler } from '@goteacher/app/analytics/query/data/get-user-table';
import { GetUserScreenTimeHandler } from '@goteacher/app/analytics/query/data/get-users-screen-time';
import { Module } from '@nestjs/common';
import { AnalyticDataController } from 'apps/api/src/api/analytic/data/analytic.data.controller';
import { AnalyticFiltersController } from 'apps/api/src/api/analytic/filter/analytic.filter.controller';

@Module({
  imports: [],
  controllers: [AnalyticFiltersController, AnalyticDataController],
  providers: [
    EnrichmentService,

    GetFilterableGradesHandler,
    GetFilterableSchoolsHandler,
    GetFilterableSubjectsHandler,
    GetFilterableStudentsHandler,

    GetDataQueryHandler,
    GetTopUsersQueryHandler,
    GetUserByBucketQueryHandler,
    GetActiveUsersBucketQueryHandler,
    GetSessionDurationsQueryHandler,
    GetInOutClassQueryHandler,
    GetActiveInactiveStudentsQueryHandler,
    GetStaticDataQueryHandler,
    GetTopDomainsQueryHandler,
    GetDomainDataQueryHandler,
    GetTopSchoolsByDomainQueryHandler,
    GetTopGradesByDomainQueryHandler,
    GetTopUrlsByDomainQueryHandler,
    GetTaggedYoutubeDataQueryHandler,
    GetTopProductsQueryHandler,
    GetTopUsersCsvQueryHandler,
    GetUserTableQueryHandler,
    GetGradesAnalyticsQueryHandler,
    GetUserScreenTimeHandler,
    GetUserDataHandler,
    GetUserUsageCategoriesHandler,
    GetUserSessionsHandler,
  ],
})
export class APIAnalyticModule { }
