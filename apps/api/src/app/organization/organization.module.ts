import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { OrganizationController } from '../../api/organization/organization.controller';
import { APICoreModule } from '../core/core.module';
import { OrganizationModule } from '@goteacher/app/organization/organization.module';

@Module({
  imports: [
    CqrsModule,
    APICoreModule,
    OrganizationModule,
  ],
  controllers: [OrganizationController],
})
export class APIOrganizationModule {}