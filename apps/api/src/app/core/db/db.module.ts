import { MongooseModelModule } from '@goteacher/app/models/mongo/mongoose.model.module';
import { SequelizeModelModule } from '@goteacher/app/models/sequelize/sequelize.model.module';
import { ClickhouseModule, PostgresSequelizeModule } from '@goteacher/infra/db';
import { MongoModule } from '@goteacher/infra/db/mongo.module';
import { Global, Module } from '@nestjs/common';

@Global()
@Module({
  imports: [
    PostgresSequelizeModule,
    SequelizeModelModule,
    MongoModule,
    MongooseModelModule,
    ClickhouseModule,
  ],
  exports: [
    PostgresSequelizeModule,
    SequelizeModelModule,
    MongoModule,
    MongooseModelModule,
    ClickhouseModule,
  ],
})
export class APIDBModule {}
