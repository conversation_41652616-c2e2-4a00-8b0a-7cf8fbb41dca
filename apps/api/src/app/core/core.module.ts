import { Global, Module } from '@nestjs/common';
import { APIAWSModule } from 'apps/api/src/app/core/aws/aws.module';
import { APICacheModule } from 'apps/api/src/app/core/cache/cache.module';
import { APIDBModule } from 'apps/api/src/app/core/db/db.module';
import { APIStorageModule } from 'apps/api/src/app/core/storage/storage.module';

@Global()
@Module({
  imports: [APIAWSModule, APICacheModule, APIDBModule, APIStorageModule],
  exports: [APIAWSModule, APICacheModule, APIDBModule, APIStorageModule],
})
export class APICoreModule {}
