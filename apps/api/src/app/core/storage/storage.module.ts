import { AWSS3Service } from '@goteacher/infra/storage/s3.service';
import { GCSService } from '@goteacher/infra/storage/gcs.service';
import { IStorageService } from '@goteacher/infra/storage/storage.service';
import { Global, Module } from '@nestjs/common';

@Global()
@Module({
  providers: [
    {
      provide: IStorageService,
      useClass: AWSS3Service,
    },
    GCSService,
  ],
  exports: [IStorageService, GCSService],
})
export class APIStorageModule {}
