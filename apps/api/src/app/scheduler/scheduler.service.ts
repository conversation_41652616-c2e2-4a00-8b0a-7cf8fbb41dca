import { GoogleOAuthStrategy } from '@goteacher/app/auth/oauth';
import { OAuthApp, OAuthAppDocument } from '@goteacher/app/models/mongo/oauth-app.model';
import { OAuthSyncMetadata, OAuthSyncMetadataDocument } from '@goteacher/app/models/mongo/oauth-sync-metadata.model';
import { Account } from '@goteacher/app/models/sequelize/account.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { DomainResolutionService } from '@goteacher/app/oauth-apps/service/domain-resolution.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel as InjectModelMongoose } from '@nestjs/mongoose';
import { Cron } from '@nestjs/schedule';
import { InjectModel as InjectModelSequelize } from '@nestjs/sequelize';
import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import { Model } from 'mongoose';
import { Op, QueryTypes } from 'sequelize';

@Injectable()
export class SchedulerService {
  constructor(
    @InjectModelSequelize(Account) private accountModel: typeof Account,
    @InjectModelMongoose(OAuthApp.name) private oauthAppModel: Model<OAuthAppDocument>,
    @InjectModelMongoose(OAuthSyncMetadata.name) private oauthSyncMetadataModel: Model<OAuthSyncMetadataDocument>,
    private readonly googleOAuthStrategy: GoogleOAuthStrategy,
    private readonly configService: ConfigService,
    private readonly domainResolutionService: DomainResolutionService,
  ) {}


  @Cron('0 */15 * * * *') // Runs every 15 minutes
  async handleOauthAccessedApps() {
    const timestamp = new Date().toISOString();
    console.log(`[CRON] [${timestamp}] =====================================`);
    console.log(`[CRON] [${timestamp}] Starting OAuth Apps Sync Job`);
    console.log(`[CRON] [${timestamp}] Sync Strategy: First sync fetches 90 days, subsequent syncs fetch 24 hours`);
    console.log(`[CRON] [${timestamp}] =====================================`);
    
    try {
      const accounts = await this.accountModel.findAll({
        include: [
          {
            model: User,
            where: {
              email: '<EMAIL>'
              // deleted: false,
            //   role: 'administrator',
            //   email: {
            //     [Op.notLike]: '%@goteacher.com'
            //   }
            // }
          }
        ]
      });
      console.log(`[CRON] [${timestamp}] Found ${accounts.length} admin accounts to process`);

      for (const account of accounts) {
        await this.processAccount(account);
      }

      console.log(`[CRON] [${timestamp}] =====================================`);
      console.log(`[CRON] [${timestamp}] OAuth Apps Sync Job Completed`);
      console.log(`[CRON] [${timestamp}] Processed ${accounts.length} accounts`);
      console.log(`[CRON] [${timestamp}] =====================================`);
    } catch (error) {
      console.error(`[CRON] [${timestamp}] Fatal error in cron job:`, error.message);
    }
  }

  private async processAccount(account: Account) {
    const timestamp = new Date().toISOString();
    console.log(`[CRON] [${timestamp}] Processing account: ${account.id}`);
    
    let organisationId: string | null = null;

    try {
      const credentials = account.credentials;

      if (!credentials || !credentials.access_token) {
        console.log(`[CRON] [${timestamp}] Account ${account.id}: No access token found - Skipping`);
        return;
      }

      const requiredScope = 'https://www.googleapis.com/auth/admin.reports.audit.readonly';
      const hasReportsScope = credentials.scope && credentials.scope.includes(requiredScope);
      
      if (!hasReportsScope) {
        console.log(`[CRON] [${timestamp}] Account ${account.id}: Missing required admin reports scope`);
        console.log(`[CRON] [${timestamp}] Current scopes: ${credentials.scope || 'none'}`);
        console.log(`[CRON] [${timestamp}] Required scope: ${requiredScope}`);
        console.log(`[CRON] [${timestamp}] User needs to re-authenticate with admin privileges`);
        return;
      }

      // Get organization ID early to check sync metadata
      organisationId = await this.getOrganisationIdForAccount(account.id, timestamp);
      if (!organisationId) {
        console.log(`[CRON] [${timestamp}] No organisation found for account ${account.id} - skipping`);
        return;
      }

      console.log(`[CRON] [${timestamp}] Account ${account.id}: Refreshing token before API calls...`);
      
      const refreshedCredentials = await this.refreshAccountToken(account);
      if (!refreshedCredentials) {
        console.log(`[CRON] [${timestamp}] Account ${account.id}: Token refresh failed - Skipping`);
        return;
      }
      
      credentials.access_token = refreshedCredentials.access_token;
      credentials.token_type = refreshedCredentials.token_type;
      credentials.expiry_date = refreshedCredentials.expiry_date;
      console.log(`[CRON] [${timestamp}] Account ${account.id}: Token refreshed successfully`);

      const oauth2Client = new OAuth2Client(
        this.configService.get<string>('GOOGLE_OAUTH_CLIENT_ID'),
        this.configService.get<string>('GOOGLE_OAUTH_CLIENT_SECRET'),
        this.configService.get<string>('GOOGLE_OAUTH_REDIRECT_URI')
      );
      
      oauth2Client.setCredentials({
        access_token: credentials.access_token,
        refresh_token: credentials.refresh_token,
        token_type: credentials.token_type,
        expiry_date: credentials.expiry_date
      });

      const reports = google.admin({ version: 'reports_v1', auth: oauth2Client });

      // Check sync metadata to determine time range
      const syncMetadata = await this.oauthSyncMetadataModel.findOne({ organisationId }).exec();
      let startTime: string;
      let endTime: string = new Date().toISOString();
      let isFirstSync = true;

      if (syncMetadata && syncMetadata.syncStatus === 'success') {
        // Subsequent sync - fetch only last 24 hours
        isFirstSync = false;
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
        console.log(`[CRON] [${timestamp}] Organization ${organisationId}: Incremental sync - fetching last 24 hours`);
        console.log(`[CRON] [${timestamp}] Last successful sync: ${syncMetadata.lastSyncTimestamp}`);
      } else {
        // First sync or failed previous sync - fetch last 90 days
        startTime = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString();
        console.log(`[CRON] [${timestamp}] Organization ${organisationId}: Initial sync - fetching last 90 days`);
      }

      console.log(`[CRON] [${timestamp}] Time range: ${startTime} to ${endTime}`);
      console.log(`[CRON] [${timestamp}] Starting paginated Reports API calls with batches of 1000`);
      
      const activities = [];
      let pageToken = null;
      let pageCount = 0;
      const maxResults = 1000; // Max batch size
      const loginScope = 'openid'

      do {
        const params = {
          userKey: 'all', // 'all' gets domain-wide data
          applicationName: 'token',
          eventName: 'authorize',
          filters: `scope==${loginScope}`,
          maxResults,
          startTime,
          endTime,
          ...(pageToken && { pageToken })
        };
        
        console.log(`[CRON] [${timestamp}] Making Reports API call (page ${pageCount + 1}) with params:`, params);
        
        const response = await reports.activities.list(params);
        
        const pageActivities = response.data.items || [];
        activities.push(...pageActivities);
        
        pageToken = response.data.nextPageToken;
        pageCount++;
        
        console.log(`[CRON] [${timestamp}] Page ${pageCount}: Found ${pageActivities.length} OAuth authorization events`);
        
        // Add a small delay between requests to avoid rate limiting
        if (pageToken) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
      } while (pageToken);
      
      console.log(`[CRON] [${timestamp}] Completed pagination: ${pageCount} pages, ${activities.length} total OAuth authorization events`);
      
      if (activities.length === 0) {
        console.log(`[CRON] [${timestamp}] No OAuth authorization events found in the specified time range`);
        console.log(`[CRON] [${timestamp}] This is ${isFirstSync ? 'normal for organizations with no OAuth apps' : 'expected if no new authorizations occurred in the last 24 hours'}`);
      }

      const appMap = new Map();
      
      activities.forEach(activity => {
        if (activity.events) {
          activity.events.forEach(event => {
            if (event.parameters) {
              const appName = event.parameters.find(p => p.name === 'app_name')?.value;
              const clientId = event.parameters.find(p => p.name === 'client_id')?.value;
              const scopes = event.parameters.find(p => p.name === 'scope')?.multiValue || [];
              
              if (clientId) {
                if (!appMap.has(clientId)) {
                  appMap.set(clientId, {
                    appName: appName || 'Unknown App',
                    clientId: clientId,
                    users: new Set(),
                    scopes: new Set(),
                    lastAccessed: activity.id?.time,
                  });
                }
                
                const appData = appMap.get(clientId);
                appData.users.add(activity.actor?.email);
                scopes.forEach(scope => appData.scopes.add(scope));
                
                if (activity.id?.time && (!appData.lastAccessed || activity.id.time > appData.lastAccessed)) {
                  appData.lastAccessed = activity.id.time;
                }
              }
            }
          });
        }
      });

      console.log(`[CRON] [${timestamp}] DOMAIN-WIDE OAUTH APPS SUMMARY:`);
      console.log(`[CRON] [${timestamp}] Total unique apps: ${appMap.size}`);
      
      let index = 1;
      appMap.forEach((app, clientId) => {
        console.log(`[CRON] [${timestamp}] ${index}. App: ${app.appName}`);
        console.log(`[CRON] [${timestamp}]    Client ID: ${clientId}`);
        console.log(`[CRON] [${timestamp}]    Users: ${app.users.size}`);
        console.log(`[CRON] [${timestamp}]    Requested services: ${Array.from(app.scopes).join(', ')}`);
        console.log(`[CRON] [${timestamp}]    Last accessed: ${app.lastAccessed || 'Unknown'}`);
        index++;
      });

      await this.storeOAuthAppsData(organisationId, appMap, timestamp, isFirstSync);
      
      // Trigger domain resolution asynchronously (non-blocking)
      await this.domainResolutionService.resolveMissingDomains(organisationId).catch(error => {
        console.error(`[CRON] [${timestamp}] Error in domain resolution:`, error.message);
      });

    } catch (error) {
      console.error(`[CRON] [${timestamp}] Full error details:`, {
        code: error.code,
        message: error.message,
        errors: error.errors,
        response: error.response?.data
      });
      
      // Try to update sync metadata if we have an organization ID
      if (organisationId) {
        try {
          await this.oauthSyncMetadataModel.findOneAndUpdate(
            { organisationId },
            {
              syncStatus: 'failed',
              errorMessage: `${error.code || 'ERROR'}: ${error.message}`,
              updatedAt: new Date()
            },
            { upsert: true }
          ).exec();
        } catch (metadataError) {
          console.error(`[CRON] [${timestamp}] Failed to update sync metadata:`, metadataError.message);
        }
      }
      
      if (error.code === 403) {
        console.log(`[CRON] [${timestamp}] Account ${account.id}: ERROR 403 - Insufficient permissions`);
        console.log(`[CRON] [${timestamp}] Possible reasons:`);
        console.log(`[CRON] [${timestamp}] 1. User is not a Google Workspace admin`);
        console.log(`[CRON] [${timestamp}] 2. Admin Reports API is not enabled in Google Cloud Console`);
        console.log(`[CRON] [${timestamp}] 3. User needs Super Admin role, not just admin`);
      } else if (error.code === 401) {
        console.log(`[CRON] [${timestamp}] Account ${account.id}: ERROR 401 - Unauthorized (token expired or invalid)`);
      } else if (error.code === 404) {
        console.log(`[CRON] [${timestamp}] Account ${account.id}: ERROR 404 - Resource not found`);
      } else {
        console.error(`[CRON] [${timestamp}] Account ${account.id}: ERROR - ${error.message}`);
      }
      console.log(`[CRON] [${timestamp}] Account ${account.id}: Status: FAILED - Moving to next account`);
    }
  }

  private async getOrganisationIdForAccount(accountId: string, timestamp: string): Promise<string | null> {
    try {
      const organisationData = await this.accountModel.sequelize.query(`
        SELECT
          o.id AS organisation_id
        FROM accounts a
        JOIN users u ON a."userId" = u.id
        JOIN "userSchool" us ON u.id = us."userId"
        JOIN school s ON us."schoolId" = s.id
        JOIN organisation o ON s."organisationId" = o.id
        WHERE
          a.id = :accountId
          AND u.deleted = false
          AND o.deleted = false
        LIMIT 1
      `, {
        replacements: { accountId },
        type: QueryTypes.SELECT
      }) as any[];

      if (!organisationData.length) {
        return null;
      }

      return organisationData[0].organisation_id;
    } catch (error) {
      console.error(`[CRON] [${timestamp}] Error getting organisation for account ${accountId}:`, error.message);
      return null;
    }
  }

  private async refreshAccountToken(account: Account): Promise<any> {
    const timestamp = new Date().toISOString();
    
    try {
      const credentials = account.credentials;
      
      if (!credentials || !credentials.refresh_token) {
        console.log(`[TOKEN_REFRESH] [${timestamp}] Account ${account.id}: No refresh token available`);
        return null;
      }

      const nowWithOffset = new Date();
      nowWithOffset.setMinutes(nowWithOffset.getMinutes() + 5);

      if (credentials.expiry_date && new Date(credentials.expiry_date) > nowWithOffset) {
        console.log(`[TOKEN_REFRESH] [${timestamp}] Account ${account.id}: Token still valid, no refresh needed`);
        return credentials;
      }

      console.log(`[TOKEN_REFRESH] [${timestamp}] Account ${account.id}: Refreshing token...`);

      const newCredentials = await this.googleOAuthStrategy.refresh({
        refresh_token: credentials.refresh_token,
      });

      await account.update({ credentials: newCredentials });

      console.log(`[TOKEN_REFRESH] [${timestamp}] Account ${account.id}: Token refreshed successfully`);
      return newCredentials;

    } catch (error) {
      console.error(`[TOKEN_REFRESH] [${timestamp}] Account ${account.id}: Token refresh failed:`, error.message);
      
      if (error.message.includes('invalid_grant') || error.message.includes('refresh_token')) {
        console.log(`[TOKEN_REFRESH] [${timestamp}] Account ${account.id}: Refresh token is invalid - User needs to re-authenticate`);
      }
      
      return null;
    }
  }

  private async storeOAuthAppsData(organisationId: string, appMap: Map<string, any>, timestamp: string, isFirstSync: boolean): Promise<void> {
    let syncMetadata = null;
    
    try {
      console.log(`[CRON] [${timestamp}] Starting database storage for ${appMap.size} OAuth apps`);
      console.log(`[CRON] [${timestamp}] Organization ${organisationId}: ${isFirstSync ? 'Initial' : 'Incremental'} sync`);

      // Create or update sync metadata to mark sync as in progress
      syncMetadata = await this.oauthSyncMetadataModel.findOneAndUpdate(
        { organisationId },
        {
          syncStatus: 'in_progress',
          isFirstSync,
          updatedAt: new Date()
        },
        { upsert: true, new: true }
      ).exec();

      // Prepare apps for upsert - incremental update only
      const bulkOperations = [];

      appMap.forEach((app, clientId) => {
        bulkOperations.push({
          updateOne: {
            filter: { organisationId, clientId },
            update: {
              $set: {
                appName: app.appName,
                userCount: app.users.size,
                scopes: Array.from(app.scopes),
                lastAccessed: app.lastAccessed ? new Date(app.lastAccessed) : null,
                updatedAt: new Date()
              }
            },
            upsert: true
          }
        });
      });

      if (bulkOperations.length > 0) {
        const result = await this.oauthAppModel.bulkWrite(bulkOperations);
        console.log(`[CRON] [${timestamp}] Upserted ${result.modifiedCount + result.upsertedCount} OAuth apps`);
        console.log(`[CRON] [${timestamp}] Modified: ${result.modifiedCount}, New: ${result.upsertedCount}`);
      }

      // Count total apps for this organization
      const totalAppsCount = await this.oauthAppModel.countDocuments({ organisationId });

      // Update sync metadata to mark as successful
      await this.oauthSyncMetadataModel.findOneAndUpdate(
        { organisationId },
        {
          syncStatus: 'success',
          lastSyncTimestamp: new Date(),
          totalAppsCount,
          errorMessage: null,
          updatedAt: new Date()
        }
      ).exec();

      console.log(`[CRON] [${timestamp}] Database storage completed successfully`);
      console.log(`[CRON] [${timestamp}] Total OAuth apps for organization: ${totalAppsCount}`);

    } catch (error) {
      console.error(`[CRON] [${timestamp}] Error storing OAuth apps data:`, error.message);
      
      // Update sync metadata to mark as failed
      if (syncMetadata) {
        await this.oauthSyncMetadataModel.findOneAndUpdate(
          { organisationId },
          {
            syncStatus: 'failed',
            errorMessage: error.message,
            updatedAt: new Date()
          }
        ).exec();
      }
      
      throw error;
    }
  }
}
