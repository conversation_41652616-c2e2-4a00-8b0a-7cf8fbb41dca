import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { SequelizeModule } from '@nestjs/sequelize';
import { MongooseModule } from '@nestjs/mongoose';
import { Account } from '@goteacher/app/models/sequelize/account.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { OAuthApp, OAuthAppSchema } from '@goteacher/app/models/mongo/oauth-app.model';
import { GoogleOAuthModule } from '@goteacher/app/auth/oauth';
import { ConfigService } from '@nestjs/config';
import { SchedulerService } from './scheduler.service';
import { DomainResolutionService } from '@goteacher/app/oauth-apps/service/domain-resolution.service';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    SequelizeModule.forFeature([Account, User]),
    MongooseModule.forFeature([{ name: OAuthApp.name, schema: OAuthAppSchema }]),
    GoogleOAuthModule.forRoot({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        clientId: configService.get<string>('GOOGLE_OAUTH_CLIENT_ID'),
        clientSecret: configService.get<string>('GOOGLE_OAUTH_CLIENT_SECRET'),
        redirectUri: configService.get<string>('GOOGLE_OAUTH_REDIRECT_URI'),
      }),
    }),
  ],
  providers: [SchedulerService, DomainResolutionService],
})
export class SchedulerModule {}
