import {
  GetOrgMetadata<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ListOrgMetadata<PERSON><PERSON><PERSON>,
  ListProductsHandler,
  UpsertOrgMetadataHandler,
} from '@goteacher/app/metadata';

import { CreateCustomMeradataHandler } from '@goteacher/app/metadata/command/create-custom-metadata';
import { DeleteCustomMetadataHandler } from '@goteacher/app/metadata/command/delete-custom-metadata';
import { GetCustomMetadtaQueryHandler } from '@goteacher/app/metadata/query/get-custom-metadata/handler';
import { ListCategoriesHandler } from '@goteacher/app/metadata/query/list-categories';
import { ListPlatformsHandler } from '@goteacher/app/metadata/query/list-platform';
import { ListProductsHandlerV2 } from '@goteacher/app/metadata/query/list-product-v2';
import { MetadataController } from 'apps/api/src/api/metadata/metadata.controller';
import { Module } from '@nestjs/common';
import { UpdateCustomMetadataCommandHandler } from '@goteacher/app/metadata/command/update-custom-metadata';

@Module({
  imports: [],
  controllers: [MetadataController],
  providers: [
    UpsertOrgMetadataHandler,
    GetOrgMetadataQueryHandler,
    ListOrgMetadataHandler,
    ListPlatformsHandler,
    ListProductsHandler,
    ListProductsHandlerV2,
    CreateCustomMeradataHandler,
    UpdateCustomMetadataCommandHandler,
    DeleteCustomMetadataHandler,
    GetCustomMetadtaQueryHandler,
    ListCategoriesHandler,
  ],
  exports: [],
})
export class APIMetadataModule {}
