import { ApiProperty } from '@nestjs/swagger';

export class GoogleTokenResponseDto {
  @ApiProperty({
    description: 'GoTeacher OAuth2 access token.',
  })
  accessToken: string;

  @ApiProperty({
    description: 'GoTeacher OAuth2 refresh token.',
  })
  refreshToken: string;
}

export class GoogleRefreshResponseDto {
  @ApiProperty({
    description: 'Google OAuth2 access token.',
  })
  accessToken: string;
}

export class AppleLoginResponseDto {
  // @ApiProperty({
  //   description: 'Apple OAuth2 access token.',
  // })
  // accessToken: string;

  // @ApiProperty({
  //   description: 'Apple OAuth2 refresh token.',
  // })
  // refreshToken: string;
  @ApiProperty({
    description: 'Apple OAuth2 url.',
  })
  url: string;
}

export class AppleRedirectResponseDto {
  @ApiProperty({
    description: 'Apple OAuth2 access token.',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Apple OAuth2 refresh token.',
  })
  refreshToken: string;
}
