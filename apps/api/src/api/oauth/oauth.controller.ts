/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  JWTGuard,
  JWTProvider,
  JWTService,
  RefreshTokenCommand,
  RegisterUserCommand,
  ReqContext,
} from '@goteacher/app/auth';
import { AuthMeQuery } from '@goteacher/app/auth/query/goteacher';
import { Account } from '@goteacher/app/models/sequelize/account.model';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { ICacheService } from '@goteacher/infra/cache';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import {
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiServiceUnavailableResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  RefreshTokenRequestBodyDto,
  RegisterUserRequestBodyDto,
} from 'apps/api/src/api/oauth/req.dto';
import { GoogleTokenResponseDto } from 'apps/api/src/api/oauth/res.dto';
import { FastifyReply } from 'fastify';
import { toLower } from 'lodash';

@ApiTags('oauth')
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@ApiServiceUnavailableResponse({ description: 'Service unavailable' })
@Controller('oauth')
export class OAuthController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Account) private accountModel: typeof Account,
    private readonly configService: ConfigService,
    private readonly jwtService: JWTService,
    private cacheService: ICacheService,
  ) {}

  @Get('me')
  @UseGuards(JWTGuard)
  @HttpCode(HttpStatus.OK)
  @ApiSecurity('bearer')
  // @ApiOkResponse({
  //   type: User,
  // })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiOperation({ summary: 'Get current user' })
  async me(@ReqContext() ctx: ReqContext) {
    return await this.queryBus.execute(new AuthMeQuery({ userId: ctx.userId }));
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: GoogleTokenResponseDto,
  })
  @ApiUnauthorizedResponse({ description: 'No user found.' })
  @ApiOperation({ summary: 'Refresh access token' })
  async refresh(@Body() body: RefreshTokenRequestBodyDto) {
    return await this.commandBus.execute(
      new RefreshTokenCommand({ refreshToken: body.refreshToken }),
    );
  }

  @Post('register')
  @UseGuards(JWTGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: User,
  })
  @ApiUnauthorizedResponse({ description: 'No user found.' })
  @ApiSecurity('bearer')
  @ApiOperation({ summary: 'Complete registration for user.' })
  async register(
    @Body() body: RegisterUserRequestBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.commandBus.execute(
      new RegisterUserCommand({
        ctx: ctx,
        ...body,
      }),
    );
  }

  // @Get('apple')
  // @ApiOperation({ summary: 'Apple login' })
  // @UseGuards(AuthGuard('apple'))
  // async apple(): Promise<any> {
  //   return HttpStatus.OK;
  // }

  @Get('clever')
  // @UseGuards(AuthGuard('clever'))
  async loginWithClever(@Req() req, @Res() res: FastifyReply) {
    const redirectUrl =
      'https://clever.com/oauth/authorize' + '?' + this.getRedirectUrlParams();

    console.log('Redirect URL:', redirectUrl);

    res.status(200).send({
      redirectUrl: redirectUrl,
    });
  }

  getRedirectUrlParams() {
    return new URLSearchParams({
      client_id: this.configService.get<string>('CLEVER_CLIENT_ID'),
      redirect_uri: 'https://app.goteacher.com/auth/clever',
      response_type: 'code',
      scope: 'read:students_basic read:teachers_basic',
      // district_id: this.configService.get<string>('CLEVER_DISTRICT_ID'),
    }).toString();
  }

  @Get('clever/callback')
  // @UseGuards(AuthGuard('clever'))
  async cleverCallback(@Req() req, @Res() res) {
    const { access_token } = await this.getTokensFromCode(req.query.code);

    const user = await this.getUserInfo(access_token);

    console.log('User:', user);

    const tokens = await this.exchangeCleverTokens(user);

    res.status(200).send({
      message: 'Login successful',
      user,
      tokens,
    });
  }

  async getUserInfo(access_token: string): Promise<CleverUser> {
    const response = await fetch('https://api.clever.com/userinfo', {
      headers: {
        Authorization: `Bearer ${access_token}`,
        Accept: 'application/json',
      },
    });

    const data = await response.json();

    return data;
  }

  async getUserProfile(accessToken: string) {
    const response = await fetch('https://api.clever.com/v3.0/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: 'application/json',
      },
    });
    const data = await response.json();
    return data;
  }

  async getTokensFromCode(code: string): Promise<TokenResponse> {
    const myHeaders = new Headers();
    const clientId = this.configService.get<string>('CLEVER_CLIENT_ID');
    const clientSecret = this.configService.get<string>('CLEVER_SECRET');
    const basicAuth = Buffer.from(`${clientId}:${clientSecret}`).toString(
      'base64',
    );

    myHeaders.append('Content-Type', 'application/x-www-form-urlencoded');
    myHeaders.append('Authorization', 'Basic ' + basicAuth);

    const urlencoded = new URLSearchParams();
    urlencoded.append('code', code);
    urlencoded.append('grant_type', 'authorization_code');
    urlencoded.append('redirect_uri', 'https://app.goteacher.com/auth/clever');

    const response = await fetch('https://clever.com/oauth/tokens', {
      method: 'POST',
      headers: myHeaders,
      body: urlencoded,
      redirect: 'follow',
    });

    const data = await response.json();

    console.log('Data:', data);

    return data;
  }

  async exchangeCleverTokens(cleverUser: CleverUser) {
    let user = await this.userModel.findOne({
      where: { email: toLower(cleverUser.email) },
      include: [
        {
          model: UserSchool,
          include: [
            {
              model: School,
              attributes: ['id', 'organisationId'],
            },
          ],
        },
      ],
    });

    if (!user) {
      user = await this.userModel.create({
        email: toLower(cleverUser.email),
        firstName: cleverUser.name.first,
        lastName: cleverUser.name.last,
        picture: '',
        role: 'student',
        isRegistrationCompleted: false,
      });

      // TODO: do we use this?
      // await this.accountModel.create({
      //   platform: 'clever',
      //   credentials: {},
      //   externalId: cleverUser.user_id,
      //   userId: user.id,
      // });
    }

    const schoolIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.schoolId)
      : [];
    const orgIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.school.organisationId)
      : [];

    this.cacheService.set(`user-${user.id}`, user, 60 * 60);

    const tokens = this.jwtService.signPayload({
      email: user.email,
      provider: JWTProvider.CLEVER,
      role: user.role,
      sub: user.id,
      schoolIds,
      orgIds,
    });

    return tokens;
  }
}

// @Post('apple/success')
// @HttpCode(HttpStatus.OK)
// @ApiOkResponse({
//   type: AppleRedirectResponseDto,
// })
// @ApiOperation({ summary: 'Apple login redirect' })
// async redirect(@Body() payload: AppleLoginRequestDto) {
//   return await this.commandBus.execute(new AppleLoginCommand(payload));
// }

type TokenResponse = {
  access_token: string;
  token_type: string;
};

export type CleverUser = {
  sub: string;
  user_id: string;
  multi_role_user_id?: string;
  user_type: 'student' | 'teacher'; // Modify based on possible roles
  district_id?: string;
  district?: string;
  email: string;
  name: {
    first: string;
    last: string;
    middle?: string;
  };
  schools: string[];
  given_name: string;
  family_name: string;
  email_verified: boolean;
  // ??
  authorized_by?: 'district' | 'school' | 'admin'; // Modify based on possible authorizations
};
