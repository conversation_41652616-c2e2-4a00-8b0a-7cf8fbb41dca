import {
  GOOGLE_SCOPES,
  GoogleAuthorization<PERSON>uery,
  JWTGuard,
  ReqContext,
} from '@goteacher/app/auth';
import {
  GoogleExchangeCodeCommand,
  GoogleRefreshCommand,
} from '@goteacher/app/auth/command';
import { GoogleExtensionTokenCommand } from '@goteacher/app/auth/command/google/extension-token/command';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiProduces,
  ApiResponse,
  ApiSecurity,
  ApiServiceUnavailableResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  GoogleAuthorizeRequestQueryDto,
  GoogleExtensionTokenRequestBodyDto,
  GoogleTokenRequestBodyDto,
} from 'apps/api/src/api/oauth/req.dto';
import {
  GoogleRefreshResponseDto,
  GoogleTokenResponseDto,
} from 'apps/api/src/api/oauth/res.dto';

@ApiTags('oauth')
@ApiInternalServerErrorResponse({
  description: 'Internal server error.',
})
@ApiServiceUnavailableResponse({ description: 'Service unavailable' })
@Controller('oauth/google')
export class OAuthGoogleController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) { }

  @Get('authorize')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    type: String,
    description: 'Authorization URL as text.',
    example:
      'https://accounts.google.com/o/oauth2/v2/auth?client_id=**********&redirect_uri=https%3A%2F%2Fwww.example.com%2Fcallback&response_type=code&scope=email%20profile%20classroom.announcements%20classroom.courses%20classroom.coursework.me%20classroom.coursework.students%20classroom.coursework.materials%20classroom.guardianlinks.students.readonly%20classroom.profile.emails%20classroom.profile.photos%20classroom.push.notifications%20classroom.rosters.readonly%20classroom.topics&state=redirect_uri%3Dhttps%253A%252F%252Fwww.example.com%252Fcallback%26role%3Dmanager',
    status: 200,
  })
  @ApiOperation({
    summary: 'Get Google OAUTH authorization URL.',
  })
  @ApiProduces('text/plain')
  async authorize(
    @Query() query: GoogleAuthorizeRequestQueryDto,
  ): Promise<string> {
    const state = new URLSearchParams({ redirect_uri: query.redirect_url });
    if (query.role) state.set('role', query.role);

    const authorizationResponse = await this.queryBus.execute(
      new GoogleAuthorizationQuery({
        ...query,
        redirect_uri: query.redirect_url,
        state: state.toString(),
        scope: [
          GOOGLE_SCOPES.EMAIL,
          GOOGLE_SCOPES.PROFILE,
          GOOGLE_SCOPES.CLASSROOM_ANNOUNCEMENTS,
          GOOGLE_SCOPES.CLASSROOM_COURSES,
          GOOGLE_SCOPES.CLASSROOM_COURSEWORK_ME,
          GOOGLE_SCOPES.CLASSROOM_COURSEWORK_STUDENTS,
          GOOGLE_SCOPES.CLASSROOM_COURSEWORK_MATERIALS,
          GOOGLE_SCOPES.CLASSROOM_GUARDIANLINKS_STUDENTS_READONLY,
          GOOGLE_SCOPES.CLASSROOM_PROFILE_EMAILS,
          GOOGLE_SCOPES.CLASSROOM_PROFILE_PHOTOS,
          GOOGLE_SCOPES.CLASSROOM_PUSH_NOTIFICATIONS,
          GOOGLE_SCOPES.CLASSROOM_ROSTERS_READONLY,
          GOOGLE_SCOPES.CLASSROOM_TOPICS,
          GOOGLE_SCOPES.ADMIN_REPORTS_AUDIT_READONLY,
        ],
      }),
    );

    return authorizationResponse.uri;
  }

  @Post('token')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: GoogleTokenResponseDto,
    description: 'GoTeacher JWT tokens.',
  })
  @ApiUnauthorizedResponse({
    description: 'ORGANISATION_NOT_APPROVED or GOOGLE_RELOGIN',
  })
  @ApiOperation({
    summary: 'Exchange Google OAuth2 authorization code for access token.',
  })
  async token(@Body() body: GoogleTokenRequestBodyDto) {
    return await this.commandBus.execute(new GoogleExchangeCodeCommand(body));
  }

  @Post('extension/token')
  @ApiOkResponse({
    type: GoogleTokenResponseDto,
    description: 'GoTeacher JWT tokens.',
  })
  @ApiUnauthorizedResponse({
    description: 'Google access token is invalid, expired, or lacks required permissions.',
  })
  @ApiOperation({
    summary:
      'Used by GoTeacher Chrome extension to exchange google access token for refresh token.',
  })
  async extensionToken(@Body() body: GoogleExtensionTokenRequestBodyDto) {
    return await this.commandBus.execute(
      new GoogleExtensionTokenCommand({ ...body }),
    );
  }

  @Get('refresh')
  @UseGuards(JWTGuard)
  @ApiOkResponse({
    type: GoogleRefreshResponseDto,
    description: 'Google OAuth2 access token.',
  })
  @ApiNotFoundResponse({
    description: 'No credentials found for this user!',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized access.',
  })
  @ApiSecurity('bearer')
  @ApiOperation({
    summary: 'Refresh Google OAuth2 access token.',
  })
  async me(@ReqContext() ctx: ReqContext): Promise<GoogleRefreshResponseDto> {
    const credentials = await this.commandBus.execute(
      new GoogleRefreshCommand({ userId: ctx.userId }),
    );

    return { accessToken: credentials.access_token };
  }
}
