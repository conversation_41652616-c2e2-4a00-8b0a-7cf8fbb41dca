import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { IPrompt } from '@goteacher/app/auth';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';

export class GoogleAuthorizeRequestQueryDto {
  @ApiProperty({
    description:
      'Redirect URL code callback will be sent to. Needs to be whitelisted in Google Console.',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  redirect_url: string;

  @ApiProperty({
    description: 'OAuth client id.',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  client_id: string;

  @ApiProperty({
    description:
      'If not provided, user will be asked to consent by default. The only way to receive a new refresh token is to set this to `consent`.',
    enum: IPrompt,
    required: false,
    default: null,
  })
  @IsOptional()
  @IsEnum(IPrompt)
  prompt?: IPrompt;

  @ApiProperty({
    description:
      'GoTeacher role. If not provided, user will not be assigned a role.',
    required: false,
    enum: UserRole,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;
}

export class GoogleTokenRequestBodyDto {
  @ApiProperty({
    description: 'OAuth authorization code.',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'OAuth authorization state. Currently set to redirect url.',
  })
  @IsString()
  @IsNotEmpty()
  state: string;
}

export class GoogleExtensionTokenRequestBodyDto {
  @ApiProperty({
    description: 'Chrome extension access token.',
  })
  @IsString()
  @IsNotEmpty()
  token: string;
}

export class RefreshTokenRequestBodyDto {
  @ApiProperty({
    description: 'Refresh token.',
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;
}

export class RegisterUserRequestBodyDto {
  @ApiProperty({
    description: 'User role.',
    required: false,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role: UserRole;
}

export class AppleLoginRequestDto {
  @ApiProperty({
    description: 'Apple OAuth2 code.',
  })
  @IsString()
  @IsNotEmpty()
  id_token: string;
  @ApiProperty({
    description: 'Apple OAuth2 code.',
  })
  @IsString()
  @IsNotEmpty()
  code: string;
  @ApiProperty({
    description: 'Apple OAuth2 code.',
  })
  @IsString()
  @IsNotEmpty()
  user: string;
}
