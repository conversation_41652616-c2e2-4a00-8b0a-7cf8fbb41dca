import { UserGroup } from '@goteacher/app/analytics';
import { sqlSanitizer, TransformToBoolean } from '@goteacher/app/utility';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsOptional,
} from 'class-validator';

export class GetAudienceQueryDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
          typeof item === 'string' ? sqlSanitizer(item) : item,
        )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
          typeof item === 'string' ? sqlSanitizer(item) : item,
        )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;
}

export class GetLicensesQueryDto {
  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    description: 'Start date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  fromDate?: Date;

  @ApiProperty({
    required: false,
    description: 'End date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  toDate?: Date;
}

export class GetSummaryQueryDto {
  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    description: 'Start date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  fromDate?: Date;

  @ApiProperty({
    required: false,
    description: 'End date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  toDate?: Date;
}

export class GetSchoolsTopSpendQueryDto {
  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    description: 'Start date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  fromDate?: Date;

  @ApiProperty({
    required: false,
    description: 'End date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  toDate?: Date;
}

export class GetGradesTopSpendQueryDto {
  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    description: 'Start date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  fromDate?: Date;

  @ApiProperty({
    required: false,
    description: 'End date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  toDate?: Date;
}
