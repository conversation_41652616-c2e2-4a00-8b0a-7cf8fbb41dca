import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { RoleGuard, RoleProtected } from '@goteacher/app/auth/guard/role.guard';
import {
  GetGradesTopSpendQuery,
  GetLicensesQuery,
  GetSchoolsTopSpendQuery,
  GetSummaryQuery,
} from '@goteacher/app/financial';
import { GetAudienceQuery } from '@goteacher/app/financial/query/calculate-audience';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Query,
  UseGuards,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  GetAudienceQueryDto,
  GetGradesTopSpendQueryDto,
  GetLicensesQueryDto,
  GetSchoolsTopSpendQueryDto,
  GetSummaryQueryDto,
} from 'apps/api/src/api/financial/cost/req.dto';
import {
  AudienceResponseDto,
  GradesTopSpendResponseDto,
  LicensesSummaryResponseDto,
  SchoolsTopSpendResponseDto,
  SummaryResponseDto,
} from 'apps/api/src/api/financial/cost/res.dto';

@ApiTags('financial')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@UseGuards(JWTGuard, RoleGuard)
@RoleProtected([UserRole.ADMINISTRATOR])
@Controller('financial/cost')
export class FinancialCostController {
  constructor(private readonly queryBys: QueryBus) {}

  @HttpCode(HttpStatus.OK)
  @Get('audience')
  @ApiOkResponse({ type: AudienceResponseDto })
  @ApiOperation({ summary: 'Calculate audience' })
  async getAudience(
    @ReqContext() ctx: ReqContext,
    @Query() query: GetAudienceQueryDto,
  ) {
    return await this.queryBys.execute(new GetAudienceQuery({ ...query, ctx }));
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: LicensesSummaryResponseDto })
  @ApiOperation({ summary: 'Get licenses summary for all contracts' })
  @Get('licenses')
  async getLicenses(
    @ReqContext() ctx: ReqContext,
    @Query() query: GetLicensesQueryDto,
  ) {
    return await this.queryBys.execute(new GetLicensesQuery({ ...query, ctx }));
  }

  @Get('summary')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: SummaryResponseDto })
  @ApiOperation({ summary: 'Get summary of all contracts' })
  async getSummary(
    @ReqContext() ctx: ReqContext,
    @Query() query: GetSummaryQueryDto,
  ) {
    return await this.queryBys.execute(new GetSummaryQuery({ ...query, ctx }));
  }

  @Get('schools-top-spend')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: SchoolsTopSpendResponseDto,
    isArray: true,
  })
  @ApiOperation({ summary: 'Get top spending schools' })
  async getSchoolsTopSpend(
    @ReqContext() ctx: ReqContext,
    @Query() query: GetSchoolsTopSpendQueryDto,
  ) {
    return await this.queryBys.execute(
      new GetSchoolsTopSpendQuery({ ...query, ctx }),
    );
  }

  @Get('grades-top-spend')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: GradesTopSpendResponseDto,
    isArray: true,
  })
  @ApiOperation({ summary: 'Get top spending grades' })
  async getGradesTopSpend(
    @ReqContext() ctx: ReqContext,
    @Query() query: GetGradesTopSpendQueryDto,
  ) {
    return await this.queryBys.execute(
      new GetGradesTopSpendQuery({ ...query, ctx }),
    );
  }
}
