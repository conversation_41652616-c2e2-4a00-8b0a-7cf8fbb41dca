import { ApiProperty } from '@nestjs/swagger';

export class AudienceResponseDto {
  @ApiProperty({
    description: 'Total audience',
    type: Number,
  })
  total: number;

  @ApiProperty({
    description: 'Audience without trackable grades',
    type: Number,
  })
  unTrackable: number;

  @ApiProperty({
    description: 'Audience with trackable grades',
    type: Number,
  })
  trackable: number;
}

export class LicensesSummaryResponseDto {
  @ApiProperty({
    description: 'Total licenses',
    type: Number,
  })
  licenses: number;

  @ApiProperty({
    description: 'Total active licenses',
    type: Number,
  })
  activeLicenses: number;

  @ApiProperty({
    description: 'Total inactive licenses',
    type: Number,
  })
  inactiveLicenses: number;

  @ApiProperty({
    description: 'Total extra licenses',
    type: Number,
  })
  extraLicenses: number;

  @ApiProperty({
    description: 'Total licenses untrackable',
    type: Number,
  })
  licenses_untrackable: number;

  @ApiProperty({
    description: 'Total licenses unaccounted',
    type: Number,
  })
  licenses_unaccounted: number;
}

export class SummaryResponseDto {
  @ApiProperty({
    description: 'Total cost',
    type: Number,
  })
  totalCost: number;

  @ApiProperty({
    description: 'Total cost savings',
    type: Number,
  })
  totalSavings: number;

  @ApiProperty({
    description: 'Total number of contracts',
    type: Number,
  })
  numberOfContracts: number;

  @ApiProperty({
    description: 'Total number of users',
    type: Number,
  })
  userCount: number;
}

export class SchoolsTopSpendResponseDto {
  @ApiProperty({
    description: 'School ID',
    type: String,
  })
  schoolId: string;

  @ApiProperty({
    description: 'Total cost',
    type: Number,
  })
  totalCost: number;
}

export class GradesTopSpendResponseDto {
  @ApiProperty({
    description: 'Grade',
    type: String,
  })
  grade: string;

  @ApiProperty({
    description: 'Total cost',
    type: Number,
  })
  totalCost: number;
}
