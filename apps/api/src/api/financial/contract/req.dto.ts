import {
  ApiExtraModels,
  ApiProperty,
  OmitType,
  PartialType,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';
import {
  OrderByRequest,
  PaginationRequest,
  TransformToBoolean,
} from '@goteacher/app/utility';
import {
  PaymentMode,
  PaymentTerm,
  PaymentTermFixedFee,
  PaymentTermLicence,
} from '@goteacher/app/models/mongo';
import { Transform, Type } from 'class-transformer';

import { Type as TransformType } from 'class-transformer';

@ApiExtraModels(PaymentTermLicence, PaymentTermFixedFee)
export class CreateContractRequestBodyDto {
  @ApiProperty({
    example: 'org.example.com',
    required: false,
    description: 'Domain name - required if productId is not provided',
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({
    example: '67371943db5db89eb879318b',
    description: 'Product ID - required if domain is not provided',
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    example: 'Product name',
    description: 'Product name',
    required: false,
  })
  @IsOptional()
  @IsString()
  productName?: string;

  @ApiProperty({
    example: 'Product full domain',
    description: 'Product full domain',
    required: false,
  })
  @IsOptional()
  @IsString()
  fullDomain?: string;

  @ApiProperty({ description: 'Contract title', required: false, type: String })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Subscription start date',
    required: true,
    type: Date,
  })
  @IsDate()
  @Type(() => Date)
  subscriptionStartDate: Date;

  @ApiProperty({
    description: 'Subscription end date',
    required: true,
    type: Date,
  })
  @IsDate()
  @Type(() => Date)
  subscriptionEndDate: Date;

  @ApiProperty({ description: 'Active subscription', type: Boolean })
  @IsOptional()
  @IsBoolean()
  active: boolean;

  @ApiProperty({ description: 'Extra fees', type: Number, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  extraFees: number;

  @ApiProperty({
    description: 'Terms',
    isArray: true,
    type: PaymentTerm,
    discriminator: {
      propertyName: 'paymentMode',
      mapping: {
        [PaymentMode.FIXED_FEE]: getSchemaPath(PaymentTermFixedFee),
        [PaymentMode.PRICE_PER_LICENSE]: getSchemaPath(PaymentTermLicence),
      },
    },
    required: true,
  })
  @IsArray()
  @Type(() => PaymentTerm, {
    keepDiscriminatorProperty: true,
    discriminator: {
      property: 'paymentMode',
      subTypes: [
        { value: PaymentTermFixedFee, name: PaymentMode.FIXED_FEE },
        { value: PaymentTermLicence, name: PaymentMode.PRICE_PER_LICENSE },
      ],
    },
  })
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  terms: Array<PaymentTermFixedFee | PaymentTermLicence>;
}

export class DeleteContractRequestParamsDto {
  @ApiProperty({
    example: 'c2a1e5a0-3a45-4a10-8e5f-1e3d4f5e6b2a',
    description: 'Contract Object ID',
    required: true,
  })
  @IsString()
  contractId: string;
}

export class UpdateContractRequestParamsDto {
  @ApiProperty({
    example: 'c2a1e5a0-3a45-4a10-8e5f-1e3d4f5e6b2a',
    description: 'Contract Object ID',
    required: true,
  })
  @IsString()
  contractId: string;
}

export class UpdateContractRequestBodyDto extends PartialType(
  OmitType(CreateContractRequestBodyDto, ['domain']),
) {}

export class GetContractRequestParamsDto {
  @ApiProperty({
    example: 'c2a1e5a0-3a45-4a10-8e5f-1e3d4f5e6b2a',
    description: 'Contract Object ID',
    required: true,
  })
  @IsString()
  contractId: string;
}

export class GetContractRequestQueryDto {
  @ApiProperty({
    description: 'Include cost breakdown',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  withCostBreakdown: boolean;
}

export class GetContractUsersRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}

export class GetContractUsersCsvRequestQueryDto {
  @ApiProperty({
    required: false,
    description: 'Order by',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMaxSize(5)
  @TransformType(() => OrderByRequest)
  orderBy?: OrderByRequest[];

  @ApiProperty({
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}

export class GetPlatformUsersCsvRequestQueryDto {
  @ApiProperty({
    required: false,
    description: 'Domain',
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({
    required: false,
    description: 'Product Id',
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: false,
    description: 'Order by',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMaxSize(5)
  @TransformType(() => OrderByRequest)
  order: OrderByRequest[];

  @ApiProperty({
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}

export class ListContractQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    description: 'Domains',
    required: false,
    type: [String],
  })
  @IsOptional()
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  @IsArray()
  @ArrayMaxSize(100)
  @IsString({ each: true })
  @Type(() => String)
  domains: string[];

  @ApiProperty({
    required: false,
    description: 'Subscription start date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  fromDate?: Date;

  @ApiProperty({
    required: false,
    description: 'Subscription end date',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  toDate?: Date;

  @ApiProperty({
    required: false,
    description: 'Active subscription',
  })
  @TransformToBoolean()
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @ApiProperty({
    description: 'Include cost breakdown',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  withCostBreakdown: boolean;

  @ApiProperty({
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}
