import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { RoleGuard, RoleProtected } from '@goteacher/app/auth/guard/role.guard';
import { CreateContractCommand } from '@goteacher/app/financial';
import { DeleteContractCommand } from '@goteacher/app/financial/command/delete-contract';
import { UpdateContractCommand } from '@goteacher/app/financial/command/update-contract/command';
import { GetContractQuery } from '@goteacher/app/financial/query/get-contract';
import { GetContractUsersQuery } from '@goteacher/app/financial/query/get-contract-users';
import { GetContractUsersCsvQuery } from '@goteacher/app/financial/query/get-contract-users-csv';
import { ListContractQuery } from '@goteacher/app/financial/query/list-contract';
import { Contract } from '@goteacher/app/models/mongo';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  CreateContractRequestBodyDto,
  DeleteContractRequestParamsDto,
  GetContractRequestParamsDto,
  GetContractRequestQueryDto,
  GetContractUsersCsvRequestQueryDto,
  GetContractUsersRequestQueryDto,
  ListContractQueryDto,
  UpdateContractRequestBodyDto,
  UpdateContractRequestParamsDto,
} from 'apps/api/src/api/financial/contract/req.dto';
import { GetContractUsersResponseDto, ListContractsResponseDto } from 'apps/api/src/api/financial/contract/res.dto';
import { FastifyReply } from 'fastify';

@ApiTags('financial')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@UseGuards(JWTGuard, RoleGuard)
@RoleProtected([UserRole.ADMINISTRATOR])
@Controller('financial/contract')
export class FinancialContractController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) { }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Contract created successfully',
    type: Contract,
  })
  @ApiOperation({ summary: 'Create contract' })
  @Put()
  async createContract(
    @Body() body: CreateContractRequestBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.commandBus.execute(
      new CreateContractCommand({ contract: body, ctx }),
    );
  }

  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'Contract deleted successfully' })
  @ApiNotFoundResponse({ description: 'Contract not found' })
  @ApiOperation({ summary: 'Delete contract' })
  @Delete(':contractId')
  async deleteContract(
    @Param() params: DeleteContractRequestParamsDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.commandBus.execute(
      new DeleteContractCommand({ contractId: params.contractId, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Contract updated successfully',
    type: Contract,
  })
  @ApiNotFoundResponse({ description: 'Contract not found' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @ApiOperation({ summary: 'Update contract' })
  @Post(':contractId')
  async updateContract(
    @Param() params: UpdateContractRequestParamsDto,
    @Body() body: UpdateContractRequestBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.commandBus.execute(
      new UpdateContractCommand({
        contractId: params.contractId,
        contract: body,
        ctx,
      }),
    );
  }

  @Get(':contractId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Contract found successfully',
    type: Contract,
  })
  @ApiNotFoundResponse({ description: 'Contract not found' })
  @ApiOperation({ summary: 'Get contract' })
  async getContract(
    @Param() params: GetContractRequestParamsDto,
    @Query() query: GetContractRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetContractQuery({ contractId: params.contractId, ...query, ctx }),
    );
  }

  @Get(':contractId/users')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Contract users found successfully',
    type: GetContractUsersResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Contract not found' })
  @ApiOperation({ summary: 'Get contract users' })
  async getContractUsers(
    @Param() params: GetContractRequestParamsDto,
    @Query() query: GetContractUsersRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetContractUsersQuery({ contractId: params.contractId, ...query, ctx }),
    );
  }

  @Get(':contractId/users-csv')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Contract users exported to CSV successfully',
    schema: {
      type: 'string',
      format: 'binary'
    },
  })
  @ApiNotFoundResponse({ description: 'Contract not found' })
  @ApiOperation({ summary: 'Get contract users CSV' })
  async getContractUsersCsv(
    @Param() params: GetContractRequestParamsDto,
    @Query() query: GetContractUsersCsvRequestQueryDto,
    @ReqContext() ctx: ReqContext,
    @Res({ passthrough: true }) res: FastifyReply,
  ) {
    const csvBuffer = await this.queryBus.execute(
      new GetContractUsersCsvQuery({ contractId: params.contractId, ...query, ctx }),
    );

    res.header('Content-Type', 'text/csv');
    res.header('Content-Disposition', `attachment; filename="contract-users-${params.contractId}-${new Date().toISOString().split('T')[0]}.csv"`);
    return csvBuffer;
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Contracts found successfully',
    type: ListContractsResponseDto,
  })
  @ApiOperation({ summary: 'List contracts' })
  async getContracts(
    @Query() query: ListContractQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new ListContractQuery({ ...query, ctx }),
    );
  }
}
