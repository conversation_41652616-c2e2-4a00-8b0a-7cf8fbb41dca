import {
  GetFilterableGradesQuery,
  GetFilterableSchoolsQuery,
  GetFilterableStudentsQuery,
  GetFilterableSubjectsQuery,
} from '@goteacher/app/analytics';
import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { RoleGuard, RoleProtected } from '@goteacher/app/auth/guard/role.guard';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  GetFilterableGradesRequestQueryDto,
  GetFilterableSchoolsRequestQueryDto,
  GetFilterableStudentsRequestQueryDto,
  GetFilterableSubjectsRequestQueryDto,
} from 'apps/api/src/api/analytic/filter/req.dto';
import {
  GetFilterableGradesResponseDto,
  GetFilterableSchoolsResponseDto,
  GetFilterableStudentsResponseDto,
  GetFilterableSubjectsResponseDto,
} from 'apps/api/src/api/analytic/filter/res.dto';

@ApiSecurity('bearer')
@ApiTags('analytic')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal Server Error' })
@UseGuards(JWTGuard, RoleGuard)
@Controller('analytic/filter')
@RoleProtected([UserRole.ADMINISTRATOR, UserRole.TEACHER])
export class AnalyticFiltersController {
  constructor(private readonly queryBys: QueryBus) {}

  @Get('grades')
  @ApiOkResponse({ type: GetFilterableGradesResponseDto })
  @ApiOperation({ summary: 'Get filterable grades' })
  async getFilterableGrades(
    @Query() query: GetFilterableGradesRequestQueryDto,
  ) {
    return await this.queryBys.execute(new GetFilterableGradesQuery(query));
  }

  @Get('subjects')
  @ApiOkResponse({ type: GetFilterableSubjectsResponseDto })
  @ApiOperation({ summary: 'Get filterable subjects' })
  async getFilterableSubjects(
    @Query() query: GetFilterableSubjectsRequestQueryDto,
  ) {
    return await this.queryBys.execute(new GetFilterableSubjectsQuery(query));
  }

  @Get('schools')
  @ApiOkResponse({ type: GetFilterableSchoolsResponseDto })
  @ApiOperation({ summary: 'Get filterable schools' })
  async getFilterableSchools(
    @Query() query: GetFilterableSchoolsRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBys.execute(
      new GetFilterableSchoolsQuery({ ...query, ctx }),
    );
  }

  @Get('students')
  @ApiOkResponse({ type: GetFilterableStudentsResponseDto })
  @ApiOperation({ summary: 'Get filterable students' })
  async getFilterableStudents(
    @Query() query: GetFilterableStudentsRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBys.execute(
      new GetFilterableStudentsQuery({ ...query, ctx }),
    );
  }
}
