import { PaginationRequest, TransformToBoolean } from '@goteacher/app/utility';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class GetFilterableGradesRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    required: false,
    description: 'Licenseable',
  })
  @IsBoolean()
  @TransformToBoolean()
  @IsOptional()
  licenseable?: boolean;
}

export class GetFilterableSubjectsRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;
}

export class GetFilterableSchoolsRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;
}

export class GetFilterableStudentsRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;
}
