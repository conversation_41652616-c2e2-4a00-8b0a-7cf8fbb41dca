import { Grade, Subject } from '@goteacher/app/models/sequelize';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { createPaginationResponseDto } from '@goteacher/app/utility';

export const GetFilterableSchoolsResponseDto =
  createPaginationResponseDto<School>(School);

export const GetFilterableStudentsResponseDto =
  createPaginationResponseDto<User>(User);

export const GetFilterableGradesResponseDto =
  createPaginationResponseDto<Grade>(Grade);

export const GetFilterableSubjectsResponseDto =
  createPaginationResponseDto<Subject>(Subject);
