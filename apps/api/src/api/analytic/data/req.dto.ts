import {
  EnrichmentColumn,
  TimeGranularity,
  UserGroup,
} from '@goteacher/app/analytics';
import { AgreementStatus } from '@goteacher/app/analytics/service/enrichment.service';
import { ApprovalStatus, PaymentStatus } from '@goteacher/app/models/mongo';
import {
  IsComparable,
  IsRegexMatch,
  OrderByRequest,
  PaginationRequest,
  TransformToBoolean,
  sqlSanitizer,
} from '@goteacher/app/utility';
import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class EnrichmentDto {
  @ApiProperty({ isArray: true, enum: EnrichmentColumn })
  @IsArray()
  @ArrayMaxSize(30)
  @IsEnum(EnrichmentColumn, { each: true })
  enrichColumns: EnrichmentColumn[];
}

export class ColumnDto {
  @ApiProperty({
    description: 'This is the metric identifier',
    example: 'active_user_student',
  })
  @IsString()
  @IsRegexMatch(/^[_a-zA-Z][_a-zA-Z0-9$]*$/, {
    message: 'This is not a valid column identifier',
  })
  column: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum([
    'avg',
    'sum',
    'count',
    'min',
    'max',
    'uniq',
    'avgMerge',
    'sumMerge',
    'countMerge',
    'minMerge',
    'maxMerge',
    'uniqMerge',
  ])
  aggFunction?: string;
}

export class FilterDto {
  @ApiProperty()
  @IsString()
  @IsRegexMatch(/^[_a-zA-Z][_a-zA-Z0-9$]*$/, {
    message: 'This is not a valid column identifier',
  })
  column: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @IsEnum(['in', 'not in', '=', '<=', '>=', '<', '>', 'like', '!='])
  type?: string;

  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  value: unknown[];
}

export class JoinDto {
  @ApiProperty({
    example: 'time_spent_per_url_per_school',
  })
  @IsString()
  @IsRegexMatch(/^[_a-zA-Z][_a-zA-Z0-9$]*$/, {
    message: 'This is not a valid entity type identifier',
  })
  entityType: string;

  @ApiProperty()
  @IsEnum(['LEFT', 'RIGHT', 'INNER'])
  joinType: string;

  @ApiProperty({ type: [ColumnDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColumnDto)
  @ArrayMaxSize(30)
  columns: ColumnDto[];

  @ApiProperty({ isArray: true, type: String })
  @IsArray()
  @ArrayMaxSize(30)
  on: [string];
}

export class GetDataBodyDto extends PaginationRequest {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;

  @ApiProperty({
    example: 'time_spent_per_url_per_school',
    description: 'Table name',
    required: true,
  })
  @IsString()
  @IsRegexMatch(/^[_a-zA-Z][_a-zA-Z0-9$]*$/, {
    message: 'This is not a valid entity type identifier',
  })
  entityType: string;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({ type: [FilterDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterDto)
  @ArrayMaxSize(30)
  filters: FilterDto[];

  @ApiProperty({ type: [ColumnDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColumnDto)
  @ArrayMaxSize(30)
  columns: ColumnDto[];

  @ApiProperty({ type: [JoinDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JoinDto)
  @ArrayMaxSize(30)
  @IsOptional()
  join: JoinDto[];

  @ApiProperty({ type: [EnrichmentDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EnrichmentDto)
  @ArrayMaxSize(10)
  @IsOptional()
  enrichment: EnrichmentDto[];
}

export class TopUsersBodyDto extends PaginationRequest {
  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: true,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: true,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];
}

export class TopUsersCsvBodyDto extends OmitType(TopUsersBodyDto, [
  'timeGranularity',
  'limit',
  'offset',
  ]) {
  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.WEEKLY;
}

export class GetUserByBucketBodyDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    type: [String],
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  @IsArray()
  @ArrayMaxSize(100)
  @IsString({ each: true })
  @Type(() => String)
  domains: string[];

  @ApiProperty({
    required: false,
    description: 'Product IDs',
    type: [String],
  })
  @IsOptional()
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  @IsArray()
  @ArrayMaxSize(100)
  @IsString({ each: true })
  @Type(() => String)
  productIds?: string[];

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}

export class UsersAnalyticsBodyDto extends PaginationRequest {
  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];
}

export class UserDataBodyDto {
  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh?: boolean;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;
}

export class UserScreenTimeBodyDto {
  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh?: boolean;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;
}

export class UserUsageCategoriesBodyDto {
  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh?: boolean;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;
}

export class UserSessionsBodyDto {
  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh?: boolean;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;
}

export class GetActiveUsersBucketBodyDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.WEEKLY,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity;

  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}

export class GetSessionDurationsBodyDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.DAILY,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity;

  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}

export class GetInOutClassBodyDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.DAILY,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity;

  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}

export class GetActiveInactiveStudentsBodyDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.DAILY,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity;

  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    example: false,
    description: 'This is used to force refresh the cache',
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean;
}

export class GetStaticDataBodyDto {
  @ApiProperty({ description: 'Time range', enum: ['1W', '1M', '3M'] })
  @IsOptional()
  @IsEnum(['1W', '1M', '3M'])
  timeRange: '1W' | '1M' | '3M';

  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
  })
  @IsString()
  domain: string;
}

export class GetTopDomainsBodyDto extends PaginationRequest {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;

  @ApiProperty({
    required: false,
    enum: PaymentStatus,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  price?: PaymentStatus;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    required: false,
    enum: ApprovalStatus,
  })
  @IsOptional()
  @IsEnum(ApprovalStatus)
  approvalStatus?: ApprovalStatus;

  @ApiProperty({
    required: false,
    description: 'Risk level',
  })
  @IsOptional()
  @IsString()
  riskLevel?: string;

  @ApiProperty({
    required: false,
    description: 'Categories',
  })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  categories?: string[];

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
    enum: AgreementStatus,
    description: 'SDPC agreement status filter',
  })
  @IsOptional()
  @IsEnum(AgreementStatus)
  sdpcAgreementStatus?: AgreementStatus;
}

export class GetTopProductsBodyDto extends PaginationRequest {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;

  @ApiProperty({
    required: false,
    enum: PaymentStatus,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  price?: PaymentStatus;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    required: false,
    enum: ApprovalStatus,
  })
  @IsOptional()
  @IsEnum(ApprovalStatus)
  approvalStatus?: ApprovalStatus;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;
}

export class GetDomainDataBodyDto {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    example: ['google.com'],
    description: 'Platform',
    type: [String],
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  @IsArray()
  @ArrayMaxSize(100)
  @IsString({ each: true })
  @Type(() => String)
  domains: string[];

  @ApiProperty({
    required: false,
    description: 'Product IDs',
    type: [String],
  })
  @IsOptional()
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  @IsArray()
  @ArrayMaxSize(100)
  @IsString({ each: true })
  @Type(() => String)
  productIds?: string[];
}

export class TopSchoolsByDomainBodyDto extends PaginationRequest {
  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: true,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: true,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];
}

export class TopGradesByDomainBodyDto extends PaginationRequest {
  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: true,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: true,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];
}

export class GetTopUrlsByDomainBodyDto extends PaginationRequest {
  @ApiProperty({
    required: false,
  })
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;

  @ApiProperty({
    required: false,
    enum: PaymentStatus,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  price?: PaymentStatus;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;
}

export class GetTaggedYoutubeUrlsDto extends PaginationRequest {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(0)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  subjects?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(0)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  topics?: string[];

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;
}

export class GradesAnalyticsBodyDto {
  @ApiProperty({
    example: 'google.com',
    description: 'Platform',
    required: false,
  })
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsString()
  productId?: string;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
        typeof item === 'string' ? sqlSanitizer(item) : item,
      )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    required: false,
    description: 'Order by',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMaxSize(5)
  @Type(() => OrderByRequest)
  order?: OrderByRequest[];
}
