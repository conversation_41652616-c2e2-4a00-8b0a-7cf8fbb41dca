import {
  GetActiveInactiveStudentsQuery,
  GetActiveUsersBucketQuery,
  GetInOutClassQuery,
  GetTopProductsQuery,
  GetTopUsersCsvQuery,
  GetTopUsersQuery,
  GetUserByBucketQuery,
  GetUserUsageCategoriesQuery,
} from '@goteacher/app/analytics';
import { GetTopDomainsQuery } from '@goteacher/app/analytics/query/data/get-analytics-list-domains/query';
import { GetDataQuery } from '@goteacher/app/analytics/query/data/get-data/query';
import { GetDomainDataQuery } from '@goteacher/app/analytics/query/data/get-domain-data/query';
import { GetGradesAnalyticsQuery } from '@goteacher/app/analytics/query/data/get-grades-analytics/query';
import { GetSessionDurationsQuery } from '@goteacher/app/analytics/query/data/get-sessions-duration/query';
import { GetStaticDataQuery } from '@goteacher/app/analytics/query/data/get-static-data/query';
import { GetTaggedYoutubeDataQuery } from '@goteacher/app/analytics/query/data/get-tagged-youtube-data';
import { GetTopGradesByDomainQuery } from '@goteacher/app/analytics/query/data/get-top-grades-by-domain/query';
import { GetTopSchoolsByDomainQuery } from '@goteacher/app/analytics/query/data/get-top-schools-by-domain/query';
import { GetTopUrlsByDomainQuery } from '@goteacher/app/analytics/query/data/get-top-urls-by-domain/query';
import { GetUserData } from '@goteacher/app/analytics/query/data/get-user-data';
import { GetUserSessionsQuery } from '@goteacher/app/analytics/query/data/get-user-sessions';
import { GetUserTableQuery } from '@goteacher/app/analytics/query/data/get-user-table';
import { GetUserScreenTime } from '@goteacher/app/analytics/query/data/get-users-screen-time';
import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { RoleGuard, RoleProtected } from '@goteacher/app/auth/guard/role.guard';
import { TaggedYoutubeVideo } from '@goteacher/app/models/mongo';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import { PaginationResponse } from '@goteacher/app/utility';
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  GetActiveInactiveStudentsBodyDto,
  GetActiveUsersBucketBodyDto,
  GetDataBodyDto,
  GetDomainDataBodyDto,
  GetInOutClassBodyDto,
  GetSessionDurationsBodyDto,
  GetStaticDataBodyDto,
  GetTaggedYoutubeUrlsDto,
  GetTopDomainsBodyDto,
  GetTopProductsBodyDto,
  GetTopUrlsByDomainBodyDto,
  GetUserByBucketBodyDto,
  GradesAnalyticsBodyDto,
  TopGradesByDomainBodyDto,
  TopSchoolsByDomainBodyDto,
  TopUsersBodyDto,
  TopUsersCsvBodyDto,
  UserDataBodyDto,
  UsersAnalyticsBodyDto,
  UserScreenTimeBodyDto,
  UserSessionsBodyDto,
  UserUsageCategoriesBodyDto,
} from 'apps/api/src/api/analytic/data/req.dto';
import {
  ActiveInactiveStudentsResponse,
  ActiveUserByBucketResponse,
  InOutClassResponse,
  SessionsDurationResponse,
  StaticDataResponseDto,
  TopGradesByDomainResponse,
  TopSchoolsByDomainResponse,
  TopUsersResponseDto,
  UserByBucketResponse,
} from 'apps/api/src/api/analytic/data/res.dto';
import { FastifyReply } from 'fastify';

@ApiSecurity('bearer')
@ApiTags('analytic')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal Server Error' })
@UseGuards(JWTGuard, RoleGuard)
@Controller('analytic/data')
@RoleProtected([UserRole.ADMINISTRATOR, UserRole.TEACHER])
export class AnalyticDataController {
  constructor(private readonly queryBus: QueryBus) { }

  @HttpCode(HttpStatus.OK)
  @Post('/top-domains')
  async getTopDomains(
    @Body() body: GetTopDomainsBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetTopDomainsQuery({ ctx, ...body }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @Post('/top-products')
  async getTopProducts(
    @Body() body: GetTopProductsBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetTopProductsQuery({ ctx, ...body }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @Post('/top-urls')
  async getTopUrls(
    @Body() body: GetTopUrlsByDomainBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetTopUrlsByDomainQuery({ ctx, ...body }),
    );
  }

  @Post()
  @ApiOkResponse({
    description: 'Analytic data',
    type: PaginationResponse<unknown>,
  })
  @ApiOperation({ summary: 'Get analytic data with custom query' })
  async getData(@Body() body: GetDataBodyDto, @ReqContext() ctx: ReqContext) {
    return await this.queryBus.execute(new GetDataQuery({ ...body, ctx }));
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Top users',
    type: TopUsersResponseDto,
  })
  @Post('top-users')
  @ApiOperation({ summary: 'Get top users' })
  async getTopUsers(
    @Body() body: TopUsersBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(new GetTopUsersQuery({ ...body, ctx }));
  }

  @Post('static-data')
  @ApiOkResponse({
    description: 'Static data',
    type: [StaticDataResponseDto],
  })
  @ApiOperation({ summary: 'Get static data' })
  async getStaticData(
    @ReqContext() ctx: ReqContext,
    @Body() body: GetStaticDataBodyDto,
  ) {
    return await this.queryBus.execute(
      new GetStaticDataQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'User by engagement bucket',
    type: [UserByBucketResponse],
  })
  @Post('user-by-bucket')
  @ApiOperation({ summary: 'Get user by engagement bucket' })
  async getUserByBucket(
    @ReqContext() ctx: ReqContext,
    @Body() body: GetUserByBucketBodyDto,
  ): Promise<any> {
    return await this.queryBus.execute(
      new GetUserByBucketQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @Post('active-user-bucket')
  @ApiOkResponse({
    description: 'Active user by engagement bucket',
    type: [ActiveUserByBucketResponse],
  })
  @ApiOperation({ summary: 'Get active user by engagement bucket' })
  async getActiveUserBucket(
    @ReqContext() ctx: ReqContext,
    @Body() body: GetActiveUsersBucketBodyDto,
  ) {
    return await this.queryBus.execute(
      new GetActiveUsersBucketQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Session durations',
    type: [SessionsDurationResponse],
  })
  @Post('session-durations')
  @ApiOperation({ summary: 'Get session durations metrics' })
  async getSessionDurations(
    @ReqContext() ctx: ReqContext,
    @Body() body: GetSessionDurationsBodyDto,
  ) {
    return await this.queryBus.execute(
      new GetSessionDurationsQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'In-out class',
    type: [InOutClassResponse],
  })
  @Post('in-out-class')
  @ApiOperation({ summary: 'Get in-out class metrics' })
  async getInOutClass(
    @ReqContext() ctx: ReqContext,
    @Body() body: GetInOutClassBodyDto,
  ) {
    return await this.queryBus.execute(
      new GetInOutClassQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Active-inactive students',
    type: [ActiveInactiveStudentsResponse],
  })
  @Post('active-inactive-students')
  @ApiOperation({ summary: 'Get active-inactive student metrics' })
  async getActiveInactiveStudents(
    @ReqContext() ctx: ReqContext,
    @Body() body: GetActiveInactiveStudentsBodyDto,
  ) {
    return await this.queryBus.execute(
      new GetActiveInactiveStudentsQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @Post('domain')
  async getDomainData(
    @ReqContext() ctx: ReqContext,
    @Body() body: GetDomainDataBodyDto,
  ) {
    return await this.queryBus.execute(
      new GetDomainDataQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Top schools by domain',
    type: TopSchoolsByDomainResponse,
  })
  @Post('top-schools-by-domain')
  @ApiOperation({ summary: 'Get top schools' })
  async getTopSchoolsByDomain(
    @Body() body: TopSchoolsByDomainBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetTopSchoolsByDomainQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Top grades by domain',
    type: TopGradesByDomainResponse,
  })
  @Post('top-grades-by-domain')
  @ApiOperation({ summary: 'Get top grades' })
  async getTopGradesByDomain(
    @Body() body: TopGradesByDomainBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetTopGradesByDomainQuery({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Users Table',
  })
  @Post('users')
  @ApiOperation({ summary: 'Get analytics for users' })
  async getUsersAnalytics(
    @Body() body: UsersAnalyticsBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(new GetUserTableQuery({ ...body, ctx }));
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'User data',
  })
  @Post('user-data')
  @ApiOperation({ summary: 'Get user data' })
  async getUserData(
    @Body() body: UserDataBodyDto,
    @ReqContext() ctx: ReqContext,
    @Query('userId') userId: string,
  ) {
    return await this.queryBus.execute(
      new GetUserData({ ...body, ctx, userId }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'User screen time',
  })
  @Post('user-screen-time')
  @ApiOperation({ summary: 'Get user screen time' })
  async getUserScreenTime(
    @Body() body: UserScreenTimeBodyDto,
    @ReqContext() ctx: ReqContext,
    @Query('userId') userId: string,
  ) {
    return await this.queryBus.execute(
      new GetUserScreenTime({ ...body, ctx, userId }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'User usage categories',
  })
  @Post('user-usage-categories')
  @ApiOperation({ summary: 'Get user usage categories' })
  async getUserUsageCategories(
    @Body() body: UserUsageCategoriesBodyDto,
    @ReqContext() ctx: ReqContext,
    @Query('userId') userId: string,
  ) {
    return await this.queryBus.execute(
      new GetUserUsageCategoriesQuery({ ...body, ctx, userId }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'User sessions',
  })
  @Post('user-sessions')
  @ApiOperation({ summary: 'Get user sessions' })
  async getUserSessions(
    @Body() body: UserSessionsBodyDto,
    @ReqContext() ctx: ReqContext,
    @Query('userId') userId: string,
  ) {
    return await this.queryBus.execute(
      new GetUserSessionsQuery({ ...body, ctx, userId }),
    );
  }

  @Post('users-csv')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({})
  @ApiNotFoundResponse()
  async getPlatformUsersCsv(
    @Body() body: TopUsersCsvBodyDto,
    @ReqContext() ctx: ReqContext,
    @Res({ passthrough: true }) res: FastifyReply,
  ) {
    const csvBuffer = await this.queryBus.execute(
      new GetTopUsersCsvQuery({
        ...body,
        ctx,
      }),
    );

    res.header('Content-Type', 'text/csv');
    res.header(
      'Content-Disposition',
      `attachment; filename="contract-users-${body.domain || body.productId}-${new Date().toISOString().split('T')[0]}.csv"`,
    );

    return csvBuffer;
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Tagged youtube data',
    type: [TaggedYoutubeVideo],
  })
  @Post('tagged-youtube-data')
  @ApiOperation({ summary: 'Get tagged youtube data' })
  async getTaggedYoutubeData(
    @Body() query: GetTaggedYoutubeUrlsDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetTaggedYoutubeDataQuery({ ctx, ...query }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Grades Analytics',
  })
  @Post('grades')
  @ApiOperation({ summary: 'Get analytics for grades' })
  async getGradesAnalytics(
    @Body() body: GradesAnalyticsBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(
      new GetGradesAnalyticsQuery({ ...body, ctx }),
    );
  }
}
