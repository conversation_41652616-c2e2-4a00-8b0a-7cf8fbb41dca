import { createPaginationResponseDto } from '@goteacher/app/utility';
import { ApiProperty } from '@nestjs/swagger';

export class UserEnrichmentDetails {
  @ApiProperty({
    description: 'User ID',
    type: String,
  })
  id: string;

  @ApiProperty({
    description: 'Email',
    type: String,
  })
  email: string;

  @ApiProperty({
    description: 'First name',
    type: String,
  })
  firstName: string;

  @ApiProperty({
    description: 'Last name',
    type: String,
  })
  lastName: string;

  @ApiProperty({
    description: 'Picture',
    type: String,
  })
  picture: string;
}

export class TopUsersResponse {
  @ApiProperty({
    description: 'Day, only available for time granularity daily or weekly',
    type: Date,
    required: false,
  })
  day?: Date;

  @ApiProperty({
    description: 'User ID',
    type: String,
  })
  userId: string;

  @ApiProperty({
    description: 'User details',
    type: UserEnrichmentDetails,
  })
  user_details: UserEnrichmentDetails;

  @ApiProperty({
    description: 'Page views',
    type: Number,
  })
  page_views: number;

  @ApiProperty({
    description: 'Count sessions',
    type: Number,
  })
  count_sessions: number;

  @ApiProperty({
    description: 'Average time spent',
    type: Number,
  })
  avg_time_spent: number;

  @ApiProperty({
    description: 'Sum time spent',
    type: Number,
  })
  sum_time_spent: number;
}

export const TopUsersResponseDto =
  createPaginationResponseDto<TopUsersResponse>(TopUsersResponse);

export class UserByBucketDto {
  @ApiProperty({
    description: 'Organization ID',
    type: String,
  })
  orgId: string;

  @ApiProperty({
    description: 'Domain',
    type: String,
  })
  domain: string;

  @ApiProperty({
    description: 'Unique users spent 20 minutes',
    type: Number,
  })
  uniq_user_spent_20: number;

  @ApiProperty({
    description: 'Unique users spent 40 minutes',
    type: Number,
  })
  uniq_user_spent_40: number;

  @ApiProperty({
    description: 'Unique users spent 60 minutes',
    type: Number,
  })
  uniq_user_spent_60: number;

  @ApiProperty({
    description: 'Unique users spent 120 minutes',
    type: Number,
  })
  uniq_user_spent_120: number;

  @ApiProperty({
    description: 'Unique users spent 120+ minutes',
    type: Number,
  })
  uniq_user_spent_120_plus: number;

  @ApiProperty({
    description: 'No login users',
    type: Number,
  })
  no_login: number;
}

export class UserByBucketResponse {
  @ApiProperty({
    description: 'User by engagement bucket data',
    type: [UserByBucketDto],
  })
  data: UserByBucketDto[];
}

export class ActiveUserByBucketDto {
  @ApiProperty({
    description: 'Organization ID',
    type: String,
  })
  orgId: string;

  @ApiProperty({
    description: 'Domain',
    type: String,
  })
  domain: string;

  @ApiProperty({
    description: 'Day, only available for time granularity daily or weekly',
    type: Date,
  })
  day?: Date;

  @ApiProperty({
    description: 'Unique users spent 20 minutes',
    type: Number,
  })
  uniq_user_spent_20: number;

  @ApiProperty({
    description: 'Unique users spent 40 minutes',
    type: Number,
  })
  uniq_user_spent_40: number;

  @ApiProperty({
    description: 'Unique users spent 60 minutes',
    type: Number,
  })
  uniq_user_spent_60: number;

  @ApiProperty({
    description: 'Unique users spent 120 minutes',
    type: Number,
  })
  uniq_user_spent_120: number;

  @ApiProperty({
    description: 'Unique users spent 120+ minutes',
    type: Number,
  })
  uniq_user_spent_120_plus: number;

  @ApiProperty({
    description: 'No login users',
    type: Number,
  })
  no_login: number;
}

export class ActiveUserByBucketResponse {
  @ApiProperty({
    description: 'Active user by engagement bucket data',
    type: [ActiveUserByBucketDto],
  })
  data: ActiveUserByBucketDto[];
}

export class SessionsDuration {
  @ApiProperty({
    description: 'Day, only available for time granularity daily or weekly',
    type: Date,
  })
  day?: Date;

  @ApiProperty({
    description: 'Count sessions of type 5',
    type: Number,
  })
  count_sessions_5: number;

  @ApiProperty({
    description: 'Count sessions of type 10',
    type: Number,
  })
  count_sessions_10: number;

  @ApiProperty({
    description: 'Count sessions of type 20',
    type: Number,
  })
  count_sessions_20: number;

  @ApiProperty({
    description: 'Count sessions of type 20+',
    type: Number,
  })
  count_sessions_20_plus: number;
}

export class SessionsDurationResponse {
  @ApiProperty({
    description: 'Sessions duration data',
    type: [SessionsDuration],
  })
  data: SessionsDuration[];
}

export class InOutClassDto {
  @ApiProperty({
    description: 'Day, only available for time granularity daily or weekly',
    type: Date,
  })
  day?: Date;

  @ApiProperty({
    description: 'Total time spent',
    type: Number,
  })
  total: number;

  @ApiProperty({
    description: 'Time spent in class',
    type: Number,
  })
  in_class: number;

  @ApiProperty({
    description: 'Time spent out of class',
    type: Number,
  })
  out_class: number;
}

export class InOutClassResponse {
  @ApiProperty({
    description: 'In-out class data',
    type: [InOutClassDto],
  })
  data: InOutClassDto[];
}

export class ActiveInactiveStudentsDto {
  @ApiProperty({
    description: 'Day, only available for time granularity daily or weekly',
    type: Date,
  })
  day?: Date;

  @ApiProperty({
    description: 'Active students',
    type: Number,
  })
  active_students: number;

  @ApiProperty({
    description: 'Inactive students',
    type: Number,
  })
  inactive_students: number;

  @ApiProperty({
    description: 'Total students',
    type: Number,
  })
  total_students: number;
}

export class ActiveInactiveStudentsResponse {
  @ApiProperty({
    description: 'Active-inactive students data',
    type: [ActiveInactiveStudentsDto],
  })
  data: ActiveInactiveStudentsDto[];
}

export class StaticDataResponseDto {
  @ApiProperty({
    description: 'Static data',
  })
  data: Record<string, unknown>;
}

export class SchoolEnrichmentDetails {
  @ApiProperty({
    description: 'School ID',
    type: String,
  })
  id: string;

  @ApiProperty({
    description: 'Name',
    type: String,
  })
  name: string;

  @ApiProperty({
    description: 'Display name',
    type: String,
  })
  displayName: string;
}

export class TopSchoolsByDomainResponse {
  @ApiProperty({
    description: 'Day, only available for time granularity daily or weekly',
    type: Date,
    required: false,
  })
  day?: Date;

  @ApiProperty({
    description: 'School ID',
    type: String,
  })
  schoolId: string;

  @ApiProperty({
    description: 'School details',
    type: SchoolEnrichmentDetails,
  })
  school_details: SchoolEnrichmentDetails;

  @ApiProperty({
    description: 'School user count',
    type: Number,
  })
  school_user_count: number;

  @ApiProperty({
    description: 'Page views',
    type: Number,
  })
  page_views: number;

  @ApiProperty({
    description: 'Count sessions',
    type: Number,
  })
  count_sessions: number;

  @ApiProperty({
    description: 'Average time spent',
    type: Number,
  })
  avg_time_spent: number;

  @ApiProperty({
    description: 'Sum time spent',
    type: Number,
  })
  sum_time_spent: number;
}

export const TopSchoolsByDomainResponseDto =
  createPaginationResponseDto<TopSchoolsByDomainResponse>(
    TopSchoolsByDomainResponse,
  );

export class GradeEnrichmentDetails {
  @ApiProperty({
    description: 'Grade ID',
    type: String,
  })
  id: string;

  @ApiProperty({
    description: 'Name',
    type: String,
  })
  name: string;

  @ApiProperty({
    description: 'Value',
    type: String,
  })
  value: string;
}

export class TopGradesByDomainResponse {
  @ApiProperty({
    description: 'Day, only available for time granularity daily or weekly',
    type: Date,
    required: false,
  })
  day?: Date;

  @ApiProperty({
    description: 'Grade',
    type: String,
  })
  grade: string;

  @ApiProperty({
    description: 'Grade details',
    type: GradeEnrichmentDetails,
  })
  grade_details: GradeEnrichmentDetails;

  @ApiProperty({
    description: 'Grade user count',
    type: Number,
  })
  grade_user_count: number;

  @ApiProperty({
    description: 'Page views',
    type: Number,
  })
  page_views: number;

  @ApiProperty({
    description: 'Count sessions',
    type: Number,
  })
  count_sessions: number;

  @ApiProperty({
    description: 'Average time spent',
    type: Number,
  })
  avg_time_spent: number;

  @ApiProperty({
    description: 'Sum time spent',
    type: Number,
  })
  sum_time_spent: number;
}

export const TopGradesByDomainResponseDto =
  createPaginationResponseDto<TopGradesByDomainResponse>(
    TopGradesByDomainResponse,
  );
