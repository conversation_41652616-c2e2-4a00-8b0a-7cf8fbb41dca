import { ApiProperty } from '@nestjs/swagger';
import { createPaginationResponseDto } from '@goteacher/app/utility';

export class OAuthAppDto {
  @ApiProperty({
    description: 'Organisation ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  organisationId: string;

  @ApiProperty({
    description: 'Application name',
    example: 'Google Drive',
  })
  appName: string;

  @ApiProperty({
    description: 'OAuth client ID',
    example: '123456789.apps.googleusercontent.com',
  })
  clientId: string;

  @ApiProperty({
    description: 'Number of users with access',
    example: 150,
  })
  userCount: number;

  @ApiProperty({
    description: 'List of OAuth scopes',
    example: [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/userinfo.email',
    ],
    type: [String],
  })
  scopes: string[];

  @ApiProperty({
    description: 'Last time the app was accessed',
    example: '2024-01-15T10:30:00Z',
    type: Date,
    nullable: true,
  })
  lastAccessed: Date | null;

  @ApiProperty({
    description: 'Created timestamp',
    example: '2024-01-01T00:00:00Z',
    type: Date,
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Updated timestamp',
    example: '2024-01-15T12:00:00Z',
    type: Date,
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Domain name of the OAuth application',
    example: 'google.com',
    type: String,
    nullable: true,
  })
  domain: string | null;

  @ApiProperty({
    description: 'Manually overridden SDPC Agreement ID',
    example: '195',
    type: String,
    nullable: true,
  })
  overriddenSdpcAgreementId: string | null;

  @ApiProperty({
    description: 'Indicates if the SDPC agreement is manually overridden',
    example: false,
    type: Boolean,
    nullable: true,
  })
  isManuallyOverridden?: boolean;
}

export const GetOAuthAppsResponseDto = createPaginationResponseDto(OAuthAppDto);