import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { OAuthAppsController } from './oauth-apps.controller';
import { UpdateOAuthAppDomainHandler, UpdateOAuthAppSdpcHandler } from '@goteacher/app/oauth-apps';
import { GetOAuthAppsHandler } from '@goteacher/app/oauth-apps/query/get-oauth-apps/handler';
import { GetOAuthAppByIdHandler } from '@goteacher/app/oauth-apps/query/get-oauth-app-by-id/handler';
import { MongooseModelModule } from '@goteacher/app/models/mongo';

@Module({
  imports: [CqrsModule, MongooseModelModule],
  controllers: [OAuthAppsController],
  providers: [
    GetOAuthAppsHandler, 
    GetOAuthAppByIdHandler,
    UpdateOAuthAppDomainHandler,
    UpdateOAuthAppSdpcHandler
  ],
})
export class OAuthAppsModule {}