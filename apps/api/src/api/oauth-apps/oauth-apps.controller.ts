import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { GetOAuthAppByIdQuery, GetOAuthAppsQuery, UpdateOAuthAppDomainCommand, UpdateOAuthAppSdpcCommand } from '@goteacher/app/oauth-apps';
import { PaginationResponse } from '@goteacher/app/utility';
import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { GetOAuthAppsRequestDto, UpdateOAuthAppDomainRequestDto, UpdateOAuthAppSdpcRequestDto } from './req.dto';
import { GetOAuthAppsResponseDto, OAuthAppDto } from './res.dto';

@ApiTags('oauth-apps')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@UseGuards(JWTGuard)
@Controller('oauth-apps')
export class OAuthAppsController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ 
    type: GetOAuthAppsResponseDto,
    description: 'List of OAuth applications with pagination',
  })
  @ApiOperation({ 
    summary: 'Get OAuth applications for the organization',
    description: 'Retrieves OAuth applications installed in the organization with optional filtering and pagination',
  })
  async getOAuthApps(
    @Query() query: GetOAuthAppsRequestDto,
    @ReqContext() ctx: ReqContext,
  ): Promise<PaginationResponse<OAuthAppDto>> {
    const userOrganizationIds = ctx.user.UserSchool?.map(
      (userSchool) => userSchool.school.organisationId,
    ) || [];

    if (userOrganizationIds.length === 0) {
      return {
        data: [],
        total: 0,
        limit: query.limit,
        offset: query.offset,
      };
    }

    const organisationId = userOrganizationIds[0];

    return await this.queryBus.execute(
      new GetOAuthAppsQuery(
        organisationId,
        // ctx.sdpcDistrictId || '',
        '9139',
        {
          offset: query.offset,
          limit: query.limit,
          order: query.order,
        },
        {
          appName: query.appName,
          clientId: query.clientId,
        },
      ),
    );
  }

  @Get(':clientId')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ 
    type: OAuthAppDto,
    description: 'OAuth application details',
  })
  @ApiOperation({ 
    summary: 'Get OAuth application by ID',
    description: 'Retrieves a specific OAuth application by client ID',
  })
  async getOAuthAppById(
    @Param('clientId') clientId: string,
    @ReqContext() ctx: ReqContext,
  ): Promise<OAuthAppDto> {
    const userOrganizationIds = ctx.user.UserSchool?.map(
      (userSchool) => userSchool.school.organisationId,
    ) || [];

    if (userOrganizationIds.length === 0) {
      throw new ForbiddenException('User not associated with any organization');
    }

    const organisationId = userOrganizationIds[0];
    
    return await this.queryBus.execute(
      new GetOAuthAppByIdQuery(
        organisationId,
        clientId,
        ctx.sdpcDistrictId || '',
      ),
    );
  }

  @Put(':clientId/domain')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ 
    type: OAuthAppDto,
    description: 'OAuth app domain updated successfully',
  })
  @ApiOperation({ 
    summary: 'Update OAuth app domain',
    description: 'Updates the domain for a specific OAuth application',
  })
  async updateOAuthAppDomain(
    @Param('clientId') clientId: string,
    @Body() body: UpdateOAuthAppDomainRequestDto,
    @ReqContext() ctx: ReqContext,
  ): Promise<OAuthAppDto> {
    const userOrganizationIds = ctx.user.UserSchool?.map(
      (userSchool) => userSchool.school.organisationId,
    ) || [];

    if (userOrganizationIds.length === 0) {
      throw new ForbiddenException('User not associated with any organization');
    }

    const organisationId = userOrganizationIds[0];
    
    return await this.commandBus.execute(
      new UpdateOAuthAppDomainCommand({
        organisationId,
        clientId,
        domain: body.domain,
      }),
    );
  }

  @Put(':clientId/sdpc')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ 
    type: OAuthAppDto,
    description: 'OAuth app SDPC agreement override updated successfully',
  })
  @ApiOperation({ 
    summary: 'Override OAuth app SDPC agreement',
    description: 'Manually override the SDPC agreement for a specific OAuth application. Pass null to remove the override.',
  })
  async updateOAuthAppSdpc(
    @Param('clientId') clientId: string,
    @Body() body: UpdateOAuthAppSdpcRequestDto,
    @ReqContext() ctx: ReqContext,
  ): Promise<OAuthAppDto> {
    const userOrganizationIds = ctx.user.UserSchool?.map(
      (userSchool) => userSchool.school.organisationId,
    ) || [];

    if (userOrganizationIds.length === 0) {
      throw new ForbiddenException('User not associated with any organization');
    }

    const organisationId = userOrganizationIds[0];
    
    return await this.commandBus.execute(
      new UpdateOAuthAppSdpcCommand({
        organisationId,
        clientId,
        sdpcAgreementId: body.sdpcAgreementId || null,
        districtId: ctx.sdpcDistrictId || '',
      }),
    );
  }
}