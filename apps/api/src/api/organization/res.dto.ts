import { ApiProperty } from '@nestjs/swagger';

export class ImportOrganizationResponseDto {
  @ApiProperty({
    example: true,
    description: 'Import success status',
  })
  success: boolean;

  @ApiProperty({
    example: 'Successfully imported 25 users, 3 decommissioned (not in CSV), 2 decommissioned (changes)',
    description: 'Import result message',
  })
  message: string;

  @ApiProperty({
    example: '2024_2025',
    description: 'School year calculated for this import',
  })
  schoolYear: string;

  @ApiProperty({
    example: true,
    description: 'Whether this was the first import for this organization/school year/type',
  })
  isFirstImport: boolean;

  @ApiProperty({
    example: 25,
    description: 'Number of users imported',
  })
  importedCount: number;

  @ApiProperty({
    example: 2,
    description: 'Number of users that failed to import',
  })
  failedCount: number;

  @ApiProperty({
    example: 3,
    description: 'Number of users decommissioned (not found in CSV)',
  })
  decommissionedCount: number;

  @ApiProperty({
    example: 2,
    description: 'Number of users decommissioned due to grade/school changes',
  })
  decommissionedForChangesCount: number;

  @ApiProperty({
    example: ['Row 5: Invalid email format', 'Row 12: Missing required field'],
    description: 'List of errors encountered during import',
    required: false,
  })
  errors?: string[];

  @ApiProperty({
    example: ['<EMAIL>', '<EMAIL>'],
    description: 'List of users decommissioned (not found in CSV)',
    required: false,
  })
  decommissionedUsers?: string[];

  @ApiProperty({
    example: ['<EMAIL> (grade)', '<EMAIL> (school)'],
    description: 'List of users decommissioned due to changes with reason',
    required: false,
  })
  decommissionedForChanges?: string[];
}