import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { RoleGuard, RoleProtected } from '@goteacher/app/auth/guard/role.guard';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import { ImportOrganizationCsvCommand } from '@goteacher/app/organization/command/import-organization-csv';
import { GCSService } from '@goteacher/infra/storage/gcs.service';
import {
  Controller,
  ForbiddenException,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import {
  ApiBody,
  ApiConsumes,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import axios from 'axios';
import { FastifyRequest } from 'fastify';
import { ImportOrganizationResponseDto } from './res.dto';

@ApiTags('organization')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiInternalServerErrorResponse({ description: 'Internal Server Error' })
@UseGuards(JWTGuard, RoleGuard)
@Controller('organization')
@RoleProtected([UserRole.ADMINISTRATOR])
export class OrganizationController {
  constructor(
    private commandBus: CommandBus,
    private gcsService: GCSService,
  ) {}



  @ApiOkResponse({
    description: 'Organizations imported successfully',
    type: ImportOrganizationResponseDto,
  })
  @ApiOperation({ summary: 'Import organizations from CSV' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        org_name: { type: 'string', description: 'Organization name' },
        org_domain: { type: 'string', description: 'Organization domain' },
        org_state: { type: 'string', description: 'Organization state' },
        type: {
          type: 'string',
          enum: ['student', 'staff'],
          description: 'Type of users to import'
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'CSV file with columns: First, Last, Email, Grade, School',
        },
        csv_link: {
          type: 'string',
          format: 'uri',
          description: 'URL to download CSV file from (alternative to file upload)',
        },
      },
      required: ['org_name', 'org_domain', 'org_state', 'type'],
    },
  })
  @Post('import')
  @HttpCode(HttpStatus.OK)
  async importOrganizations(
    @Req() request: FastifyRequest,
    @ReqContext() ctx: ReqContext,
  ): Promise<ImportOrganizationResponseDto> {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];
    if (!orgId) {
      throw new ForbiddenException(
        'User not associated with any organisation!',
      );
    }

    const parts = request.parts();
    let fileBuffer: Buffer;
    let fileName: string;
    const fields: any = {};

    for await (const part of parts) {
      if (part.type === 'file') {
        fileBuffer = await part.toBuffer();
        fileName = part.filename;
      } else {
        fields[part.fieldname] = part.value;
      }
    }

    // validate that we have either file or csv_link, but not both
    if (!fileBuffer && !fields.csv_link) {
      throw new ForbiddenException('Either file or csv_link is required');
    }

    if (fileBuffer && fields.csv_link) {
      throw new ForbiddenException('Provide either file or csv_link, not both');
    }

    if (!fields.type || !['student', 'staff'].includes(fields.type)) {
      throw new ForbiddenException('Invalid type. Must be either "student" or "staff"');
    }

    if (fields.csv_link) {
      try {
        const url = new URL(fields.csv_link);
        
        if (!['http:', 'https:'].includes(url.protocol)) {
          throw new ForbiddenException('Only HTTP/HTTPS URLs are allowed');
        }

        console.log(`Downloading CSV from URL: ${fields.csv_link}`);
        
        // custom for google storage 
        if (url.hostname === 'storage.googleapis.com') {
          const pathParts = url.pathname.substring(1).split('/');
          const bucketName = pathParts[0];
          const filePath = pathParts.slice(1).join('/');
          
          console.log(`Downloading from GCS bucket: ${bucketName}, path: ${filePath}`);
          
          try {
            fileBuffer = await this.gcsService.downloadFileFromGCSUrlWithToken(fields.csv_link);
            fileName = filePath.split('/').pop() || 'imported.csv';
            
            console.log(`Successfully downloaded CSV from GCS using token auth: ${fileName} (${fileBuffer.length} bytes)`);

          } catch (gcsError) {
            if (gcsError instanceof ForbiddenException) {
              throw gcsError;
            }else {
              throw new ForbiddenException(gcsError.message);
            } 
          }
        } else {
          const response = await axios.get(fields.csv_link, {
            responseType: 'arraybuffer',
            timeout: 30000,
            maxContentLength: 50 * 1024 * 1024,
            headers: {
              'Accept': 'text/csv, application/csv, text/plain'
            }
          });

          const contentType = response.headers['content-type'];
          if (contentType && !contentType.includes('csv') && !contentType.includes('text/plain') && !contentType.includes('comma-separated')) {
            throw new ForbiddenException('URL must point to a CSV file');
          }

          fileBuffer = Buffer.from(response.data);
          fileName = fields.csv_link.split('/').pop()?.split('?')[0] || 'imported.csv';
          
          console.log(`Successfully downloaded CSV file: ${fileName} (${fileBuffer.length} bytes)`);
        }
        
      } catch (error) {
        if (error instanceof ForbiddenException) {
          throw error;
        }
        
        if (axios.isAxiosError(error)) {
          if (error.code === 'ECONNABORTED') {
            throw new ForbiddenException('CSV download timed out');
          }
          if (error.response?.status === 404) {
            throw new ForbiddenException('CSV file not found at the provided URL');
          }
          if (error.response?.status >= 500) {
            throw new ForbiddenException('Server error while downloading CSV');
          }
          if (error.response?.status === 403) {
            throw new ForbiddenException('Access denied to the CSV file');
          }
          if (error.response?.status === 401) {
            throw new ForbiddenException('Authentication required to access the CSV file');
          }
        }
        
        console.error('Error downloading CSV:', error);
        throw new ForbiddenException(`Failed to download CSV: ${error.message}`);
      }
    }


    const result = await this.commandBus.execute(
      new ImportOrganizationCsvCommand({
        orgName: fields.org_name,
        orgDomain: fields.org_domain,
        orgState: fields.org_state,
        type: fields.type as 'student' | 'staff',
        csvBuffer: fileBuffer,
        fileName,
        orgId,
      }),
    );

    return result;
  }
}