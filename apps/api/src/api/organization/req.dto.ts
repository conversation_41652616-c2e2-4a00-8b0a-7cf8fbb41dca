import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class ImportOrganizationRequestDto {
  @ApiProperty({
    example: 'Springfield School District',
    required: true,
    description: 'Organization name',
  })
  @IsString()
  org_name: string;

  @ApiProperty({
    example: 'springfield.edu',
    required: true,
    description: 'Organization domain',
  })
  @IsString()
  org_domain: string;

  @ApiProperty({
    example: 'IL',
    required: true,
    description: 'Organization state (for filtering/reporting purposes)',
  })
  @IsString()
  org_state: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'CSV file with columns: First, Last, Email, Grade, School',
  })
  file: any;
}