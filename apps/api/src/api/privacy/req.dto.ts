import { PaginationRequest } from '@goteacher/app/utility';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';

export class GetAgreementsRequestDto {
  @ApiProperty({
    description: 'Organization/District ID',
    example: '757',
  })
  @IsString()
  @IsNotEmpty()
  organizationId: string;
}

export class GetAllAgreementsDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term for company or software name or agreement name',
  })
  @IsOptional()
  @IsString()
  search?: string;
}
