import { SDPCAgreement } from '@goteacher/app/models/mongo/sdpc.model';
import { ApiProperty } from '@nestjs/swagger';

export class PaginationDto {
  @ApiProperty({
    description: 'Total number of items',
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
  })
  pages: number;
}

export class GetAgreementsResponseDto {
  @ApiProperty({
    type: [SDPCAgreement],
    description: 'List of SDPC agreements',
  })
  agreements: SDPCAgreement[];

  @ApiProperty({
    type: PaginationDto,
    description: 'Pagination information',
  })
  pagination: PaginationDto;
}
