import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { GetAgreementQuery } from '@goteacher/app/privacy/query/get-agreement/query';
import { GetAgreementsQuery } from '@goteacher/app/privacy/query/get-agreements';
import { GetAllAgreementsQuery } from '@goteacher/app/privacy/query/get-all-agreements';
import { GetCompaniesQuery } from '@goteacher/app/privacy/query/get-companies';
import { GetSoftwareQuery } from '@goteacher/app/privacy/query/get-software';
import { RequestConnectionQuery } from '@goteacher/app/privacy/query/request-connection';
import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiForbiddenResponse, ApiInternalServerErrorResponse, ApiOperation, ApiQuery, ApiResponse, ApiSecurity, ApiTags, ApiUnauthorizedResponse } from '@nestjs/swagger';
import { GetAllAgreementsDto } from './req.dto';
import { GetAgreementsResponseDto } from './res.dto';

@ApiTags('Privacy')
@Controller('privacy')
export class PrivacyController {
  constructor(private readonly queryBus: QueryBus) { }

  @ApiSecurity('bearer')
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @ApiInternalServerErrorResponse({ description: 'Internal Server Error' })
  @UseGuards(JWTGuard)
  @Get('request-connection')
  @ApiOperation({
    summary: 'Request connection to the privacy API',
  })
  async requestConnection(@ReqContext() ctx: ReqContext) {
    return this.queryBus.execute(new RequestConnectionQuery({ ctx }));
  }

  @Get('agreements/:organizationId')
  @ApiOperation({
    summary: 'Get all agreements for an organization',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all public agreements for the organization',
    type: GetAgreementsResponseDto,
  })
  async getAllAgreements(
    @Param('organizationId') organizationId: string,
    @Query() params: GetAllAgreementsDto,
  ) {
    return this.queryBus.execute(
      new GetAllAgreementsQuery({ organizationId, ...params }),
    );
  }

  @Get('companies/:organizationId')
  @ApiOperation({
    summary: 'Get all companies with agreements for an organization',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all companies with their agreement counts',
  })
  async getCompaniesByOrganization(
    @Param('organizationId') organizationId: string,
  ) {
    return this.queryBus.execute(new GetCompaniesQuery(organizationId));
  }

  @Get('software/:organizationId')
  @ApiOperation({
    summary: 'Get all Products for an organization',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all Products with their agreement counts',
  })
  @ApiQuery({
    name: 'companyName',
    required: false,
    description: 'Optional company name filter',
    type: String,
  })
  async getSoftwareByCompany(
    @Param('organizationId') organizationId: string,
    @Query('companyName') companyName?: string,
  ) {
    return this.queryBus.execute(
      new GetSoftwareQuery(organizationId, companyName),
    );
  }

  @Get('agreements/:organizationId/:softwareId')
  @ApiOperation({
    summary: 'Get all agreements for a software in an organization',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all agreements for the specified software',
    type: GetAgreementsResponseDto,
  })
  async getAgreementsBySoftware(
    @Param('organizationId') organizationId: string,
    @Param('softwareId') softwareId: string,
  ) {
    return this.queryBus.execute(
      new GetAgreementsQuery(organizationId, softwareId),
    );
  }

  @Get('agreement/:organizationId/:dataId')
  @ApiOperation({
    summary: 'Get a single agreement by dataId',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the specified agreement',
  })
  @ApiResponse({
    status: 404,
    description: 'Agreement not found',
  })
  async getAgreement(
    @Param('organizationId') organizationId: string,
    @Param('dataId') dataId: string,
  ) {
    return this.queryBus.execute(new GetAgreementQuery(organizationId, dataId));
  }

  @ApiOperation({ summary: 'Get Platform icon' })
  @Get('/platform/icon/:platform/:size')
  async getPlatformIcon(
    @Param('platform') platform: string,
    @Param('size') size: string,
  ) {
    try {
      const response = await fetch(
        `https://www.google.com/s2/favicons?sz=${size ?? 64}&domain=${platform}`,
      );
      if (!response.ok) {
        return {
          status: response.status,
          statusText: response.statusText,
        };
      }
      const iconUrl = response.url;
      return { iconUrl };
    } catch (error) {
      throw new Error(`Error fetching platform icon: ${error.message}`);
    }
  }
}
