import { PaginationRequest } from '@goteacher/app/utility';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class SchoolRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search: string;
}

export class JoinSchoolRequestParamDto {
  @ApiProperty({
    required: true,
    description: 'School id',
  })
  @IsNotEmpty()
  schoolId: string;
  constructor(obj: JoinSchoolRequestParamDto) {
    if (obj) {
      Object.assign(this, obj);
    }
  }
}
