import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { JoinSchoolCommand } from '@goteacher/app/school';
import { GetSchoolsQuery } from '@goteacher/app/school/query';
import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  JoinSchoolRequestParamDto,
  SchoolRequestQueryDto,
} from 'apps/api/src/api/school/req.dto';
import { GetSchoolsResponseDto } from 'apps/api/src/api/school/res.dto';

@ApiTags('school')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@UseGuards(JWTGuard)
@Controller('schools')
export class SchoolController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) {}

  @Get('/')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: GetSchoolsResponseDto })
  @ApiOperation({ summary: 'Get schools' })
  async getSchools(
    @Query() query: SchoolRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.queryBus.execute(new GetSchoolsQuery({ ...query, ctx }));
  }

  @Post('/:schoolId/join')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: UserSchool })
  @ApiOperation({ summary: 'Join school' })
  async joinSchool(
    @Param() params: JoinSchoolRequestParamDto,
    @ReqContext() ctx: ReqContext,
  ) {
    return await this.commandBus.execute(
      new JoinSchoolCommand({ ctx, ...params }),
    );
  }
}
