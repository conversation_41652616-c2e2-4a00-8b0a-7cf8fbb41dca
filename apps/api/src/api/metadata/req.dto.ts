import {
  ApprovalStatus,
  DataPrivacy,
  DistrictRecommended,
} from '@goteacher/app/models/mongo/org.metadata.model';
import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator';

import { PaymentStatus } from '@goteacher/app/models/mongo';
import { PaginationRequest } from '@goteacher/app/utility';

export class UpsertOrgMetadataRequestBodyDto {
  @ApiProperty({
    example: 'org.example.com',
    required: true,
    description: 'Domain name',
  })
  @IsString()
  domain: string;

  @ApiProperty({
    example: DistrictRecommended.APPROVED,
    description: 'District recommended status',
    enum: DistrictRecommended,
    required: false,
  })
  @IsOptional()
  @IsEnum(DistrictRecommended)
  districtRecommended?: DistrictRecommended;

  @ApiProperty({
    example: DataPrivacy.SIGNED,
    description: 'Data privacy status',
    enum: DataPrivacy,
    required: false,
  })
  @IsOptional()
  @IsEnum(DataPrivacy)
  dataPrivacy?: DataPrivacy;

  @ApiProperty({
    example: ApprovalStatus.APPROVED,
    description: 'Approval status',
    enum: ApprovalStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ApprovalStatus)
  approvalStatus?: ApprovalStatus;
}

export class CreateCustomMetadataRequestDto {
  @ApiProperty({
    example: 'example.com',
    required: true,
    description: 'Base domain name',
  })
  @IsString()
  domain: string;

  @ApiProperty({
    example: 'sub.example.com',
    required: false,
    description: 'Full domain name including subdomain',
  })
  @IsString()
  @IsOptional()
  fullDomain: string;

  @ApiProperty({
    example: 'LOW',
    required: false,
    description: 'Risk level assessment of the domain',
  })
  @IsString()
  @IsOptional()
  riskLevel: string;

  @ApiProperty({
    example: ['EDUCATION', 'PRODUCTIVITY'],
    required: true,
    description: 'Categories of the domain',
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  categories: string[];
}

export class UpdateCustomMetadataRequestBodyDto extends PartialType(
  OmitType(CreateCustomMetadataRequestDto, [
    'domain',    
    'fullDomain',
  ]),
) { }

export class GetOrgMetadataRequestParamsDto {
  @ApiProperty({
    example: 'org.example.com',
    required: true,
    description: 'Domain name',
  })
  @IsString()
  domain: string;
}

export class ListOrgMetadataRequestQueryDto {
  @ApiProperty({
    required: false,
    type: [String],
    description: 'Domains',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  domains: string[];

  @ApiProperty({
    example: DataPrivacy.SIGNED,
    description: 'Data privacy status',
    enum: DataPrivacy,
    required: false,
  })
  @IsOptional()
  @IsEnum(DataPrivacy)
  dataPrivacy?: DataPrivacy;

  @ApiProperty({
    example: DistrictRecommended.APPROVED,
    description: 'District recommended status',
    enum: DistrictRecommended,
    required: false,
  })
  @IsOptional()
  @IsEnum(DistrictRecommended)
  districtRecommended?: DistrictRecommended;

  @ApiProperty({
    example: PaymentStatus.PAID,
    description: 'Payment status',
    enum: PaymentStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @ApiProperty({
    example: ApprovalStatus.APPROVED,
    description: 'Approval status',
    enum: ApprovalStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ApprovalStatus)
  approvalStatus?: ApprovalStatus;
}

export class ListPlatformsRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;
}

export class ListProductsRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;
}

export class ListCategoriesRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;
}

export class GetCustomMetadataRequestQueryDto extends PaginationRequest {
  @ApiProperty({
    required: true,
    description: 'Domain name',
  })
  @IsString()
  domain: string;
}

export class DeleteCustomMetadataRequestDto {
  @ApiProperty({
    required: true,
    description: 'Metadata Id',
  })
  @IsString()
  metadataId: string;
}
