import { JWTG<PERSON>, ReqContext } from '@goteacher/app/auth';
import { RoleGuard, RoleProtected } from '@goteacher/app/auth/guard/role.guard';
import {
  GetOrgMetadataQuery,
  ListOrgMetadataQuery,
  ListProductsQuery,
  UpsertOrgMetadataCommand,
} from '@goteacher/app/metadata';
import { CreateCustomMetadataCommand } from '@goteacher/app/metadata/command/create-custom-metadata';
import { DeleteCustomMetadataCommand } from '@goteacher/app/metadata/command/delete-custom-metadata';
import { UpdateCustomMetadataCommand } from '@goteacher/app/metadata/command/update-custom-metadata';
import { GetCustomMetadataQuery } from '@goteacher/app/metadata/query/get-custom-metadata/query';
import { ListCategoriesQuery } from '@goteacher/app/metadata/query/list-categories';
import { ListPlatformsQuery } from '@goteacher/app/metadata/query/list-platform';
import { ListProductsQueryV2 } from '@goteacher/app/metadata/query/list-product-v2';
import { OrganizationDomainMetadata } from '@goteacher/app/models/mongo/org.metadata.model';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  CreateCustomMetadataRequestDto,
  DeleteCustomMetadataRequestDto,
  GetCustomMetadataRequestQueryDto,
  GetOrgMetadataRequestParamsDto,
  ListCategoriesRequestQueryDto,
  ListOrgMetadataRequestQueryDto,
  ListPlatformsRequestQueryDto,
  ListProductsRequestQueryDto,
  UpdateCustomMetadataRequestBodyDto,
  UpsertOrgMetadataRequestBodyDto,
} from 'apps/api/src/api/metadata/req.dto';

@ApiTags('metadata')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiInternalServerErrorResponse({ description: 'Internal Server Error' })
@UseGuards(JWTGuard, RoleGuard)
@Controller('metadata')
@RoleProtected([UserRole.ADMINISTRATOR])
export class MetadataController {
  constructor(
    private commandBus: CommandBus,
    private queryBus: QueryBus,
  ) { }

  @ApiOkResponse({
    description: 'Org metadata created successfully',
    type: OrganizationDomainMetadata,
  })
  @ApiOperation({ summary: 'Upsert org metadata' })
  @Post('domain')
  async upsertOrgMetadata(
    @Body() body: UpsertOrgMetadataRequestBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];
    if (!orgId) {
      throw new ForbiddenException(
        'User not associated with any organisation!',
      );
    }

    return this.commandBus.execute(
      new UpsertOrgMetadataCommand({ ...body, orgId }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Org metadata found successfully',
    type: OrganizationDomainMetadata,
  })
  @ApiNotFoundResponse({ description: 'Org metadata not found' })
  @ApiOperation({ summary: 'Get org metadata for a single domain' })
  @Get('/domain/:domain')
  async getOrgMetadata(
    @Param() params: GetOrgMetadataRequestParamsDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];

    return this.queryBus.execute(new GetOrgMetadataQuery({ ...params, orgId }));
  }

  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'List and search for org metadata' })
  @Get('/domain')
  async listOrgMetadata(
    @Query() query: ListOrgMetadataRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];

    return await this.queryBus.execute(
      new ListOrgMetadataQuery({
        orgId,
        ...query,
      }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'List platform metadata' })
  @Get('/platform')
  async listPlatforms(
    @Query() query: ListPlatformsRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];
    return await this.queryBus.execute(
      new ListPlatformsQuery({
        orgId,
        ...query,
      }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'List Product metadata' })
  @Get('/product')
  async listProducts(
    @Query() query: ListProductsRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];
    return await this.queryBus.execute(
      new ListProductsQuery({
        orgId,
        ...query,
      }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'List products' })
  @Get('/v2/product')
  async listProductsV2(
    @Query() query: ListProductsRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];
    return await this.queryBus.execute(
      new ListProductsQueryV2({
        orgId,
        ...query,
      }),
    );
  }

  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create custom metadata' })
  @Post('/custom')
  async createCustomMetadata(
    @Body() body: CreateCustomMetadataRequestDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];
    if (!orgId) {
      throw new ForbiddenException(
        'User not associated with any organisation!',
      );
    }

    return this.commandBus.execute(
      new CreateCustomMetadataCommand({ metadata: body, orgId }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update custom metadata' })
  @Put('/custom/:id')
  async updateCustomMetadata(
    @Param('id') id: string,
    @Body() body: UpdateCustomMetadataRequestBodyDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];
    if (!orgId) {
      throw new ForbiddenException(
        'User not associated with any organisation!',
      );
    }

    return this.commandBus.execute(
      new UpdateCustomMetadataCommand({
        metadata: body,
        metadataId: id,
        orgId,
      }),
    );
  }

  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete custom metadata' })
  @Delete('/custom')
  async deleteCustomMetadata(
    @Query() query: DeleteCustomMetadataRequestDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];
    if (!orgId) {
      throw new ForbiddenException(
        'User not associated with any organisation!',
      );
    }

    return await this.commandBus.execute(
      new DeleteCustomMetadataCommand({
        ...query,
        orgId,
      }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get custom metadata' })
  @Get('/custom/:domain')
  async getCustomMetadata(
    @Query() query: GetCustomMetadataRequestQueryDto,
    @ReqContext() ctx: ReqContext,
  ) {
    const orgId = ctx.user.UserSchool.map((s) => s.school.organisationId)[0];

    return this.queryBus.execute(
      new GetCustomMetadataQuery({
        ...query,
        orgId,
      }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'List categories' })
  @Get('/categories')
  async listCategories(@Query() query: ListCategoriesRequestQueryDto) {
    return await this.queryBus.execute(
      new ListCategoriesQuery({
        ...query,
      }),
    );
  }
}
