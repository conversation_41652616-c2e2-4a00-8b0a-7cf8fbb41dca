import { GetExtensionConfigHandler } from '@goteacher/app/extension';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { IngestionExtensionController } from 'apps/ingestion/src/api/extension/extension.controller';

@Module({
  imports: [CqrsModule],
  controllers: [IngestionExtensionController],
  providers: [GetExtensionConfigHandler],
})
export class IngestionExtensionModule {}
