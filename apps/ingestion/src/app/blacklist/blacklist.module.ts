import { DeleteBlacklist<PERSON>om<PERSON>Handler } from '@goteacher/app/blacklist/command/delete-blacklist';
import { UpsertBlacklistCommandHandler } from '@goteacher/app/blacklist/command/upsert-blacklist';
import { CheckBlacklistQueryHandler } from '@goteacher/app/blacklist/query/check-blacklist';
import { ListBlacklistQueryHandler } from '@goteacher/app/blacklist/query/list-blacklist';
import { Modu<PERSON> } from '@nestjs/common';
import { IngestionBlacklistController } from 'apps/ingestion/src/api/blacklist/blacklist.controller';

@Module({
  controllers: [IngestionBlacklistController],
  providers: [
    UpsertBlacklistCommandHandler,
    DeleteBlacklistCommandHandler,
    CheckBlacklistQueryHandler,
    ListBlacklistQueryHandler,
  ],
})
export class IngestionBlacklistModule {}
