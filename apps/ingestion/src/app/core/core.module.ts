import { Global, Module } from '@nestjs/common';
// import { IngestionAWSModule } from 'apps/ingestion/src/app/core/aws/aws.module';
import { IngestionBrokerModule } from 'apps/ingestion/src/app/core/broker/broker.module';
import { IngestionCacheModule } from 'apps/ingestion/src/app/core/cache/cache.module';
import { IngestionDBModule } from 'apps/ingestion/src/app/core/db/db.module';
// import { IngestionStorageModule } from 'apps/ingestion/src/app/core/storage/storage.module';

@Global()
@Module({
  imports: [
    IngestionDBModule,
    IngestionCacheModule,
    // IngestionAWSModule,
    // IngestionStorageModule,
    IngestionBrokerModule,
  ],
  exports: [
    IngestionDBModule,
    IngestionCacheModule,
    // IngestionAWSModule,
    // IngestionStorageModule,
    IngestionBrokerModule,
  ],
})
export class IngestionCoreModule { }
