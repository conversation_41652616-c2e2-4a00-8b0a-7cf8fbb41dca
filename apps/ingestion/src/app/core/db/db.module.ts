import { MongooseModelModule } from '@goteacher/app/models/mongo';
import { SequelizeModelModule } from '@goteacher/app/models/sequelize';
import { MongoModule, PostgresSequelizeModule } from '@goteacher/infra/db';
import { Global, Module } from '@nestjs/common';

@Global()
@Module({
  imports: [
    PostgresSequelizeModule,
    SequelizeModelModule,
    MongoModule,
    MongooseModelModule,
  ],
  exports: [
    PostgresSequelizeModule,
    SequelizeModelModule,
    MongoModule,
    MongooseModelModule,
  ],
})
export class IngestionDBModule { }
