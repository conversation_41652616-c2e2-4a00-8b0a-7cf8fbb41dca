import { KafkaModule } from '@goteacher/infra/kafka';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KafkaConfig } from 'kafkajs';

@Module({
  imports: [
    KafkaModule.forRoot([
      {
        name: 'goteacher-ingestion',
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => {
          let config: KafkaConfig = {
            clientId: 'goteacher-ingestion',
            retry: {
              retries: 10,
              initialRetryTime: 100,
            },
            brokers: configService.get('KAFKA_BROKERS').split(','),
          };

          if (configService.get<string>('NODE_ENV') != 'local') {
            config = {
              ...config,
              sasl: {
                mechanism: configService.get('KAFKA_MECHANISM') as any,
                username: configService.get('KAFKA_USERNAME'),
                password: configService.get('KAFKA_PASSWORD'),
              },
              ssl: false,
            };
          }

          return config;
        },
      },
    ]),
    KafkaModule.register([
      {
        name: 'ingestion',
        connName: 'goteacher-ingestion',
        consumers: [],
        producer: {
          options: {
            allowAutoTopicCreation: true,
            retry: {
              retries: 10,
            },
          },
          topics: ['enriched_events', 'content_tagging_requests', 'activity_sessions', 'interaction_summaries', 'page_scraping_events'],
        },
      },
    ]),
  ],
  providers: [],
  exports: [KafkaModule],
})
export class IngestionBrokerModule { }
