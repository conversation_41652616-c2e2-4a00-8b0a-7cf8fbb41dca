import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, C<PERSON><PERSON>ventHandler } from '@goteacher/app/event/command/create-event';
import { ScrapeEventHandler } from '@goteacher/app/event/command/scrape-event';
import { Module } from '@nestjs/common';
import { IngestionEventController } from 'apps/ingestion/src/api/event/event.controller';

@Module({
  controllers: [IngestionEventController],
  providers: [CreateEventHandler, ScrapeEventHandler, CreateBatchEventHandler],
})
export class IngestionEventModule { }
