import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class BlacklistUpsertBodyDto {
  @ApiProperty({
    required: true,
    type: String,
    description: 'Domain',
  })
  @IsString()
  domain: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'URL',
  })
  @IsOptional()
  @IsString()
  url: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'URL pattern',
  })
  @IsOptional()
  @IsString()
  urlPattern: string;
}

export class BlacklistDeleteParamsDto {
  @ApiProperty({
    required: true,
    type: String,
    description: 'Blacklist ID',
  })
  @IsString()
  id: string;
}

export class BlackListRequestQueryDto {
  @ApiProperty({
    required: true,
    type: String,
    description: 'Domain',
  })
  @IsString()
  domain: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'URL',
  })
  @IsString()
  url: string;
}

export class BlacklistListQueryDto {
  @ApiProperty({
    required: false,
    type: String,
    description: 'Domain',
  })
  @IsOptional()
  @IsString()
  domain: string;
}
