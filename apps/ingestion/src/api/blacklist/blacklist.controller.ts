import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { RoleGuard, RoleProtected } from '@goteacher/app/auth/guard/role.guard';
import { DeleteBlacklistCommand } from '@goteacher/app/blacklist/command/delete-blacklist';
import { UpsertBlacklistCommand } from '@goteacher/app/blacklist/command/upsert-blacklist';
import { CheckBlacklistQuery } from '@goteacher/app/blacklist/query/check-blacklist';
import { ListBlacklistQuery } from '@goteacher/app/blacklist/query/list-blacklist';
import { Blacklist } from '@goteacher/app/models/sequelize/blacklist.model';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  BlacklistDeleteParamsDto,
  BlacklistListQueryDto,
  BlackListRequestQueryDto,
  BlacklistUpsertBodyDto,
} from 'apps/ingestion/src/api/blacklist/req.dto';
import { CheckBlacklistResponseDto } from 'apps/ingestion/src/api/blacklist/res.dto';

@ApiTags('blacklist')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@UseGuards(JWTGuard)
@Controller('blacklist')
export class IngestionBlacklistController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) { }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description:
      'Blacklist check completed with productId (when not blacklisted)',
    type: CheckBlacklistResponseDto,
  })
  @ApiOperation({
    summary: 'Check if url is blacklisted and get productId',
  })
  @Get()
  async checkBlacklist(
    @ReqContext() ctx: ReqContext,
    @Query() query: BlackListRequestQueryDto,
  ) {
    return await this.queryBus.execute(
      new CheckBlacklistQuery({ ...query, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Blacklist created successfully',
    type: [Blacklist],
  })
  @ApiOperation({ summary: 'Upsert blacklist' })
  @UseGuards(RoleGuard)
  @RoleProtected([UserRole.ADMINISTRATOR])
  @Put()
  async upsertBlacklist(
    @ReqContext() ctx: ReqContext,
    @Body() body: BlacklistUpsertBodyDto,
  ) {
    return await this.commandBus.execute(
      new UpsertBlacklistCommand({ ...body, ctx }),
    );
  }

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Blacklist deleted successfully',
    type: Blacklist,
  })
  @ApiNotFoundResponse({ description: 'Blacklist not found' })
  @ApiOperation({ summary: 'Delete blacklist' })
  @UseGuards(RoleGuard)
  @RoleProtected([UserRole.ADMINISTRATOR])
  @Delete(':id')
  async deleteBlackList(
    @ReqContext() ctx: ReqContext,
    @Param() params: BlacklistDeleteParamsDto,
  ) {
    return await this.commandBus.execute(
      new DeleteBlacklistCommand({ ...params, ctx }),
    );
  }

  @Get('list')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'List blacklist' })
  @ApiOkResponse({
    description: 'Blacklist found successfully',
    type: [Blacklist],
  })
  async listBlacklist(
    @ReqContext() ctx: ReqContext,
    @Query() query: BlacklistListQueryDto,
  ) {
    return await this.queryBus.execute(
      new ListBlacklistQuery({ ...query, ctx }),
    );
  }
}
