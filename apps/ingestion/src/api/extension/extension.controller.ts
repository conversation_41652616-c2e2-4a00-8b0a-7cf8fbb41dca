import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { GetExtensionConfigQuery, ExtensionConfigResponseDto } from '@goteacher/app/extension';
import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

@ApiTags('extension')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@UseGuards(JWTGuard)
@Controller('extension')
export class IngestionExtensionController {
  constructor(private readonly queryBus: QueryBus) {}

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Extension configuration retrieved successfully',
    type: ExtensionConfigResponseDto,
  })
  @ApiOperation({
    summary: 'Get extension configuration',
    description: 'Retrieves extension configuration for the authenticated user\'s organization with fallback to global defaults'
  })
  @Get('config')
  async getConfig(@ReqContext() ctx: ReqContext): Promise<ExtensionConfigResponseDto> {
    const orgId = ctx.tokenPayload.orgIds?.[0] || '*';
    
    const config = await this.queryBus.execute(
      new GetExtensionConfigQuery({ orgId }),
    );

    return config;
  }
}
