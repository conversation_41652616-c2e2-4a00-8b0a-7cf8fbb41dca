import { JWTGuard } from '@goteacher/app/auth';
import { ScrapeConfig } from '@goteacher/app/models/sequelize/scrape-config.model';
import { GetScrapingConfigQuery } from '@goteacher/app/scraping/query/get-scraping-config';
import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Query,
  UseGuards,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { ScrapingConfigQueryDto } from 'apps/ingestion/src/api/scraping/req.dto';

@ApiTags('scraping')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@UseGuards(JWTGuard)
@Controller()
export class IngestionScrapingController {
  constructor(private readonly queryBus: QueryBus) {}

  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Scraping config found successfully',
    type: ScrapeConfig,
  })
  @ApiNotFoundResponse({ description: 'Scraping config not found' })
  @ApiOperation({ summary: 'Get scraping config' })
  @Get('config')
  async getConfig(@Query() query: ScrapingConfigQueryDto) {
    return await this.queryBus.execute(
      new GetScrapingConfigQuery({ ...query }),
    );
  }
}
