import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class ScrapingConfigQueryDto {
  @ApiProperty({
    required: true,
    type: String,
    description: 'Domain',
  })
  @IsString()
  domain: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'URL',
  })
  @IsString()
  url: string;

  @ApiProperty({
    required: false,
    type: String,
    description: 'FavIcon URL',
  })
  @IsOptional()
  @IsString()
  favIconUrl?: string;
}
