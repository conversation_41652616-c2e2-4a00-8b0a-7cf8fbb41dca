import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { CheckBlacklistQuery } from '@goteacher/app/blacklist/query/check-blacklist';
import { CreateBatchEventCommand, CreateEventCommand } from '@goteacher/app/event/command/create-event';
import { ScrapeEventCommand } from '@goteacher/app/event/command/scrape-event';
import { EventType } from '@goteacher/app/event/event.types';
import { InjectKafka, KafkaClient, KafkaEvent } from '@goteacher/infra/kafka';
import {
  BadRequestException,
  Body,
  Controller,
  ForbiddenException,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  UseGuards,
  ValidationPipe
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ApiBody,
  ApiExtraModels,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNoContentResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  BaseEventDto,
  ClickEventDto,
  CreateEventBodyDto,
  IdlePingEventDto,
  InteractionSummaryEventDto,
  KeydownEventDto,
  NoActivityEventDto,
  PageCloseEventDto,
  PageOpenEventDto,
  PageScrapingEventDto,
  ScrollEventDto,
  VideoEndedEventDto,
  VideoPauseEventDto,
  VideoPlayEventDto,
  VideoSeekEventDto,
  VideoVolumeEventDto,
} from 'apps/ingestion/src/api/event/req.dto';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { FastifyRequest } from 'fastify';

@ApiTags('event')
@ApiSecurity('bearer')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal server error' })
@UseGuards(JWTGuard)
@Controller()
export class IngestionEventController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
    @InjectKafka('ingestion') private readonly kafkaService: KafkaClient,
  ) { }

  @ApiExtraModels(
    BaseEventDto,
    ClickEventDto,
    KeydownEventDto,
    ScrollEventDto,
    PageOpenEventDto,
    PageCloseEventDto,
    VideoPlayEventDto,
    VideoPauseEventDto,
    VideoSeekEventDto,
    VideoEndedEventDto,
    VideoVolumeEventDto,
    PageScrapingEventDto,
    NoActivityEventDto,
    InteractionSummaryEventDto,
    IdlePingEventDto,
  )
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'Event(s) created successfully' })
  @ApiBody({
    schema: {
      oneOf: [
        {
          oneOf: [
            { $ref: getSchemaPath(ClickEventDto) },
            { $ref: getSchemaPath(KeydownEventDto) },
            { $ref: getSchemaPath(ScrollEventDto) },
            { $ref: getSchemaPath(PageOpenEventDto) },
            { $ref: getSchemaPath(PageCloseEventDto) },
            { $ref: getSchemaPath(VideoPlayEventDto) },
            { $ref: getSchemaPath(VideoPauseEventDto) },
            { $ref: getSchemaPath(VideoSeekEventDto) },
            { $ref: getSchemaPath(VideoEndedEventDto) },
            { $ref: getSchemaPath(VideoVolumeEventDto) },
            { $ref: getSchemaPath(PageScrapingEventDto) },
            { $ref: getSchemaPath(NoActivityEventDto) },
            { $ref: getSchemaPath(InteractionSummaryEventDto) },
            { $ref: getSchemaPath(IdlePingEventDto) },
          ],
          discriminator: {
            propertyName: 'type',
            mapping: {
              [EventType.CLICK]: getSchemaPath(ClickEventDto),
              [EventType.KEYDOWN]: getSchemaPath(KeydownEventDto),
              [EventType.SCROLL]: getSchemaPath(ScrollEventDto),
              [EventType.PAGE_OPEN]: getSchemaPath(PageOpenEventDto),
              [EventType.PAGE_CLOSE]: getSchemaPath(PageCloseEventDto),
              [EventType.VIDEO_PLAY]: getSchemaPath(VideoPlayEventDto),
              [EventType.VIDEO_PAUSE]: getSchemaPath(VideoPauseEventDto),
              [EventType.VIDEO_SEEK]: getSchemaPath(VideoSeekEventDto),
              [EventType.VIDEO_ENDED]: getSchemaPath(VideoEndedEventDto),
              [EventType.VIDEO_VOLUME]: getSchemaPath(VideoVolumeEventDto),
              [EventType.PAGE_SCRAPING]: getSchemaPath(PageScrapingEventDto),
              [EventType.NO_ACTIVITY]: getSchemaPath(NoActivityEventDto),
              [EventType.INTERACTION_SUMMARY]: getSchemaPath(InteractionSummaryEventDto),
              [EventType.IDLE_PING]: getSchemaPath(IdlePingEventDto),
            },
          },
        },
        {
          type: 'array',
          items: {
            oneOf: [
              { $ref: getSchemaPath(ClickEventDto) },
              { $ref: getSchemaPath(KeydownEventDto) },
              { $ref: getSchemaPath(ScrollEventDto) },
              { $ref: getSchemaPath(PageOpenEventDto) },
              { $ref: getSchemaPath(PageCloseEventDto) },
              { $ref: getSchemaPath(VideoPlayEventDto) },
              { $ref: getSchemaPath(VideoPauseEventDto) },
              { $ref: getSchemaPath(VideoSeekEventDto) },
              { $ref: getSchemaPath(VideoEndedEventDto) },
              { $ref: getSchemaPath(VideoVolumeEventDto) },
              { $ref: getSchemaPath(PageScrapingEventDto) },
              { $ref: getSchemaPath(NoActivityEventDto) },
              { $ref: getSchemaPath(InteractionSummaryEventDto) },
              { $ref: getSchemaPath(IdlePingEventDto) },
            ],
            discriminator: {
              propertyName: 'type',
              mapping: {
                [EventType.CLICK]: getSchemaPath(ClickEventDto),
                [EventType.KEYDOWN]: getSchemaPath(KeydownEventDto),
                [EventType.SCROLL]: getSchemaPath(ScrollEventDto),
                [EventType.PAGE_OPEN]: getSchemaPath(PageOpenEventDto),
                [EventType.PAGE_CLOSE]: getSchemaPath(PageCloseEventDto),
                [EventType.VIDEO_PLAY]: getSchemaPath(VideoPlayEventDto),
                [EventType.VIDEO_PAUSE]: getSchemaPath(VideoPauseEventDto),
                [EventType.VIDEO_SEEK]: getSchemaPath(VideoSeekEventDto),
                [EventType.VIDEO_ENDED]: getSchemaPath(VideoEndedEventDto),
                [EventType.VIDEO_VOLUME]: getSchemaPath(VideoVolumeEventDto),
                [EventType.PAGE_SCRAPING]: getSchemaPath(PageScrapingEventDto),
                [EventType.NO_ACTIVITY]: getSchemaPath(NoActivityEventDto),
                [EventType.INTERACTION_SUMMARY]: getSchemaPath(InteractionSummaryEventDto),
                [EventType.IDLE_PING]: getSchemaPath(IdlePingEventDto),
              },
            },
          },
        },
      ],
    },
  })
  @ApiOperation({ summary: 'Create single event or batch of events' })
  @Post('create')
  async createEvent(
    @Body({
      transform: async (value) => {
        const isBatch = Array.isArray(value);
        const events = isBatch ? value : [value];

        const transformedEvents = await Promise.all(
          events.map(async (eventValue) => {
            let transformedValue: CreateEventBodyDto;

            if (eventValue.type === EventType.CLICK) {
              transformedValue = plainToInstance(ClickEventDto, eventValue);
            } else if (eventValue.type === EventType.KEYDOWN) {
              transformedValue = plainToInstance(KeydownEventDto, eventValue);
            } else if (eventValue.type === EventType.SCROLL) {
              transformedValue = plainToInstance(ScrollEventDto, eventValue);
            } else if (eventValue.type === EventType.PAGE_OPEN) {
              transformedValue = plainToInstance(PageOpenEventDto, eventValue);
            } else if (eventValue.type === EventType.PAGE_CLOSE) {
              transformedValue = plainToInstance(PageCloseEventDto, eventValue);
            } else if (eventValue.type === EventType.VIDEO_PLAY) {
              transformedValue = plainToInstance(VideoPlayEventDto, eventValue);
            } else if (eventValue.type === EventType.VIDEO_PAUSE) {
              transformedValue = plainToInstance(VideoPauseEventDto, eventValue);
            } else if (eventValue.type === EventType.VIDEO_SEEK) {
              transformedValue = plainToInstance(VideoSeekEventDto, eventValue);
            } else if (eventValue.type === EventType.VIDEO_ENDED) {
              transformedValue = plainToInstance(VideoEndedEventDto, eventValue);
            } else if (eventValue.type === EventType.VIDEO_VOLUME) {
              transformedValue = plainToInstance(VideoVolumeEventDto, eventValue);
            } else if (eventValue.type === EventType.PAGE_SCRAPING) {
              transformedValue = plainToInstance(PageScrapingEventDto, eventValue);
            } else if (eventValue.type === EventType.NO_ACTIVITY) {
              transformedValue = plainToInstance(NoActivityEventDto, eventValue);
            } else if (eventValue.type === EventType.INTERACTION_SUMMARY) {
              transformedValue = plainToInstance(InteractionSummaryEventDto, eventValue);
            } else if (eventValue.type === EventType.IDLE_PING) {
              transformedValue = plainToInstance(IdlePingEventDto, eventValue);
            } else {
              throw new BadRequestException('Invalid event type!');
            }

            const validation = await validate(transformedValue);
            if (validation.length > 0) {
              const validationPipe = new ValidationPipe();
              const exceptionFactory = validationPipe.createExceptionFactory();
              throw exceptionFactory(validation);
            }

            return transformedValue;
          })
        );

        return isBatch ? transformedEvents : transformedEvents[0];
      },
    })
    body: CreateEventBodyDto | CreateEventBodyDto[],
    @ReqContext() ctx: ReqContext,
    @Req() req: FastifyRequest,
  ) {
    if (Array.isArray(body)) {
      const firstEvent = body[0];
      const { trackingAllowed } = await this.queryBus.execute(
        new CheckBlacklistQuery({ domain: firstEvent.domain, url: firstEvent.url, ctx }),
      );

      if (!trackingAllowed) {
        throw new ForbiddenException('Tracking not allowed!');
      }
      const { events, activitySessions } = await this.commandBus.execute(
        new CreateBatchEventCommand({ events: body, ip: req.ip, ctx }),
      );
      if (events.length > 0) {
        // Route analytics events (INTERACTION_SUMMARY and IDLE_PING) to interaction_summaries topic, others to enriched_events
        const interactionSummariesEvents = events.filter(event => 
          ['INTERACTION_SUMMARY', 'IDLE_PING'].includes(event.eventName)
        );
        const regularEvents = events.filter(event => 
          !['INTERACTION_SUMMARY', 'IDLE_PING'].includes(event.eventName)
        );
        
        if (regularEvents.length > 0) {
          await Promise.all(regularEvents.map(event => this.kafkaService.publish(new KafkaEvent('enriched_events', { value: event }))));
        }
        
        if (interactionSummariesEvents.length > 0) {
          await Promise.all(interactionSummariesEvents.map(event => this.kafkaService.publish(new KafkaEvent('interaction_summaries', { value: event }))));
        }
      }
      if (activitySessions && activitySessions.length > 0) {
        for (const activitySession of activitySessions) {
          this.kafkaService.publish(new KafkaEvent('activity_sessions', { value: activitySession }));
        }
      }
      return;
    } else {
      const eventBody = body as CreateEventBodyDto;
      const { trackingAllowed } = await this.queryBus.execute(
        new CheckBlacklistQuery({ domain: eventBody.domain, url: eventBody.url, ctx }),
      );

      if (!trackingAllowed) {
        throw new ForbiddenException('Tracking not allowed!');
      }

      if (eventBody.type === EventType.PAGE_SCRAPING) {
        // const scrapingConfig = await this.queryBus.execute(
        //   new GetScrapingConfigQuery({ domain: eventBody.domain, url: eventBody.url }),
        // );

        // if (!scrapingConfig) {
        //   throw new NotFoundException('Scraping config not found!');
        // }

        const event = await this.commandBus.execute(
          new ScrapeEventCommand({
            event: eventBody,
            // config: scrapingConfig,
            ctx,
            ip: req.ip,
          }),
        );

        this.kafkaService.publish(
          new KafkaEvent('page_scraping_events', { value: event }),
        );
      } else {
        const event = await this.commandBus.execute(
          new CreateEventCommand({ event: eventBody, ip: req.ip, ctx }),
        );

        // Route events to appropriate topics
        const topic = [EventType.INTERACTION_SUMMARY, EventType.IDLE_PING].includes(eventBody.type)
          ? 'interaction_summaries'
          : 'enriched_events';
        this.kafkaService.publish(new KafkaEvent(topic, { value: event }));
      }
    }
  }
}
