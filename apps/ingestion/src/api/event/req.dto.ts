import { EventType } from '@goteacher/app/event/event.types';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';

export class BaseEventDto {
  @ApiProperty({ description: 'Event ID', required: true, type: String })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType;

  @ApiProperty({ description: 'Domain', required: true, type: String })
  @IsString()
  domain: string;

  @ApiProperty({ description: 'URL', required: true, type: String })
  @IsString()
  url: string;

  @ApiProperty({ description: 'Session ID', required: true, type: String })
  @IsString()
  sessionId: string;

  @ApiProperty({ description: 'Tab ID', required: true, type: Number })
  @IsNumber()
  tabId: number;

  @ApiProperty({
    description: 'Created at unix timestamp',
    required: true,
    type: Number,
  })
  @IsNumber()
  created_at: number;

  @ApiProperty({
    description: 'Client timezone',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  clientTimezone: string;

  @ApiProperty({
    description: 'FavIcon URL',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  favIconUrl?: string;

  @ApiProperty({
    description: 'Browser brand',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  browserBrand?: string;

  @ApiProperty({
    description: 'Browser version',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  browserVersion?: string;

  @ApiProperty({
    description: 'Platform',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiProperty({
    description: 'ProductId',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  product?: string;

  @ApiProperty({
    description: 'Extension version',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  extensionVersion?: string;
}

export class ClickEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.CLICK;

  @ApiProperty({ description: 'X position', required: true, type: Number })
  @IsNumber()
  posX: number;

  @ApiProperty({ description: 'Y position', required: true, type: Number })
  @IsNumber()
  posY: number;
}

export class KeydownEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.KEYDOWN;

  @ApiProperty({ description: 'Key', required: true, type: String })
  @IsString()
  key: string;
}

export class ScrollEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.SCROLL;

  @ApiProperty({
    description: 'Scroll percentage',
    required: true,
    type: Number,
  })
  @IsNumber()
  scrollPercentage: number;

  @ApiProperty({ description: 'Document height', required: true, type: Number })
  @IsNumber()
  docHeight: number;

  @ApiProperty({ description: 'Scroll top', required: true, type: Number })
  @IsNumber()
  scrollTop: number;

  @ApiProperty({ description: 'Window height', required: true, type: Number })
  @IsNumber()
  winHeight: number;
}

export class PageOpenEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.PAGE_OPEN;
}

export class PageCloseEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.PAGE_CLOSE;
}

export class VideoPlayEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_PLAY;

  @ApiProperty({ description: 'Video time', required: true, type: Number })
  @IsNumber()
  videoTime: number;

  @ApiProperty({ description: 'Video duration', required: true, type: Number })
  @IsNumber()
  videoDuration: number;
}

export class VideoPauseEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_PAUSE;

  @ApiProperty({ description: 'Video time', required: true, type: Number })
  @IsNumber()
  videoTime: number;

  @ApiProperty({ description: 'Video duration', required: true, type: Number })
  @IsNumber()
  videoDuration: number;
}

export class VideoSeekEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_SEEK;

  @ApiProperty({ description: 'Video time to', required: true, type: Number })
  @IsNumber()
  videoTimeTo: number;

  @ApiProperty({ description: 'Video time from', required: true, type: Number })
  @IsNumber()
  videoTimeFrom: number;

  @ApiProperty({ description: 'Video duration', required: true, type: Number })
  @IsNumber()
  videoDuration: number;
}

export class VideoEndedEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_ENDED;

  @ApiProperty({ description: 'Video duration', required: true, type: Number })
  @IsNumber()
  videoDuration: number;
}

export class VideoVolumeEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_VOLUME;

  @ApiProperty({ description: 'Video volume', required: true, type: Number })
  @IsNumber()
  videoVolume: number;
}

export class PageScrapingEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.PAGE_SCRAPING;

  @ApiProperty({ description: 'Page contents', required: true, type: Array })
  @IsArray()
  @IsOptional()
  contents?: Array<object>;

  @ApiProperty({ description: 'Metadata', required: true, type: Array })
  @IsArray()
  @IsOptional()
  metadata?: Array<object>;

  @ApiProperty({ description: 'Screenshot as base64 data URL', required: true, type: String })
  @IsString()
  @IsOptional()
  screenshot?: string;

  @ApiProperty({ description: 'Page headers', required: true, type: Array })
  @IsArray()
  @IsOptional()
  headers?: Array<string>;

  @ApiProperty({ description: 'JSON-LD structured data', required: true, type: Array })
  @IsArray()
  @IsOptional()
  ldJson?: Array<string>;
}

export class NoActivityEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.NO_ACTIVITY;
}

export class InteractionSummaryEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.INTERACTION_SUMMARY;

  @ApiProperty({ description: 'Start time timestamp', required: true, type: Number })
  @IsNumber()
  startTime: number;

  @ApiProperty({ description: 'End time timestamp', required: true, type: Number })
  @IsNumber()
  endTime: number;

  @ApiProperty({ description: 'Duration in seconds', required: true, type: Number })
  @IsNumber()
  @Min(0)
  duration: number;

  @ApiProperty({ description: 'Click count', required: true, type: Number })
  @IsNumber()
  @Min(0)
  clickCount: number;

  @ApiProperty({ description: 'Scroll event count', required: true, type: Number })
  @IsNumber()
  @Min(0)
  scrollEventCount: number;

  @ApiProperty({ description: 'Keypress count', required: true, type: Number })
  @IsNumber()
  @Min(0)
  keypressCount: number;

  @ApiProperty({ description: 'Mouse move duration in ms', required: true, type: Number })
  @IsNumber()
  @Min(0)
  mouseMoveDuration: number;

  @ApiProperty({ description: 'Max scroll depth percentage', required: true, type: Number })
  @IsNumber()
  @Min(0)
  @Max(100)
  maxScrollDepth: number;

  @ApiProperty({ description: 'Is video playing', required: true, type: Boolean })
  @IsBoolean()
  isVideoPlaying: boolean;

  // Optional Fields
  @ApiProperty({ description: 'Flush reason', required: false, enum: ['timeout', 'unload'] })
  @IsEnum(['timeout', 'unload'])
  @IsOptional()
  flushReason: 'timeout' | 'unload';

  @ApiProperty({ description: 'Event quality', required: false, enum: ['normal', 'flushed_early', 'unload_flush'] })
  @IsEnum(['normal', 'flushed_early', 'unload_flush'])
  @IsOptional()
  eventQuality: 'normal' | 'flushed_early' | 'unload_flush';
}

export class IdlePingEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.IDLE_PING;

  @ApiProperty({ description: 'Start time timestamp', required: true, type: Number })
  @IsNumber()
  startTime: number;

  @ApiProperty({ description: 'End time timestamp', required: true, type: Number })
  @IsNumber()
  endTime: number;

  @ApiProperty({ description: 'Duration in seconds', required: true, type: Number })
  @IsNumber()
  @Min(0)
  duration: number;

  @ApiProperty({ description: 'Idle duration in seconds', required: false, type: Number })
  @IsNumber()
  @Min(0)
  @IsOptional()
  idleDuration: number;

  @ApiProperty({ description: 'Ping sequence number', required: false, type: Number })
  @IsNumber()
  @Min(0)
  @IsOptional()
  pingSequence: number;

  @ApiProperty({ description: 'Last activity time timestamp', required: true, type: Number })
  @IsNumber()
  lastActivityTime: number;
}


export type CreateEventBodyDto =
  | ClickEventDto
  | KeydownEventDto
  | ScrollEventDto
  | PageOpenEventDto
  | PageCloseEventDto
  | VideoPlayEventDto
  | VideoPauseEventDto
  | VideoSeekEventDto
  | VideoEndedEventDto
  | VideoVolumeEventDto
  | PageScrapingEventDto
  | NoActivityEventDto
  | InteractionSummaryEventDto
  | IdlePingEventDto;
