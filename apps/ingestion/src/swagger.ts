import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export async function setupSwagger(app: INestApplication, port: number) {
  const config = new DocumentBuilder()
    .setTitle('GoTeacher Inc Ingestion API')
    .setDescription('GoTeacher Inc Ingestion OpenAPI specifications.')
    .setVersion(process.env.npm_package_version)
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT Token',
        in: 'header',
      },
      'bearer',
    )
    .addServer(`http://localhost:${port}`, 'Local')
    .addServer(`https://api.dev.goteacher.com`, 'GoTeacher Development')
    .setExternalDoc('Postman Collection', '/event/docs-json')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('/event/docs', app, document, {
    swaggerOptions: {
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });
}
