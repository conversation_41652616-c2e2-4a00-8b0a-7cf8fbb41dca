import { CommonConsoleLogger } from '@goteacher/app/common/logging';
import { SeedDiscovery } from '@goteacher/app/common/seed';
import { NestFactory } from '@nestjs/core';
import { SeedsMainModule } from 'apps/seeds/src/main.module';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(SeedsMainModule);
  app.useLogger(app.get(CommonConsoleLogger));

  await app.get(SeedDiscovery).runSeeds();
  await app.close();

  process.exit(0);
}
bootstrap();
