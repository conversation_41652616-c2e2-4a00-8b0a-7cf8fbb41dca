import { HttpContextInterceptor } from "@goteacher/app/common/logging";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { APP_INTERCEPTOR } from "@nestjs/core";
import { SeedsAppModule } from "apps/seeds/src/app/app.module";

import core from "./config/core.config";
import postgres from "./config/postgres.config";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [core, postgres]
    }),
    SeedsAppModule
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpContextInterceptor,
    },
  ],
})
export class SeedsMainModule {}