import { ISeed, Seed } from '@goteacher/app/common/seed';
import {
  Organisation,
  OrganisationStatus,
} from '@goteacher/app/models/sequelize/organisation.model';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import * as orgs from '../data/organizations.json';

@Injectable()
@Seed('organizations', { context: ['local', 'development'] })
export class OrganizationSeed implements ISeed {
  constructor(
    @InjectModel(Organisation)
    private readonly organizationModel: typeof Organisation,
  ) {}

  async up() {
    const organizationCt = await this.organizationModel.count();
    if (organizationCt == 0) {
      const organizations = orgs['default'].map((o) => ({
        name: o.name,
        displayName: o.displayName,
        domains: o.domains,
        status: OrganisationStatus[o.status as keyof typeof OrganisationStatus],
        deleted: o.deleted,
      }));
      await this.organizationModel.bulkCreate(organizations);
    }
  }

  async down() {
    await this.organizationModel.destroy({ where: {} });
  }
}
