import { Injectable } from "@nestjs/common";

import { ISeed, Seed } from "@goteacher/app/common/seed";
import { Country, State } from "@goteacher/app/models/sequelize";
import { InjectModel } from "@nestjs/sequelize";
import * as state from "../data/states.json";

@Injectable()
@Seed("states", { context: ["local", "development", "production"], depends_on: ["countries"] })
export class StateSeed implements ISeed {
  constructor(
    @InjectModel(State) private stateModel: typeof State,
    @InjectModel(Country) private countryModel: typeof Country
  ) {}

  async up() {
    const stateCt = await this.stateModel.count();

    if (stateCt == 0) {
      const countries = await this.countryModel.findAll();

      const states = countries
        .map((c) =>
          c.countryInitials == "US"
            ? state["default"].map((s) => ({
                countryId: c.id,
                stateInitials: s.abbreviation,
                stateName: s.name,
              }))
            : {
                countryId: c.id,
                stateInitials: c.countryInitials,
                stateName: c.countryName,
              },
        )
        .flat();

      await this.stateModel.bulkCreate(states);
    }
  }

  async down() {
    await this.stateModel.destroy({ where: {} });
  }
}
