import { ISeed, Seed } from '@goteacher/app/common/seed';
import { ScrapeConfig } from '@goteacher/app/models/sequelize/scrape-config.model';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

@Injectable()
@Seed('scrape_config', { context: ['local', 'development', 'production'] })
export class ScrapeConfigSeed implements ISeed {
  constructor(
    @InjectModel(ScrapeConfig) private scrapeConfigModel: typeof ScrapeConfig,
  ) {}

  async up(): Promise<void> {
    const scrapeConfigCount = await this.scrapeConfigModel.count();
    if (scrapeConfigCount > 0) {
      return;
    }

    await this.scrapeConfigModel.create({
      domain: '*',
      urlPattern: '^(https?://).*',
      contents: [
        {
          alias: 'content',
          key: 'content',
          type: 'n/a',
          value: 'n/a',
        },
      ],
      metadata: [],
      requested_tags: [
        {
          alias: 'ARTICLE_SUBJECT',
          description:
            'Subject of the article falling in one of these categories (addition, algebra, comparing, counting,           decimals, division, estimation, exponents_roots_and_logarithms, fact_fluency, fractions, functions_and_equations,  geometry_and_spatial_reasoning, graphs, integers, logic_and_reasoning, measurement, mixed_operations, money_and_consumer_math, multiplication, number_theory, patterns, percents, place_values, probability_and_statistics, properties, ratios_and_proportions, subtraction, time, trigonometry, word_problems, adjectives_and_adverbs, book_study, capitalization, context_clues, editing_and_revising, figurative_language, grammar, informational_texts, letters, literary_texts, main_idea, nouns, opinions_and_arguments, phonemic_awareness, phonics, phonological_awareness,pronouns, punctuation, reading_comprehension, reference_skills, roots_and_affixes, sight_words, spelling, verbs, vocabulary,  word_study, writing_strategies, biology, chemistry, earth_science, literacy_in_science, physics, science_and_engineering_practices,           units_and_measurement, civics, culture, economics, geography, global_studies, social_studies_skills, us_history, world_history) ',
        },
      ],
    });
  }
  async down(): Promise<void> {
    await this.scrapeConfigModel.destroy({ where: {} });
  }
}
