import { ISeed, Seed } from '@goteacher/app/common/seed';
import { Subject } from '@goteacher/app/models/sequelize';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

@Injectable()
@Seed('subjects', { context: ['local', 'development', 'production'] })
export class SubjectSeed implements ISeed {
  constructor(@InjectModel(Subject) private subjectModel: typeof Subject) {}

  async up() {
    const subjects = [
      { name: 'Addition', value: 'addition' },
      { name: 'Algebra', value: 'algebra' },
      { name: 'Comparing', value: 'comparing' },
      { name: 'Counting', value: 'counting' },
      { name: 'Decimals', value: 'decimals' },
      { name: 'Division', value: 'division' },
      { name: 'Estimation', value: 'estimation' },
      {
        name: 'Exponents, roots, and logarithms',
        value: 'exponents_roots_and_logarithms',
      },
      { name: 'Fact fluency', value: 'fact_fluency' },
      { name: 'Fractions', value: 'fractions' },
      { name: 'Functions and equations', value: 'functions_and_equations' },
      {
        name: 'Geometry and spatial reasoning',
        value: 'geometry_and_spatial_reasoning',
      },
      { name: 'Graphs', value: 'graphs' },
      { name: 'Integers', value: 'integers' },
      { name: 'Logic and reasoning', value: 'logic_and_reasoning' },
      { name: 'Measurement', value: 'measurement' },
      { name: 'Mixed operations', value: 'mixed_operations' },
      { name: 'Money and consumer math', value: 'money_and_consumer_math' },
      { name: 'Multiplication', value: 'multiplication' },
      { name: 'Number theory', value: 'number_theory' },
      { name: 'Patterns', value: 'patterns' },
      { name: 'Percents', value: 'percents' },
      { name: 'Place values', value: 'place_values' },
      {
        name: 'Probability and statistics',
        value: 'probability_and_statistics',
      },
      { name: 'Properties', value: 'properties' },
      { name: 'Ratios and proportions', value: 'ratios_and_proportions' },
      { name: 'Subtraction', value: 'subtraction' },
      { name: 'Time', value: 'time' },
      { name: 'Trigonometry', value: 'trigonometry' },
      { name: 'Word problems', value: 'word_problems' },
      { name: 'Adjectives and adverbs', value: 'adjectives_and_adverbs' },
      { name: 'Book study', value: 'book_study' },
      { name: 'Capitalization', value: 'capitalization' },
      { name: 'Context clues', value: 'context_clues' },
      { name: 'Editing and revising', value: 'editing_and_revising' },
      { name: 'Figurative language', value: 'figurative_language' },
      { name: 'Grammar', value: 'grammar' },
      { name: 'Informational texts', value: 'informational_texts' },
      { name: 'Letters', value: 'letters' },
      { name: 'Literary texts', value: 'literary_texts' },
      { name: 'Main idea', value: 'main_idea' },
      { name: 'Nouns', value: 'nouns' },
      { name: 'Opinions and arguments', value: 'opinions_and_arguments' },
      { name: 'Phonemic awareness', value: 'phonemic_awareness' },
      { name: 'Phonics', value: 'phonics' },
      { name: 'Phonological awareness', value: 'phonological_awareness' },
      { name: 'Pronouns', value: 'pronouns' },
      { name: 'Punctuation', value: 'punctuation' },
      { name: 'Reading comprehension', value: 'reading_comprehension' },
      { name: 'Reference skills', value: 'reference_skills' },
      { name: 'Roots and affixes', value: 'roots_and_affixes' },
      { name: 'Sight words', value: 'sight_words' },
      { name: 'Spelling', value: 'spelling' },
      { name: 'Verbs', value: 'verbs' },
      { name: 'Vocabulary', value: 'vocabulary' },
      { name: 'Word study', value: 'word_study' },
      { name: 'Writing strategies', value: 'writing_strategies' },
      { name: 'Biology', value: 'biology' },
      { name: 'Chemistry', value: 'chemistry' },
      { name: 'Earth science', value: 'earth_science' },
      { name: 'Literacy in science', value: 'literacy_in_science' },
      { name: 'Physics', value: 'physics' },
      {
        name: 'Science and engineering practices',
        value: 'science_and_engineering_practices',
      },
      { name: 'Units and measurement', value: 'units_and_measurement' },
      { name: 'Civics', value: 'civics' },
      { name: 'Culture', value: 'culture' },
      { name: 'Economics', value: 'economics' },
      { name: 'Geography', value: 'geography' },
      { name: 'Global studies', value: 'global_studies' },
      { name: 'Social Studies skills', value: 'social_studies_skills' },
      { name: 'U.S. history', value: 'u_s_history' },
      { name: 'World history', value: 'world_history' },
    ];
    const subjectCt = await this.subjectModel.count();
    if (subjectCt == 0) {
      await this.subjectModel.bulkCreate(subjects);
    }
  }

  async down() {
    await this.subjectModel.destroy({ where: {} });
  }
}
