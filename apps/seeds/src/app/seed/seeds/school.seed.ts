import { ISeed, Seed } from '@goteacher/app/common/seed';
import { Address } from '@goteacher/app/models/sequelize';
import { Organisation } from '@goteacher/app/models/sequelize/organisation.model';
import {
  School,
  SchoolStatus,
} from '@goteacher/app/models/sequelize/school.model';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { v4 as uuid } from 'uuid';

@Injectable()
@Seed('schools', {
  context: ['local'],
  depends_on: ['organizations', 'addresses'],
})
export class SchoolSeed implements ISeed {
  constructor(
    @InjectModel(School) private schoolModel: typeof School,
    @InjectModel(Organisation) private organisationModel: typeof Organisation,
    @InjectModel(Address) private addressModel: typeof Address,
  ) {}

  async up() {
    const schoolCt = await this.schoolModel.count();
    if (schoolCt == 0) {
      const organizations = await this.organisationModel.findAll({});
      const addresses = await this.addressModel.findAll({});
      const schools = organizations.map((org, i) => {
        return {
          id: uuid(),
          name: org.name,
          displayName: org.displayName,
          deleted: org.deleted,
          status: SchoolStatus[org.status as keyof typeof SchoolStatus],
          organisationId: org.id,
          addressId: addresses[i].id,
        };
      });

      await this.schoolModel.bulkCreate(schools);
    }
  }

  async down() {
    await this.schoolModel.destroy({ where: {} });
  }
}
