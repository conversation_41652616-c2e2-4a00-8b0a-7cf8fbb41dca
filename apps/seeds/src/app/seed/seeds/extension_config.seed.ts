import { ISeed, Seed } from '@goteacher/app/common/seed';
import { DEFAULT_EXTENSION_CONFIG } from '@goteacher/app/extension/extension.types';
import { ExtensionConfigModel } from '@goteacher/app/models/sequelize/extension-config.model';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

@Injectable()
@Seed('extension_config', { context: ['local', 'development', 'production'] })
export class ExtensionConfigSeed implements ISeed {
  constructor(
    @InjectModel(ExtensionConfigModel)
    private extensionConfigModel: typeof ExtensionConfigModel,
  ) { }

  async up(): Promise<void> {
    const extensionConfigCount = await this.extensionConfigModel.count();
    if (extensionConfigCount > 0) {
      return;
    }

    // Create global default configuration
    await this.extensionConfigModel.create({
      orgId: '*',
      config: DEFAULT_EXTENSION_CONFIG,
      description: 'Global default extension configuration',
      isActive: true,
    });

    // Create a sample organization-specific configuration for demo purposes
    await this.extensionConfigModel.create({
      orgId: 'demo-org-id',
      config: {
        ...DEFAULT_EXTENSION_CONFIG,
        allowScraping: {
          screenshot: true,
          metadata: true,
        },
        consecutiveIdlePings: 3,
        heartbeatInterval: 45, // 45 seconds for demo org
        timeframeRestriction: {
          enabled: true,
          allowedWindow:
          {
            start: '09:00',
            end: '16:00',
            daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
          },
          timezone: 'America/New_York',
        },
      },
      description: 'Demo organization configuration with custom settings',
      isActive: true,
    });
  }

  async down(): Promise<void> {
    await this.extensionConfigModel.destroy({ where: {} });
  }
}
