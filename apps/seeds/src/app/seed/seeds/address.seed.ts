import { ISeed, Seed } from "@goteacher/app/common/seed";
import { Address, Country, State } from "@goteacher/app/models/sequelize";
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import * as addresses from "../data/addresses.json";

@Injectable()
@Seed("addresses", { context: ["local", "development", "production"], depends_on: ["states"] })
export class AddressSeed implements ISeed {
  constructor(
    @InjectModel(Address) private addressModel: typeof Address,
    @InjectModel(State) private stateModel: typeof State,
    @InjectModel(Country) private countryModel: typeof Country
  ) {}

  async up() {
    const addressCt = await this.addressModel.count();
    if (addressCt == 0) {
      const countries = await this.countryModel.findAll();
      const states = await this.stateModel.findAll();

      const addressData = addresses["default"].map((address) => {
        const country = countries.find((c) => c.countryInitials === address.countryAbbreviation);
        const state = states.find((c) => c.stateInitials === address.stateAbbreviation);

        return {
          addressLine1: address.addressLine1,
          addressLine2: address.addressLine2,
          postalCode: address.postalCode,
          city: address.city,
          countryId: country.id,
          stateId: state.id,
        };
      });

      await this.addressModel.bulkCreate(
        addressData,
      );
    }
  }
  async down() {
    await this.addressModel.destroy({ where: {} });
  }
}
