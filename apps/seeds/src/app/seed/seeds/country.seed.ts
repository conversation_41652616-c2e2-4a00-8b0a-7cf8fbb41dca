import { Injectable } from "@nestjs/common";
import { v4 as uuidv4 } from "uuid";

import { ISeed, Seed } from "@goteacher/app/common/seed";
import { Country } from "@goteacher/app/models/sequelize";
import { InjectModel } from "@nestjs/sequelize";
import * as country from "../data/countries.json";

@Injectable()
@Seed("countries", { context: ["local", "development", "production"] })
export class CountrySeed implements ISeed {
  constructor(@InjectModel(Country) private countryModel: typeof Country) {} 

  async up() {
    const countryCt = await this.countryModel.count();

    if (countryCt == 0) {
      const countries = country["default"].map((c) => ({
        id: uuidv4(),
        countryName: c.name,
        countryInitials: c["alpha-2"],
      }));

      await this.countryModel.bulkCreate(countries);
    }
  }

  async down() {
    await this.countryModel.destroy({ where: {} });
  }
}
