import { ISeed, Seed } from '@goteacher/app/common/seed';
import { Grade } from '@goteacher/app/models/sequelize/grade.model';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
@Seed('grades', { context: ['local', 'development', 'production'] })
export class GradeSeed implements ISeed {
  constructor(@InjectModel(Grade) private gradeModel: typeof Grade) {}

  async up() {
    const grades = [
      {
        id: uuidv4(),
        name: 'Pre-K',
        value: 'PRE-K',
        order: 1,
        filterable: false,
        trackable: false,
        licenseable: false,
      },
      {
        id: uuidv4(),
        name: 'Kindergarten',
        value: 'KG',
        order: 2,
        filterable: true,
        trackable: false,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '1st grade',
        value: '1',
        order: 3,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '2nd grade',
        value: '2',
        order: 4,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '3rd grade',
        value: '3',
        order: 5,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '4th grade',
        value: '4',
        order: 6,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '5th grade',
        value: '5',
        order: 7,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '6th grade',
        value: '6',
        order: 8,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '7th grade',
        value: '7',
        order: 9,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '8th grade',
        value: '8',
        order: 10,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '9th grade',
        value: '9',
        order: 11,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '10th grade',
        value: '10',
        order: 12,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '11th grade',
        value: '11',
        order: 13,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: '12th grade',
        value: '12',
        order: 14,
        filterable: true,
        trackable: true,
        licenseable: true,
      },
      {
        id: uuidv4(),
        name: 'Higher education / College',
        value: 'COLLEGE',
        order: 15,
        filterable: false,
        trackable: false,
        licenseable: false,
      },
      {
        id: uuidv4(),
        name: 'Vocational education',
        value: 'VOCATIONAL',
        order: 16,
        filterable: false,
        trackable: false,
        licenseable: false,
      },
      {
        id: uuidv4(),
        name: 'Professional development',
        value: 'PROFESSIONAL',
        order: 17,
        filterable: false,
        trackable: false,
        licenseable: false,
      },
    ];

    const gradesDb = await this.gradeModel.findAll();

    for (const grade of grades) {
      const dbGrade = gradesDb.find((gradeDb) => gradeDb.value === grade.value);
      const id = dbGrade?.id ?? grade.id;
      await this.gradeModel.upsert({ ...grade, id });
    }
  }

  async down() {
    await this.gradeModel.destroy({ where: {} });
  }
}
