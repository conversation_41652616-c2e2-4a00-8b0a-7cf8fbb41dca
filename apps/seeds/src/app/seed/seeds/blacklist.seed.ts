import { ISeed, Seed } from '@goteacher/app/common/seed';
import { Blacklist } from '@goteacher/app/models/sequelize/blacklist.model';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

@Injectable()
@Seed('blacklist', { context: ['local', 'development', 'production'] })
export class BlacklistSeed implements ISeed {
  constructor(
    @InjectModel(Blacklist) private blacklistModel: typeof Blacklist,
  ) {}

  async up(): Promise<void> {
    const blacklistCount = await this.blacklistModel.count();
    if (blacklistCount > 0) {
      return;
    }

    const blacklistToCreate = [
      { domain: 'facebook.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'tiktok.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'instagram.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'instagram.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'twitter.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'x.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'amazon.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'spotify.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'espn.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'nytimes.com', urlPattern: '[sS]*', orgId: '*' },
      { domain: 'zoom.us', urlPattern: '[sS]*', orgId: '*' },
      {
        domain: 'yahoo.com',
        urlPattern: 'https?://(mail).yahoo.com',
        orgId: '*',
      },
      {
        domain: 'google.com',
        urlPattern: 'https?://(accounts|meet|earth|calendar|mail).google.com',
        orgId: '*',
      },
    ];

    await this.blacklistModel.bulkCreate(blacklistToCreate);
  }
  async down(): Promise<void> {
    await this.blacklistModel.destroy({ where: {} });
  }
}
