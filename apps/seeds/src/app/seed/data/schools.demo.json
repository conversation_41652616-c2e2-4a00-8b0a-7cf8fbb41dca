[{"orgName": "GoTeacher Inc", "name": "GoTeacher High School", "displayName": "GoTeacher High School", "deleted": false, "status": "APPROVED"}, {"orgName": "GoTeacher Inc", "name": "GoTeacher Primary School", "displayName": "GoTeacher Primary School", "deleted": false, "status": "APPROVED"}, {"orgName": "GoTeacher Inc", "name": "GoTeacher Secondary School", "displayName": "GoTeacher Secondary School", "deleted": false, "status": "APPROVED"}, {"orgName": "Batavia Public School District 101", "name": "<PERSON> Elementary School and Early Childhood Center", "displayName": "<PERSON> Elementary School and Early Childhood Center", "deleted": false, "status": "APPROVED"}, {"orgName": "Batavia Public School District 101", "name": "Hoover-Wood Elementary School", "displayName": "Hoover-Wood Elementary School", "deleted": false, "status": "APPROVED"}, {"orgName": "Batavia Public School District 101", "name": "Grace <PERSON> Elementary School", "displayName": "Grace <PERSON> Elementary School", "deleted": false, "status": "APPROVED"}, {"orgName": "Batavia Public School District 101", "name": "<PERSON><PERSON> <PERSON><PERSON> Elementary School", "displayName": "<PERSON><PERSON> <PERSON><PERSON> Elementary School", "deleted": false, "status": "APPROVED"}, {"orgName": "Batavia Public School District 101", "name": "<PERSON><PERSON> <PERSON><PERSON> Elementary School", "displayName": "<PERSON><PERSON> <PERSON><PERSON> Elementary School", "deleted": false, "status": "APPROVED"}, {"orgName": "Batavia Public School District 101", "name": "<PERSON> Elementary School", "displayName": "<PERSON> Elementary School", "deleted": false, "status": "APPROVED"}, {"orgName": "Batavia Public School District 101", "name": "Batavia High School", "displayName": "Batavia High School", "deleted": false, "status": "APPROVED"}, {"orgName": "Batavia Public School District 101", "name": "Rotolo Middle School", "displayName": "Rotolo Middle School", "deleted": false, "status": "APPROVED"}]