import { Module } from '@nestjs/common';
import { AddressSeed } from 'apps/seeds/src/app/seed/seeds/address.seed';
import { BlacklistSeed } from 'apps/seeds/src/app/seed/seeds/blacklist.seed';
import { CountrySeed } from 'apps/seeds/src/app/seed/seeds/country.seed';
import { GradeSeed } from 'apps/seeds/src/app/seed/seeds/grade.seed';
import { OrganizationSeed } from 'apps/seeds/src/app/seed/seeds/organization.seed';
import { SchoolSeed } from 'apps/seeds/src/app/seed/seeds/school.seed';
import { ScrapeConfigSeed } from 'apps/seeds/src/app/seed/seeds/scrape_config.seed';
import { StateSeed } from 'apps/seeds/src/app/seed/seeds/state.seed';
import { SubjectSeed } from 'apps/seeds/src/app/seed/seeds/subject.seed';

@Module({
  providers: [
    GradeSeed,
    CountrySeed,
    StateSeed,
    AddressSeed,
    OrganizationSeed,
    SchoolSeed,
    SubjectSeed,
    ScrapeConfigSeed,
    BlacklistSeed,
  ],
})
export class SeedsSeedModule {}
