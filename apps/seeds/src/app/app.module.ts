import { CommonModule } from "@goteacher/app/common/common.module";
import { Module } from "@nestjs/common";
import { SeedsCoreModule } from "apps/seeds/src/app/core/core.module";
import { SeedsSeedModule } from "apps/seeds/src/app/seed/seed.module";

@Module({
  imports: [CommonModule, SeedsCoreModule, SeedsSeedModule],
  exports: [CommonModule, SeedsCoreModule, SeedsSeedModule],
})
export class SeedsAppModule {}