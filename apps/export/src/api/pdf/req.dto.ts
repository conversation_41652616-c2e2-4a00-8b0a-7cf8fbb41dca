import {
  EnrichmentColumn,
  TimeGranularity,
  UserGroup,
} from '@goteacher/app/analytics';
import { PaymentStatus } from '@goteacher/app/models/mongo';
import {
  IsComparable,
  IsRegexMatch,
  PaginationRequest,
  sqlSanitizer,
  TransformToBoolean,
} from '@goteacher/app/utility';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class ExportPDFAnalyticsQueryDto extends PaginationRequest {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;

  @ApiProperty({
    example: 'time_spent_per_url_per_school',
    description: 'Table name',
    required: true,
  })
  @IsString()
  @IsRegexMatch(/^[_a-zA-Z][_a-zA-Z0-9$]*$/, {
    message: 'This is not a valid entity type identifier',
  })
  entityType: string;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;
}

export class EnrichmentDto {
  @ApiProperty({ isArray: true, enum: EnrichmentColumn })
  @IsArray()
  @ArrayMaxSize(30)
  @IsEnum(EnrichmentColumn, { each: true })
  enrichColumns: EnrichmentColumn[];
}

export class ColumnDto {
  @ApiProperty({
    description: 'This is the metric identifier',
    example: 'active_user_student',
  })
  @IsString()
  @IsRegexMatch(/^[_a-zA-Z][_a-zA-Z0-9$]*$/, {
    message: 'This is not a valid column identifier',
  })
  column: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum([
    'avg',
    'sum',
    'count',
    'min',
    'max',
    'uniq',
    'avgMerge',
    'sumMerge',
    'countMerge',
    'minMerge',
    'maxMerge',
    'uniqMerge',
  ])
  aggFunction?: string;
}

export class FilterDto {
  @ApiProperty()
  @IsString()
  @IsRegexMatch(/^[_a-zA-Z][_a-zA-Z0-9$]*$/, {
    message: 'This is not a valid column identifier',
  })
  column: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @IsEnum(['in', '=', '<=', '>=', '<', '>', 'like'])
  type?: string;

  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
          typeof item === 'string' ? sqlSanitizer(item) : item,
        )
      : value,
  )
  value: unknown[];
}

export class JoinDto {
  @ApiProperty({
    example: 'time_spent_per_url_per_school',
  })
  @IsString()
  @IsRegexMatch(/^[_a-zA-Z][_a-zA-Z0-9$]*$/, {
    message: 'This is not a valid entity type identifier',
  })
  entityType: string;

  @ApiProperty()
  @IsEnum(['LEFT', 'RIGHT', 'INNER'])
  joinType: string;

  @ApiProperty({ type: [ColumnDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColumnDto)
  @ArrayMaxSize(30)
  columns: ColumnDto[];

  @ApiProperty({ isArray: true, type: String })
  @IsArray()
  @ArrayMaxSize(30)
  on: [string];
}

export class ExportPDFAnalyticsBodyDto {
  @ApiProperty({ type: [FilterDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterDto)
  @ArrayMaxSize(30)
  filters: FilterDto[];

  @ApiProperty({ type: [ColumnDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ColumnDto)
  @ArrayMaxSize(30)
  columns: ColumnDto[];

  @ApiProperty({ type: [JoinDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JoinDto)
  @ArrayMaxSize(30)
  @IsOptional()
  join: JoinDto[];

  @ApiProperty({ type: [EnrichmentDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EnrichmentDto)
  @ArrayMaxSize(10)
  @IsOptional()
  enrichment: EnrichmentDto[];
}

export class GetTopDomainsBodyDto extends PaginationRequest {
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'toDate',
    (fromDate: Date, toDate: Date) => fromDate <= toDate,
    {
      message: 'fromDate should be less than or equal toDate',
    },
  )
  fromDate: Date;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : value))
  @IsDate()
  @IsComparable(
    'fromDate',
    (toDate: Date, fromDate: Date) => toDate >= fromDate,
    {
      message: 'toDate should be greater than or equal fromDate',
    },
  )
  toDate: Date;

  @ApiProperty({
    required: false,
    enum: TimeGranularity,
    default: TimeGranularity.ALL,
  })
  @IsOptional()
  @IsEnum(TimeGranularity)
  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;

  @ApiProperty({
    required: false,
    enum: PaymentStatus,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  price?: PaymentStatus;

  @ApiProperty({
    example: false,
    default: false,
    description: 'This is used to force refresh the cache',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @TransformToBoolean()
  forceRefresh: boolean = false;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
          typeof item === 'string' ? sqlSanitizer(item) : item,
        )
      : value,
  )
  schoolIds?: string[];

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value.map((item) =>
          typeof item === 'string' ? sqlSanitizer(item) : item,
        )
      : value,
  )
  grades?: string[];

  @ApiProperty({
    required: false,
    description: 'Search term',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    required: false,
    enum: UserGroup,
    default: UserGroup.BOTH,
  })
  @IsOptional()
  @IsEnum(UserGroup)
  userGroup?: UserGroup = UserGroup.BOTH;
}
