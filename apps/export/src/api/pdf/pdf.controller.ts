import { GetDataQuery, UserGroup } from '@goteacher/app/analytics';
import { GetTopDomainsQuery } from '@goteacher/app/analytics/query/data/get-analytics-list-domains/query';
import { JWTGuard, ReqContext } from '@goteacher/app/auth';
import { RoleGuard, RoleProtected } from '@goteacher/app/auth/guard/role.guard';
import { ExportAnalyticsPdfQuery } from '@goteacher/app/export';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import {
  Body,
  Controller,
  Post,
  Query,
  Res,
  StreamableFile,
  UseGuards,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import {
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  ExportPDFAnalyticsBodyDto,
  ExportPDFAnalyticsQueryDto,
  GetTopDomainsBodyDto,
} from 'apps/export/src/api/pdf/req.dto';

@ApiSecurity('bearer')
@ApiTags('pdf')
@ApiUnauthorizedResponse({ description: 'Unauthorized' })
@ApiForbiddenResponse({ description: 'Forbidden' })
@ApiInternalServerErrorResponse({ description: 'Internal Server Error' })
@UseGuards(JWTGuard, RoleGuard)
@RoleProtected([UserRole.ADMINISTRATOR, UserRole.TEACHER])
@Controller('pdf')
export class ExportPDFController {
  constructor(private readonly queryBus: QueryBus) {}

  @Post('analytics')
  @ApiOperation({ summary: 'Export analytic data for PDF' })
  async getExportAnalytic(
    @ReqContext() ctx: ReqContext,
    @Res({ passthrough: true }) res: Response,
    @Query() query: ExportPDFAnalyticsQueryDto,
    @Body() body: ExportPDFAnalyticsBodyDto,
  ) {
    const columnNames = [
      '#',
      ...body.columns.map((col) => col.column),
      ...body.join.map((join) => join.columns.map((col) => col.column)).flat(),
    ];

    const data = await this.queryBus.execute(
      new GetDataQuery({ ...query, ...body, ctx }),
    );
    const result = await this.queryBus.execute(
      new ExportAnalyticsPdfQuery({
        columns: columnNames,
        rows: data.data,
        fromDate: query.fromDate,
        toDate: query.toDate,
        ctx,
      }),
    );
    res.headers['Content-Type'] = 'application/pdf';
    res.headers['Content-Disposition'] =
      `attachment; filename=${result.fullFileName}`;
    return new StreamableFile(result.file);
  }

  @Post('top-domains')
  @ApiOperation({ summary: 'Export top domains analytics' })
  async exportTopDomains(
    @ReqContext() ctx: ReqContext,
    @Res({ passthrough: true }) res: Response,
    @Body() body: GetTopDomainsBodyDto,
  ) {
    const data = await this.queryBus.execute(
      new GetTopDomainsQuery({ ...body, ctx }),
    );

    const columns =
      body.userGroup === UserGroup.BOTH || !body?.userGroup
        ? [
            '#',
            'domain',
            'active_users',
            'page_views',
            'count_sessions',
            'sum_time_spent',
          ]
        : body.userGroup === UserGroup.STUDENT
          ? [
              '#',
              'domain',
              'active_users_student',
              'page_views_student',
              'count_sessions_student',
              'sum_time_spent_student',
            ]
          : [
              '#',
              'domain',
              'active_users_teacher',
              'page_views_teacher',
              'count_sessions_teacher',
              'sum_time_spent_teacher',
            ];
    const result = await this.queryBus.execute(
      new ExportAnalyticsPdfQuery({
        columns: columns,
        rows: data.data,
        fromDate: body.fromDate,
        toDate: body.toDate,
        ctx,
      }),
    );
    res.headers['Content-Type'] = 'application/pdf';
    res.headers['Content-Disposition'] =
      `attachment; filename=${result.fullFileName}`;
    return new StreamableFile(result.file);
  }
}
