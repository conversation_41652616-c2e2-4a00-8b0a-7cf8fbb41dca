import {
  EnrichmentService,
  GetDataQueryHandler,
} from '@goteacher/app/analytics';
import { GetTopDomainsQueryHandler } from '@goteacher/app/analytics/query/data/get-analytics-list-domains/handler';
import { ExportAnalyticsPdfQueryHandler } from '@goteacher/app/export';
import { Module } from '@nestjs/common';
import { ExportPDFController } from 'apps/export/src/api/pdf/pdf.controller';

@Module({
  controllers: [ExportPDFController],
  providers: [
    EnrichmentService,

    GetDataQueryHandler,
    GetTopDomainsQueryHandler,
    ExportAnalyticsPdfQueryHandler,
  ],
})
export class ExportPDFModule {}
