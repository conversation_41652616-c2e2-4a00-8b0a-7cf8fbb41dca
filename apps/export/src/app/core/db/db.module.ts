import { ClickhouseModule, PostgresSequelizeModule } from '@goteacher/infra/db';
import { Global, Module } from '@nestjs/common';

import { MongoModule } from '@goteacher/infra/db/mongo.module';
import { MongooseModelModule } from '@goteacher/app/models/mongo/mongoose.model.module';
import { SequelizeModelModule } from '@goteacher/app/models/sequelize/sequelize.model.module';

@Global()
@Module({
  imports: [
    PostgresSequelizeModule,
    SequelizeModelModule,
    MongoModule,
    MongooseModelModule,
    ClickhouseModule,
  ],
  exports: [
    PostgresSequelizeModule,
    SequelizeModelModule,
    MongoModule,
    MongooseModelModule,
    ClickhouseModule,
  ],
})
export class ExportDBModule {}
