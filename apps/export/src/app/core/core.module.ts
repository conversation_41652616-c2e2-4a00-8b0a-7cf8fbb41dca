import { Global, Module } from '@nestjs/common';
import { ExportAWSModule } from 'apps/export/src/app/core/aws/aws.module';
import { ExportCacheModule } from 'apps/export/src/app/core/cache/cache.module';
import { ExportDBModule } from 'apps/export/src/app/core/db/db.module';
import { ExportStorageModule } from 'apps/export/src/app/core/storage/storage.module';

@Global()
@Module({
  imports: [
    ExportAWSModule,
    ExportCacheModule,
    ExportDBModule,
    ExportStorageModule,
  ],
  exports: [
    ExportAWSModule,
    ExportCacheModule,
    ExportDBModule,
    ExportStorageModule,
  ],
})
export class ExportCoreModule {}
