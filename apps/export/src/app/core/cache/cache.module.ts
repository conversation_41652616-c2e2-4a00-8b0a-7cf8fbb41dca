import { ICacheService, RedisService } from "@goteacher/infra/cache";
import { Global, Module } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Global()
@Module({
  exports: [ICacheService],
  providers: [
    {
      inject: [ConfigService],
      provide: ICacheService,
      useFactory: (configService: ConfigService) => {
        return new RedisService({url: configService.getOrThrow<string>('REDIS_URL')}); 
      },
    }
  ]
})
export class ExportCacheModule {}