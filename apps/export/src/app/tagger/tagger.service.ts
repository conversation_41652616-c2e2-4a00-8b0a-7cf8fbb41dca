import { Injectable, Logger } from '@nestjs/common';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';

@Injectable()
export class TaggerService {
  private readonly logger = new Logger(TaggerService.name);

  constructor(
    // import clickhouse service
    private clickHouseService: NodeClickHouseClient,
  ) {}

  // @Cron(CronExpression.EVERY_10_SECONDS)
  async handleCron() {
    this.logger.debug('Called every 10 seconds');
    // const sqlQuery = `
    //   SELECT
    //       *
    //   FROM
    //      users
    //   LIMIT 10
    // `;

    // const queryResult = await this.clickHouseService.query({
    //   query: sqlQuery,
    //   format: 'JSON',
    // });

    // const { data } = await queryResult.json();

    // this.logger.debug(data);
  }
}
