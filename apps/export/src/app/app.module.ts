import { CommonModule } from '@goteacher/app/common/common.module';
import { ExportAuthModule } from 'apps/export/src/app/auth/auth.module';
import { ExportCoreModule } from 'apps/export/src/app/core/core.module';
import { ExportPDFModule } from 'apps/export/src/app/pdf/pdf.module';
import { Module } from '@nestjs/common';
import { TaggerModule } from 'apps/export/src/app/tagger/tagger.module';

@Module({
  imports: [
    CommonModule,
    ExportCoreModule,
    ExportAuthModule,
    ExportPDFModule,
    TaggerModule,
  ],
  exports: [
    CommonModule,
    ExportCoreModule,
    ExportAuthModule,
    ExportPDFModule,
    TaggerModule,
  ],
})
export class ExportAppModule {}
