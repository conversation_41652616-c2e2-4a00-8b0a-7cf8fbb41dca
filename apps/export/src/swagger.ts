import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export async function setupSwagger(app: INestApplication, port: number) {
  const config = new DocumentBuilder()
    .setTitle('GoTeacher Inc Export API')
    .setDescription('GoTeacher Inc Export OpenAPI specifications.')
    .setVersion(process.env.npm_package_version)
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT Token',
        in: 'header',
      },
      'bearer',
    )
    .addServer(`http://localhost:${port}`, 'Local')
    .addServer(`https://export.dev.goteacher.com`, 'GoTeacher Development')
    .setExternalDoc('Postman Collection', '/api/docs-json')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('/api/docs', app, document, {
    swaggerOptions: {
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });
}
