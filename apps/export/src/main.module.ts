import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ExportAppModule } from 'apps/export/src/app/app.module';

import aws from './config/aws.config';
import clickhouse from './config/clickhouse.config';
import core from './config/core.config';
import jwt from './config/jwt.config';
import mongo from './config/mongo.config';
import postgres from './config/postgres.config';
import redis from './config/redis.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [aws, clickhouse, core, jwt, mongo, postgres, redis],
    }),
    ExportAppModule,
  ],
})
export class ExportMainModule {}
