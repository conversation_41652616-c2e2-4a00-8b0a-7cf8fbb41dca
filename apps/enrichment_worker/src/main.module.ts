import { APP_INTERCEPTOR } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';
import { EnrichmentWorkerAppModule } from 'apps/enrichment_worker/src/app/app.module';
import { HttpContextInterceptor } from '@goteacher/app/common/logging';
import { Module } from '@nestjs/common';
import aws from './config/aws.config';
import core from './config/core.config';
import jwt from './config/jwt.config';
import kafka from './config/kafka.config';
import mongo from './config/mongo.config';
import postgres from './config/postgres.config';
import redis from './config/redis.config';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [aws, core, jwt, kafka, postgres, redis, mongo],
    }),
    EnrichmentWorkerAppModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpContextInterceptor,
    },
  ],
})
export class EnrichmentWorkerMainModule {}
