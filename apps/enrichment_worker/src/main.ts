import { CommonConsoleLogger } from '@goteacher/app/common/logging';
import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { EnrichmentWorkerMainModule } from 'apps/enrichment_worker/src/main.module';
import qs from 'qs';

const logger = new Logger('bootstrap');

const PORT = parseInt(process.env.APP_PORT) || 3003;
const SWAGGER_ENABLED = process.env.SWAGGER_ENABLED || true;

async function configureApp(): Promise<NestFastifyApplication> {
  const app = await NestFactory.create<NestFastifyApplication>(
    EnrichmentWorkerMainModule,
    new FastifyAdapter({
      querystringParser: (str) => qs.parse(str, { ignoreQueryPrefix: true }),
    }),
  );

  app.useLogger(app.get(CommonConsoleLogger));
  app.setGlobalPrefix('api');
  app.enableCors();
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  );

  return app;
}

async function bootstrap() {
  const app = await configureApp();

  await app
    .listen(PORT, '0.0.0.0')
    .then(() => {
      logger.log(`Server is listening on port: ${PORT}`);
      logger.log(`Server is running with NODE_ENV=${process.env.NODE_ENV}.`);
    })
    .catch((err) => {
      console.error('Server failed to start!');
      console.error(err);
      process.kill(process.pid, 'SIGTERM');
    });

  ['SIGTERM', 'SIGINT', 'SIGQUIT'].forEach((signal) =>
    process.on(signal, () => {
      console.warn(`${signal} received. Shutting down...`);
      app.close();
      if (process.env.NODE_ENV === 'local') throw Error('Disrupt service!');
    }),
  );
}

bootstrap();
