import { Product } from '@goteacher/app/models/mongo/product.model';
import { ICacheService } from '@goteacher/infra/cache';
import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class ProductMatcherService {
  private readonly logger = new Logger(ProductMatcherService.name);

  constructor(
    @InjectModel(Product.name) private readonly productModel: Model<Product>,
    private readonly cacheService: ICacheService,
  ) { }

  async matchUrl(url: string): Promise<string | undefined> {
    try {
      const domain = new URL(url).hostname;
      // TODO: Use url as key when we enable path matching
      const cacheKey = `product-match:${domain}`;
      const cachedResult = await this.cacheService.get(cacheKey);
      if (cachedResult) return cachedResult as string;

      const match = await this.productModel
        .findOne(
          {
            active: true,
            domainRules: {
              $elemMatch: {
                pattern: domain,
                // TODO: add date validation once we have validFrom and validTo
                // validFrom: { $lte: new Date() },
                // $or: [{ validTo: null }, { validTo: { $gt: new Date() } }],
              },
            },
          },
          { _id: 1 },
        )
        .sort({ 'domainRules.priority': -1 });

      const productId = match?._id.toString();

      if (productId) {
        await this.cacheService.set(cacheKey, productId, 24 * 60 * 60);
      }

      return productId;
    } catch (error) {
      this.logger.error(`Error matching URL: ${url}`, error.stack);
      return undefined;
    }
  }
}
