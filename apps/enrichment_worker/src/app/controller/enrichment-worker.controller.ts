import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { getAcademicYear } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import {
  EventController,
  EventListener,
  InjectKafka,
  KafkaClient,
  KafkaEvent,
} from '@goteacher/infra/kafka';
import { Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { ProductMatcherService } from 'apps/enrichment_worker/src/app/controller/product-match.service';

@EventController('goteacher-enrichment-worker')
export class EnrichmentWorkerController {
  private readonly logger = new Logger(EnrichmentWorkerController.name);

  constructor(
    @InjectKafka('enrichment-worker')
    private readonly kafkaService: KafkaClient,
    @InjectModel(User)
    private readonly userModel: typeof User,
    private readonly cacheService: ICacheService,
    private readonly productMatcher: ProductMatcherService,
  ) { }

  @EventListener({
    groupId: 'goteacher-enrichment-worker',
    topics: ['raw_events'],
    dlq: 'raw_events_dlq',
  })
  async handleRawEvent(event) {
    const cacheKey = `enrichment-worker-user-${event.value.userId}`;
    const cacheResult = await this.cacheService.get(cacheKey);
    let user;
    if (cacheResult) user = cacheResult;
    else {
      user = await this.userModel.findOne({
        where: { id: event.value.userId },
        include: [
          {
            model: UserSchool,
            include: [
              {
                model: School,
                attributes: ['id', 'organisationId'],
              },
            ],
          },
        ],
      });
      await this.cacheService.set(cacheKey, user, 24 * 60 * 60);
    }

    const actualSchoolYear = getAcademicYear(
      new Date(event.value.clientTimestamp),
    );
    let userSchool = user?.UserSchool.find(
      (us) => us.schoolYear === actualSchoolYear,
    );

    if (!userSchool) {
      // this.logger.warn(
      //   `No user school found for user ${user?.id} and school year ${actualSchoolYear}, using first user school`,
      // );
      userSchool = user?.UserSchool[0];
    }

    // skip out_of_class teacher events for Narragansett 
    if (userSchool?.school?.organisationId === '9afaf988-f75a-4458-a8ae-020327e4793e') {
      if (user?.role === 'teacher') {
        if (!event.value.clientTimestamp) {
          return;
        }
        const date = new Date(event.value.clientTimestamp);
        const offset = event.value.clientTimezone || -4;     // numeric offset in hours

        const utcHour = date.getUTCHours();                // e.g. 14
        const localHour = (utcHour + offset + 24) % 24;

        if (localHour < 7 || localHour > 15) {
          return;
        }

        const ts = Date.parse(event.value.clientTimestamp);
        const offsetMs = offset * 60 * 60 * 1000;
        const localDate = new Date(ts + offsetMs);
        const dow = localDate.getUTCDay();

        if (dow === 0 || dow === 6) {
          return;
        }

      }
    }

    const fullDomain = event.value.url ? new URL(event.value.url).hostname : '';
    const productId = event.value.url
      ? await this.productMatcher.matchUrl(event.value.url)
      : '';

    event = {
      ...event.value,
      orgId: userSchool ? userSchool?.school?.organisationId : undefined,
      schoolId: userSchool ? userSchool?.schoolId : undefined,
      grade: userSchool ? userSchool?.grade : undefined,
      role: user?.role,
      schoolYear: actualSchoolYear,
      productId: productId,
      fullDomain: fullDomain,
    };

    await this.kafkaService.publish(
      new KafkaEvent('enriched_events', { value: event }),
    );
  }
}
