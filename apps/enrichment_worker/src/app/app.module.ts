import { CommonModule } from '@goteacher/app/common/common.module';
import { Module } from '@nestjs/common';
import { EnrichmentWorkerCoreModule } from 'apps/enrichment_worker/src/app/core/core.module';
import { EnrichmentWorkerEnrichmentModule } from 'apps/enrichment_worker/src/app/enrichment/enrichment.module';

@Module({
  imports: [
    CommonModule,
    EnrichmentWorkerCoreModule,
    EnrichmentWorkerEnrichmentModule,
  ],
  exports: [
    CommonModule,
    EnrichmentWorkerCoreModule,
    EnrichmentWorkerEnrichmentModule,
  ],
})
export class EnrichmentWorkerAppModule {}
