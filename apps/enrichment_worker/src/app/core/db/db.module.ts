import { Global, Module } from '@nestjs/common';
import { MongoModule, PostgresSequelizeModule } from '@goteacher/infra/db';

import { MongooseModelModule } from '@goteacher/app/models/mongo';
import { SequelizeModelModule } from '@goteacher/app/models/sequelize';

@Global()
@Module({
  imports: [
    PostgresSequelizeModule,
    SequelizeModelModule,
    MongoModule,
    MongooseModelModule,
  ],
  exports: [
    PostgresSequelizeModule,
    SequelizeModelModule,
    MongoModule,
    MongooseModelModule,
  ],
})
export class EnrichmentWorkerDBModule {}
