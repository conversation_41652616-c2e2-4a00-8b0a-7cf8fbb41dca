import { Global, Module } from '@nestjs/common';
import { EnrichmentWorkerAWSModule } from 'apps/enrichment_worker/src/app/core/aws/aws.module';
import { EnrichmentWorkerBrokerModule } from 'apps/enrichment_worker/src/app/core/broker/broker.module';
import { EnrichmentWorkerCacheModule } from 'apps/enrichment_worker/src/app/core/cache/cache.module';
import { EnrichmentWorkerDBModule } from 'apps/enrichment_worker/src/app/core/db/db.module';

@Global()
@Module({
  imports: [
    EnrichmentWorkerDBModule,
    EnrichmentWorkerAWSModule,
    EnrichmentWorkerBrokerModule,
    EnrichmentWorkerCacheModule,
  ],
  exports: [
    EnrichmentWorkerDBModule,
    EnrichmentWorkerAWSModule,
    EnrichmentWorkerBrokerModule,
    EnrichmentWorkerCacheModule,
  ],
})
export class EnrichmentWorkerCoreModule {}
