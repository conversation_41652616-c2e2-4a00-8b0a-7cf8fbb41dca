import { Global, Module } from '@nestjs/common';
import { ScrapingWorkerBrokerModule } from 'apps/scraping_worker/src/app/core/broker/broker.module';
import { ScrapingWorkerCacheModule } from 'apps/scraping_worker/src/app/core/cache/cache.module';
import { ScrapingWorkerDBModule } from 'apps/scraping_worker/src/app/core/db/db.module';
import { ScrapingWorkerStorageModule } from 'apps/scraping_worker/src/app/core/storage/storage.module';

@Global()
@Module({
  imports: [
    ScrapingWorkerDBModule,
    ScrapingWorkerBrokerModule,
    ScrapingWorkerCacheModule,
    ScrapingWorkerStorageModule,
  ],
  exports: [
    ScrapingWorkerDBModule,
    ScrapingWorkerBrokerModule,
    ScrapingWorkerCacheModule,
    ScrapingWorkerStorageModule,
  ],
})
export class ScrapingWorkerCoreModule { }