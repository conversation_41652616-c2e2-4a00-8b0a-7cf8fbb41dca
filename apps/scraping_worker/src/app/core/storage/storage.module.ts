import { GCSService } from '@goteacher/infra/storage/gcs.service';
import { IStorageService } from '@goteacher/infra/storage/storage.service';
import { Global, Module } from '@nestjs/common';

@Global()
@Module({
  providers: [
    {
      provide: IStorageService,
      useClass: GCSService,
    },
    GCSService,
  ],
  exports: [IStorageService, GCSService],
})
export class ScrapingWorkerStorageModule {}