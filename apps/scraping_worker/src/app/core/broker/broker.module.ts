import { KafkaModule } from '@goteacher/infra/kafka';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KafkaConfig } from 'kafkajs';

@Module({
  imports: [
    KafkaModule.forRoot([
      {
        name: 'goteacher-scraping-worker',
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => {
          let config: KafkaConfig = {
            clientId: 'goteacher-scraping-worker',
            retry: {
              retries: 10,
              initialRetryTime: 100,
            },
            brokers: configService.get('KAFKA_BROKERS').split(','),
          };

          if (configService.get<string>('NODE_ENV') != 'local') {
            config = {
              ...config,
              sasl: {
                mechanism: configService.get('KAFKA_MECHANISM') as any,
                username: configService.get('KAFKA_USERNAME'),
                password: configService.get('KAFKA_PASSWORD'),
              },
              ssl: false,
            };
          }

          return config;
        },
      },
    ]),
    KafkaModule.register([
      {
        name: 'scraping-worker',
        connName: 'goteacher-scraping-worker',
        consumers: [
          {
            options: {
              groupId: 'goteacher-scraping-worker',
              allowAutoTopicCreation: true,
              retry: {
                retries: 10,
              },
            },
            count: 5,
          },
        ],
        producer: {
          options: {
            allowAutoTopicCreation: true,
            retry: {
              retries: 10,
            },
          },
          topics: [], // This worker doesn't publish messages
        },
      },
    ]),
  ],
  providers: [],
  exports: [KafkaModule],
})
export class ScrapingWorkerBrokerModule {}