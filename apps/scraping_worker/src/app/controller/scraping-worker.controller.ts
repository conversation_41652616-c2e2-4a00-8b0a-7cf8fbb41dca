import { PageScrapingEvent } from '@goteacher/app/event/event.types';
import {
  EventController,
  EventListener,
  InjectKafka,
  KafkaClient,
} from '@goteacher/infra/kafka';
import { Logger } from '@nestjs/common';
import { ScrapingService } from '../scraping/scraping.service';

@EventController('goteacher-scraping-worker')
export class ScrapingWorkerController {
  private readonly logger = new Logger(ScrapingWorkerController.name);

  constructor(
    @InjectKafka('scraping-worker')
    private readonly kafkaService: KafkaClient,
    private readonly scrapingService: ScrapingService,
  ) { }

  @EventListener({
    groupId: 'goteacher-scraping-worker',
    topics: ['page_scraping_events'],
    dlq: 'page_scraping_events_dlq',
  })
  async handlePageScrapingEvent(event: { value: PageScrapingEvent }) {
    const scrapingEvent = event.value;

    try {
      // Process the page scraping event and store/update in MongoDB
      await this.scrapingService.processPageScrapingEvent(scrapingEvent);

      this.logger.debug(`Successfully processed page scraping event for URL: ${scrapingEvent.url}`);
    } catch (error) {
      this.logger.error(`Failed to process page scraping event for URL: ${scrapingEvent.url}`, error.stack);

      // TODO: Add retry logic or send failed events to a dead letter queue for manual review
      throw error;
    }
  }
}