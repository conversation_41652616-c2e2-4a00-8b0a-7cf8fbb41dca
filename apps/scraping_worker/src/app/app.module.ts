import { CommonModule } from '@goteacher/app/common/common.module';
import { Module } from '@nestjs/common';
import { ScrapingWorkerCoreModule } from 'apps/scraping_worker/src/app/core/core.module';
import { ScrapingWorkerScrapingModule } from 'apps/scraping_worker/src/app/scraping/scraping.module';

@Module({
  imports: [
    CommonModule,
    ScrapingWorkerCoreModule,
    ScrapingWorkerScrapingModule,
  ],
  exports: [
    CommonModule,
    ScrapingWorkerCoreModule,
    ScrapingWorkerScrapingModule,
  ],
})
export class ScrapingWorkerAppModule { }