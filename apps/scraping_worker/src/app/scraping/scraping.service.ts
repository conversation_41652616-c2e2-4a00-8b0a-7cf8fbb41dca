import { PageScrapingEvent } from '@goteacher/app/event/event.types';
import {
  PageMetadata,
  PageMetadataDocument
} from '@goteacher/app/models/mongo/page-metadata.model';
import {
  PageScreenshot,
  PageScreenshotDocument
} from '@goteacher/app/models/mongo/page-screenshot.model';

import { GCSService } from '@goteacher/infra/storage/gcs.service';
import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class ScrapingService {
  private readonly logger = new Logger(ScrapingService.name);

  constructor(
    @InjectModel(PageMetadata.name)
    private readonly pageMetadataModel: Model<PageMetadataDocument>,
    @InjectModel(PageScreenshot.name)
    private readonly pageScreenshotModel: Model<PageScreenshotDocument>,
    private readonly gcsService: GCSService,
  ) { }

  async processPageScrapingEvent(event: PageScrapingEvent): Promise<void> {
    const { url, domain, contents, metadata, screenshot, headers, ldJson } = event;

    try {
      const promises: Promise<any>[] = [];

      // Handle page metadata if we have any metadata fields
      if (contents || metadata || headers || ldJson) {
        promises.push(this.upsertPageMetadata(url, domain, contents, metadata, headers, ldJson));
      }

      // Handle screenshot if we have one
      if (screenshot) {
        promises.push(this.upsertPageScreenshot(url, domain, screenshot));
      }

      // Execute all operations in parallel
      await Promise.allSettled(promises);

      this.logger.log(`Processed page scraping event for URL: ${url}`);
    } catch (error) {
      this.logger.error(`Failed to process page scraping event for URL: ${url}`, error.stack);
      throw error;
    }
  }

  private async upsertPageMetadata(
    url: string,
    domain: string,
    contents?: Array<object>,
    metadata?: Array<object>,
    headers?: Array<string>,
    ldJson?: Array<string>
  ): Promise<void> {
    try {
      // Check if metadata record already exists
      const existingMetadata = await this.pageMetadataModel.findOne({ url }).exec();
      if (existingMetadata) {
        this.logger.debug(`Page metadata already exists for URL: ${url}, skipping`);
        return;
      }

      // Create metadata record
      const metadataData: any = { url, domain };
      if (contents) metadataData.contents = contents;
      if (metadata) metadataData.metadata = metadata;
      if (headers) metadataData.headers = headers;
      if (ldJson) metadataData.ldJson = ldJson;

      await this.pageMetadataModel.create(metadataData);
      this.logger.debug(`Created page metadata record for URL: ${url}`);
    } catch (error) {
      if (error.code === 11000) {
        this.logger.debug(`Page metadata record already exists for URL: ${url}`);
      } else {
        this.logger.warn(`Failed to create page metadata for URL: ${url}`, error.message);
        throw error;
      }
    }
  }

  private async upsertPageScreenshot(url: string, domain: string, screenshot: string): Promise<void> {
    try {
      // Check if screenshot record already exists
      const existingScreenshot = await this.pageScreenshotModel.findOne({ url }).exec();
      if (existingScreenshot) {
        this.logger.debug(`Page screenshot already exists for URL: ${url}, skipping`);
        return;
      }

      // Upload screenshot and create record
      const screenshotGcsUrl = await this.uploadScreenshotToGCS(screenshot, url);
      const imageSize = this.calculateImageSize(screenshot);

      await this.pageScreenshotModel.create({
        url,
        domain,
        screenshotGcsUrl,
        screenshotSize: imageSize
      });

      this.logger.log(`Created screenshot record for URL: ${url} (${imageSize} bytes)`);
    } catch (error) {
      if (error.code === 11000) {
        this.logger.debug(`Screenshot record already exists for URL: ${url}`);
      } else {
        this.logger.warn(`Failed to create screenshot record for URL: ${url}`, error.message);
        throw error;
      }
    }
  }

  private calculateImageSize(base64Screenshot: string): number {
    try {
      // Decode base64 to get actual file size
      return Buffer.from(base64Screenshot, 'base64').length;
    } catch (error) {
      // Fallback: estimate from base64 string length
      return Math.floor((base64Screenshot.length * 3) / 4);
    }
  }

  private async uploadScreenshotToGCS(screenshot: string, url: string): Promise<string> {
    try {
      // Generate unique filename
      const timestamp = Date.now();
      const sanitizedUrl = url.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filename = `${sanitizedUrl}/${timestamp}.png`;

      // Decode base64 screenshot
      const base64 = screenshot.replace(/^data:image\/\w+;base64,/, '');
      const screenshotBuffer = Buffer.from(base64, 'base64');

      const bucketName = process.env.SCREENSHOTS_BUCKET || 'goteacher-screenshots';
      await this.gcsService.writeObject(bucketName, filename, screenshotBuffer, { contentType: 'image/png' });

      const gcsUrl = `gs://${bucketName}/${filename}`;
      this.logger.log(`Screenshot uploaded to GCS: ${gcsUrl} for URL: ${url}`);

      return gcsUrl;
    } catch (error) {
      this.logger.error(`Failed to upload screenshot to GCS for URL: ${url}`, error.stack);
      throw error;
    }
  }
}