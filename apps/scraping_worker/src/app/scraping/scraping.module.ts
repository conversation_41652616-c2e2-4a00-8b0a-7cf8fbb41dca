import {
  PageMetadata,
  PageMetadataSchema,
} from '@goteacher/app/models/mongo/page-metadata.model';
import {
  PageScreenshot,
  PageScreenshotSchema,
} from '@goteacher/app/models/mongo/page-screenshot.model';

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScrapingWorkerController } from '../controller/scraping-worker.controller';
import { ScrapingService } from './scraping.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: PageMetadata.name,
        schema: PageMetadataSchema,
      },
      {
        name: PageScreenshot.name,
        schema: PageScreenshotSchema,
      },
    ]),
  ],
  controllers: [ScrapingWorkerController],
  providers: [ScrapingService],
  exports: [ScrapingService],
})
export class ScrapingWorkerScrapingModule { }