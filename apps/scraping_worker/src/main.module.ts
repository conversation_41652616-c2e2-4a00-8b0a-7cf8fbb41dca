import { HttpContextInterceptor } from '@goteacher/app/common/logging';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ScrapingWorkerAppModule } from 'apps/scraping_worker/src/app/app.module';
import core from './config/core.config';
import jwt from './config/jwt.config';
import kafka from './config/kafka.config';
import mongo from './config/mongo.config';
import postgres from './config/postgres.config';
import redis from './config/redis.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [core, jwt, kafka, postgres, redis, mongo],
    }),
    ScrapingWorkerAppModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpContextInterceptor,
    },
  ],
})
export class ScrapingWorkerMainModule { }