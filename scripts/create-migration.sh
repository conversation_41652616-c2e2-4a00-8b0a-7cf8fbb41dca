#!/bin/bash

# Ensure at least two arguments are provided (type and description)
if [ $# -lt 2 ]; then
  echo "Usage: $0 <clickhouse|sequelize> <description>"
  exit 1
fi

# Check if the first argument is either 'clickhouse' or 'sequelize'
type=$1
if [ "$type" != "clickhouse" ] && [ "$type" != "sequelize" ]; then
  echo "Error: The first argument must be either 'clickhouse' or 'sequelize'."
  exit 1
fi

# Timestamp in the format YYYYMMDDHHMMSS
timestamp=$(date +'%Y%m%d%H%M%S')

# Description of the migration (replace spaces with underscores and convert to lowercase)
description=$(echo "$2" | tr '[:upper:]' '[:lower:]' | tr ' ' '_' )

# Directory where migration files are stored, based on the type
migration_dir="./migrations/$type"

# Ensure the migrations directory exists
mkdir -p "$migration_dir"

# Create new migration file
migration_file="$migration_dir/$timestamp-$description.sql"
touch "$migration_file"

# Output the path of the created migration file
echo "Created new migration file: $migration_file"