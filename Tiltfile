def create_backend(root_path):
  rootPath = os.path.abspath(root_path)

  watch_settings(ignore=[
    rootPath+'/apps', 
    rootPath+'/libs', 
    rootPath+'/migrations',
    rootPath+'/scripts',
    root_path+'/package*'
  ])

  docker_build(
    ref="goteacher-api",
    context=rootPath,
    dockerfile=rootPath+"/docker/images/Dockerfile.api",
    target=os.getenv("NODE_ENV", "development"),
    live_update=[
      run(
        'npm run install',
        trigger=[rootPath+'/package.json']
      )
    ]
  )

  docker_build(
    ref="goteacher-ingestion",
    context=rootPath,
    dockerfile=rootPath+"/docker/images/Dockerfile.ingestion",
    target=os.getenv("NODE_ENV", "development"),
    live_update=[
      run(
        'npm run install',
        trigger=[rootPath+'/package.json']
      )
    ]
  )

  docker_build(
    ref="goteacher-enrichment_worker",
    context=rootPath,
    dockerfile=rootPath+"/docker/images/Dockerfile.enrichment_worker",
    target=os.getenv("NODE_ENV", "development"),
    live_update=[
      run(
        'npm run install',
        trigger=[rootPath+'/package.json']
      )
    ]
  )

  docker_build(
    ref="goteacher-seeds",
    context=rootPath,
    dockerfile=rootPath+"/docker/images/Dockerfile.seeds",
    target=os.getenv("NODE_ENV", "development"),
    live_update=[
      run(
        'npm run install',
        trigger=[rootPath+'/package.json']
      )
    ]
  )

  docker_build(
    ref="goteacher-export",
    context=rootPath,
    dockerfile=rootPath+"/docker/images/Dockerfile.export",
    target=os.getenv("NODE_ENV", "development"),
    live_update=[
      run(
        'npm run install',
        trigger=[rootPath+'/package.json']
      )
    ]
  )

  docker_build(
    ref="goteacher-migrations",
    context=rootPath,
    dockerfile=rootPath+"/docker/images/Dockerfile.migrations",
    live_update=[
      run(
        'npm run install',
        trigger=[rootPath+'/package.json']
      ),
    ]
  )


  composeFiles = listdir(rootPath + '/docker/compose', recursive=True)
  
  overrides = encode_yaml({ 
    'services': { 
      'api': { 
        'env_file': rootPath + '/docker/env/.env.api.dev',
        'volumes': [
          rootPath + '/apps/api:/usr/src/app/apps/api',
          rootPath + '/libs:/usr/src/app/libs',
          rootPath + '/dist/apps/api:/usr/src/app/dist/apps/api',
          '/usr/src/app/node_modules'
        ]
      },
      'ingestion': { 
        'env_file': rootPath + '/docker/env/.env.ingestion.local',
        'volumes': [
          rootPath + '/apps/ingestion:/usr/src/app/apps/ingestion', 
          rootPath + '/libs:/usr/src/app/libs',
          rootPath + '/dist/apps/ingestion:/usr/src/app/dist/apps/ingestion',
          '/usr/src/app/node_modules'
        ]
      },
      'enrichment_worker': { 
        'env_file': rootPath + '/docker/env/.env.enrichment_worker.local',
        'volumes': [
          rootPath + '/apps/enrichment_worker:/usr/src/app/apps/enrichment_worker', 
          rootPath + '/libs:/usr/src/app/libs',
          rootPath + '/dist/apps/enrichment_worker:/usr/src/app/dist/apps/enrichment_worker',
          '/usr/src/app/node_modules'
        ]
      },
      'seeds': { 
        'env_file': rootPath + '/docker/env/.env.seeds.local',
        'volumes': [
          rootPath + '/apps/seeds:/usr/src/app/apps/seeds',
          rootPath + '/libs:/usr/src/app/libs',
          rootPath + '/dist/apps/seeds:/usr/src/app/dist/apps/seeds',
          '/usr/src/app/node_modules'
        ]
      }, 
      'export': { 
        'env_file': rootPath + '/docker/env/.env.export.local',
        'volumes': [
          rootPath + '/apps/export:/usr/src/app/apps/export',
          rootPath + '/libs:/usr/src/app/libs',
          rootPath + '/dist/apps/export:/usr/src/app/dist/apps/export', 
          '/usr/src/app/node_modules'
        ]
      }, 
      'migrations': { 
        'env_file': rootPath + '/docker/env/.env.migrations.local',
        'volumes': [
          rootPath + '/migrations:/usr/src/app/migrations', 
          '/usr/src/app/node_modules'
        ]
      }
    }
  })
  composeFiles.append(overrides)
  docker_compose(configPaths=composeFiles, project_name="goteacher")

  dc_resource("api", labels="Api")
  dc_resource("export", labels="Api", auto_init=False)
  dc_resource("ingestion", labels="Api", auto_init=False)
  dc_resource("enrichment_worker", labels="Api", auto_init=False)
  dc_resource("seeds", labels="Tasks")
  dc_resource("migrations", labels="Migrations")