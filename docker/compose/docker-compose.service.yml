services:
  api:
    image: goteacher-api:latest
    environment:
      AWS_DEFAULT_REGION: "us-east-1"
      AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/creds"
    ports:
      - ${API_PORT}:${API_PORT}
      - ${API_DEBUG_PORT}:${API_DEBUG_PORT}
    healthcheck:
      test: curl --fail http://localhost:${API_PORT}/api/health || exit 1
      interval: 60s
      retries: 1
      start_period: 20s
      timeout: 10s
    networks:
      goteacher-network:
      credentials_network:
        ipv4_address: "*************"
    depends_on:
      - ecs-local-endpoints
      - postgres
      - pgbouncer
      - redis
      - migrations

  ingestion:
    image: goteacher-ingestion:latest
    environment:
      AWS_DEFAULT_REGION: "us-east-1"
      AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/creds"
    ports:
      - ${INGESTION_PORT}:${INGESTION_PORT}
      - ${INGESTION_DEBUG_PORT}:${INGESTION_DEBUG_PORT} 
    healthcheck:
      test: curl --fail http://localhost:${INGESTION_PORT}/api/health || exit 1
      interval: 60s
      retries: 1
      start_period: 20s
      timeout: 10s
    networks:
      goteacher-network:
      credentials_network:
        ipv4_address: "*************"
    depends_on:
      - ecs-local-endpoints
      - postgres
      - pgbouncer
      - redis
      - migrations
      - zookeeper 
      - kafka-1
      - kafka-2
      - kafka-3

  enrichment_worker:
    image: goteacher-enrichment_worker:latest
    environment:
      AWS_DEFAULT_REGION: "us-east-1"
      AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/creds"
    ports:
      - ${ENRICHMENT_WORKER_PORT}:${ENRICHMENT_WORKER_PORT}
      - ${ENRICHMENT_WORKER_DEBUG_PORT}:${ENRICHMENT_WORKER_DEBUG_PORT}
    healthcheck:
      test: curl --fail http://localhost:${ENRICHMENT_WORKER_PORT}/api/health || exit 1
      interval: 60s
      retries: 1
      start_period: 20s
      timeout: 10s
    networks:
      goteacher-network:
      credentials_network:
        ipv4_address: "*************"
    depends_on:
      - ecs-local-endpoints
      - postgres
      - pgbouncer
      - redis
      - migrations
      - zookeeper 
      - kafka-1
      - kafka-2
      - kafka-3

  seeds:
    image: goteacher-seeds:latest
    ports: 
      - ${SEEDS_DEBUG_PORT}:${SEEDS_DEBUG_PORT}
    environment:
      AWS_DEFAULT_REGION: "us-east-1"
      AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/creds"
    networks:
      goteacher-network:
      credentials_network:
        ipv4_address: "*************"
    depends_on:
      - ecs-local-endpoints
      - postgres
      - pgbouncer
      - migrations

  export: 
    image: goteacher-export:latest
    environment:
      AWS_DEFAULT_REGION: "us-east-1"
      AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/creds"
    ports:
      - ${EXPORT_PORT}:${EXPORT_PORT}
      - ${EXPORT_DEBUG_PORT}:${EXPORT_DEBUG_PORT}
    healthcheck:
      test: curl --fail http://localhost:${EXPORT_PORT}/api/health || exit 1
      interval: 60s
      retries: 1
      start_period: 20s
      timeout: 10s
    networks:
      goteacher-network:
      credentials_network:
        ipv4_address: "*************"
    depends_on:
      - ecs-local-endpoints
      - postgres
      - pgbouncer
      - migrations

  migrations:
    image: goteacher-migrations:latest
    environment:
      AWS_DEFAULT_REGION: "us-east-1"
      AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/creds"
    networks:
      goteacher-network:
      credentials_network:
        ipv4_address: "*************"
    depends_on:
      - postgres
      - pgbouncer
      - clickhouse1
      - clickhouse2
      - clickhouse3
      - clickhouse4
      - zookeeper 