APP_PORT=3009
DEBUG_PORT=9231
NODE_ENV=local

DATABASE_USERNAME=goteacher
DATABASE_PASSWORD=goteacher
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=goteacher
PGBOUNCER_HOST=pgbouncer
DATABASE_URL=postgresql://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${PGBOUNCER_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?schema=public&pgbouncer=true

AWS_PROFILE=goteacher_dev
BLOCKS_BUCKET=goteacher-dev-content-blocks

REDIS_ENDPOINT=redis://redis:6379

CLICKHOUSE_HOST=http://clickhouse:8123
CLICKHOUSE_USER=goteacher
CLICKHOUSE_PASSWORD=goteacher
CLICKHOUSE_DB=goteacher

JWT_SECRET=at-secret
JWT_REFRESH_SECRET=rt-secret

SWAGGER_ENABLED=true

MONGO_USERNAME=admin
MONGO_PASSWORD=admin
MONGO_DATABASE=goteacher
MONGO_URL=mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@mongo:27017