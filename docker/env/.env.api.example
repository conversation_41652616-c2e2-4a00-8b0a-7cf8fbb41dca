APP_PORT=3003
DEBUG_PORT=9229
NODE_ENV=local

DATABASE_USERNAME=goteacher
DATABASE_PASSWORD=goteacher
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=goteacher
PGBOUNCER_HOST=pgbouncer
DATABASE_URL=postgresql://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${PGBOUNCER_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?schema=public&pgbouncer=true

AWS_PROFILE=goteacher_dev
BLOCKS_BUCKET=goteacher-dev-content-blocks

REDIS_ENDPOINT=redis://redis:6379

CLICKHOUSE_HOST=http://clickhouse:8123
CLICKHOUSE_USER=goteacher
CLICKHOUSE_PASSWORD=goteacher
CLICKHOUSE_DB=goteacher

GOOGLE_OAUTH_CLIENT_ID=<GOOGLE_OAUTH_CLIENT_ID>
GOOGLE_OAUTH_CLIENT_SECRET=<GOOGLE_OAUTH_CLIENT_SECRET>
GOGOLE_OAUTH_REDIRECT_URI=http://localhost:3000/auth/oauth/google
GOOGLE_TOPIC_NAME=projects/goteacher-dev/topics/classroom

APPLE_OAUTH_PRIVATE_KEY=<APPLE_OAUTH_PRIVATE_KEY>
APPLE_OAUTH_CLIENT_ID=<APPLE_OAUTH_CLIENT_ID>
APPLE_OAUTH_KEY_ID=<APPLE_OAUTH_KEY_ID>
APPLE_OAUTH_TEAM_ID=<APPLE_OAUTH_TEAM_ID>
APPLE_OAUTH_REDIRECT_URI=http://api.dev.goteacher.com/api/oauth/apple/success

JWT_SECRET=at-secret
JWT_REFRESH_SECRET=rt-secret

SWAGGER_ENABLED=true

MONGO_USERNAME=admin
MONGO_PASSWORD=admin
MONGO_DATABASE=goteacher
MONGO_URL=mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@mongo:27017