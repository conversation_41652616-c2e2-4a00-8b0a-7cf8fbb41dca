APP_PORT=3004
DEBUG_PORT=9238
NODE_ENV=local

DATABASE_USERNAME=goteacher
DATABASE_PASSWORD=goteacher
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=goteacher
PGBOUNCER_HOST=pgbouncer
DATABASE_URL=postgresql://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${PGBOUNCER_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?schema=public&pgbouncer=true

AWS_PROFILE=goteacher_dev
SCREENSHOTS_BUCKET=goteacher-dev-screenshots

REDIS_ENDPOINT=redis://redis:6379

CLICKHOUSE_HOST=http://clickhouse:8123
CLICKHOUSE_USER=goteacher
CLICKHOUSE_PASSWORD=goteacher
CLICKHOUSE_DB=goteacher

JWT_SECRET=at-secret
JWT_REFRESH_SECRET=rt-secret

KAFKA_BROKERS=kafka-1:9092,kafka-2:9092,kafka-3:9092
KAFKA_MECHANISM=scram-sha-512
KAFKA_USERNAME=goteacher
KAFKA_PASSWORD=goteacher

MONGO_URL=mongodb://mongo:27017
MONGO_DATABASE=goteacher

# High Volume Processing Configuration
KAFKA_SESSION_TIMEOUT=30000
KAFKA_HEARTBEAT_INTERVAL=3000
KAFKA_MAX_POLL_RECORDS=100
KAFKA_FETCH_MAX_WAIT=500

# MongoDB Connection Pool for High Volume
MONGO_MAX_POOL_SIZE=20
MONGO_MIN_POOL_SIZE=5
MONGO_MAX_IDLE_TIME=30000

# Screenshot Processing
MAX_SCREENSHOT_SIZE_MB=10
SCREENSHOT_UPLOAD_TIMEOUT=30000