APP_PORT=3010
DEBUG_PORT=9232
NODE_ENV=local

DATABASE_USERNAME=goteacher
DATABASE_PASSWORD=goteacher
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=goteacher
PGBOUNCER_HOST=pgbouncer
DATABASE_URL=postgresql://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${PGBOUNCER_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?schema=public&pgbouncer=true

AWS_PROFILE=goteacher_dev

REDIS_ENDPOINT=redis://redis:6379

CLICKHOUSE_HOST=http://clickhouse:8123
CLICKHOUSE_USER=goteacher
CLICKHOUSE_PASSWORD=goteacher
CLICKHOUSE_DB=goteacher

JWT_SECRET=at-secret
JWT_REFRESH_SECRET=rt-secret

KAFKA_BROKERS=kafka-1:9092,kafka-2:9092,kafka-3:9092
KAFKA_MECHANISM=scram-sha-512
KAFKA_USERNAME=goteacher
KAFKA_PASSWORD=goteacher

INGESTION_SINK=goteacher-dev-ingestion-sink

SWAGGER_ENABLED=true