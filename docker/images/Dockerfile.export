FROM node:20.12.2-alpine3.19 as build
RUN apk --no-cache add curl
WORKDIR /usr/src/app
ADD package*.json ./
RUN npm install
ADD . .
RUN npm run export:build
RUN apk add --no-cache \
  chromium \
  nss \
  freetype \
  harfbuzz \
  ca-certificates \
  ttf-freefont 
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

FROM build AS development
WORKDIR /usr/src/app
COPY --from=build /usr/src/app/ .
ENTRYPOINT npm run export:start:debug

FROM build AS production
WORKDIR /usr/src/app
COPY --from=build /usr/src/app/node_modules ./node_modules
COPY --from=build /usr/src/app/dist/ ./dist/
COPY --from=build /usr/src/app/package*.json ./
ENTRYPOINT npm run export:start:prod
