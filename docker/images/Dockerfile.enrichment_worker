FROM node:20.12.2-alpine3.19 as build
RUN apk --no-cache add curl
WORKDIR /usr/src/app
ADD package*.json ./
RUN npm install
ADD . .
RUN npm run enrichment_worker:build

FROM build AS development
WORKDIR /usr/src/app
COPY --from=build /usr/src/app/ .
ENTRYPOINT npm run enrichment_worker:start:debug

FROM build AS production
WORKDIR /usr/src/app
COPY --from=build /usr/src/app/node_modules ./node_modules
COPY --from=build /usr/src/app/dist/ ./dist/
COPY --from=build /usr/src/app/package*.json ./
ENTRYPOINT npm run enrichment_worker:start:prod
