{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "Bash(npm run lint)", "mcp__zen__thinkdeep", "<PERSON><PERSON>(mkdir:*)", "mcp__zen__chat", "Bash(rm:*)", "mcp__ide__getDiagnostics", "Bash(npx ts-node:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "Bash(gh pr view:*)", "mcp__zen__codereview", "mcp__zen__debug", "Bash(git log:*)", "Bash(gh pr checkout:*)"], "deny": []}, "enableAllProjectMcpServers": false, "enabledMcpjsonServers": ["mcp-clickhouse"]}