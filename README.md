<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="200" alt="Nest Logo" /></a>
  <h1 align="center">GoTeacher NestJS Backend Monorepo</h1>
</p>



<p align="center">
  <!-- node version -->
  <img src="https://img.shields.io/badge/node-v20.12.2-green.svg" alt="Node Version" />
  <!-- npm version -->
  <img src="https://img.shields.io/badge/npm-v10.5.0-blue.svg" alt="NPM Version" />
</p>

## Description

<p>Monorepo for <span style="font-weight: bold">GoTeacher</span> backend. It contains all the necessary services and libraries for the project. Configured using <a href="https://docs.nestjs.com/cli/monorepo#monorepo-mode">NestJS workspaces</a>.</p>

## Setup
In order to setup the project, you need to follow the steps below:
1. Install [NVM](https://github.com/nvm-sh/nvm) 
2. Install [Node](https://nodejs.org/en/download) version specified in [`.nvmrc`](./.nvmrc) file. You can do this by running the following command:
    ```bash
      $ nvm install
    ```
3. Install dependencies by running the following command:
    ```bash
      $ npm i
    ```
4. Create `.env` files in the [docker](./docker) directory and copy the content of [`.env.example`](./.env.example) file to it. ex: `docker/env/.env.auth.local`   

    > <span style="color: orange;">**Warning:**</span> You need to create `.env` files for each service.
5. If you haven't already cloned the [tilt](https://github.com/GoTeacher-Inc/tilt) repo, clone the tilt repo and follow the instructions in the [README](https://github.com/GoTeacher-Inc/tilt/blob/main/README.md) file.
6. (Optional) We have recommended extensions if you'd like to follow our practices.

You run the monorepo from the [tilt](https://github.com/GoTeacher-Inc/tilt) repo.

## Debugging the app
In order to debug the app, there are [VSCode](https://code.visualstudio.com/) tasks available for each service. Currently support is only for [VSCode](https://code.visualstudio.com/). However when running [tilt](https://github.com/GoTeacher-Inc/tilt), each service exposes it's own debug port. View them either in the tilt repo .env file or here in [launch.json](./.vscode/launch.json).

![Alt text](./assets/debugging.png)


<h1 align="center" style="color: orange; margin-top: 100px;">Have fun!</h1>