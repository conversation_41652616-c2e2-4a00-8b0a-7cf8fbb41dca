import { CompressionTypes, IHeaders, KafkaMessage, ProducerRecord } from "kafkajs";
import { v4 } from "uuid";

export interface IKafkaEventBody<T> {
  key?: string;
  value: T;
  partition?: number;
  headers?: IHeaders;
  timestamp?: string;
}

export class KafkaEvent<T> implements IKafkaEventBody<T> {
  public topic: string;
  public value: T;
  public acks?: number;
  public timeout?: number;
  public compression?: CompressionTypes;

  public key?: string;
  public partition?: number;
  public headers?: IHeaders;
  public timestamp?: string;

  constructor(
    topic: string,
    event: IKafkaEventBody<T>,
    acks?: number,
    timeout?: number,
    compression?: CompressionTypes,
  ) {
    this.topic = topic;
    this.acks = acks;
    this.timeout = timeout;
    this.compression = compression;

    // if (!event.key) this.key = v4();

    Object.assign(this, event);
  }

  toRecord(): ProducerRecord {
    const value = typeof this.value === "object" ? JSON.stringify(this.value) : String(this.value);
    return {
      topic: this.topic,
      acks: this.acks,
      timeout: this.timeout,
      compression: this.compression,
      messages: [
        {
          key: this.key,
          value,
          partition: this.partition,
          headers: this.headers,
          timestamp: this.timestamp,
        },
      ],
    };
  }

  static toEvent(topic: string, message: KafkaMessage, partition?: number): KafkaEvent<any> {
    let value = "";
    try {
      value = JSON.parse(message?.value?.toString());
    } catch {
      value = message?.value?.toString();
    }

    return new KafkaEvent<any>(topic, {
      key: message?.key?.toString() ?? v4(),
      value,
      partition: partition,
      timestamp: message.timestamp,
      headers: message.headers,
    });
  }
}
