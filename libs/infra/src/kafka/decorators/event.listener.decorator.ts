import { Topic } from "../types/module.types";

export const EVENT_LISTENER_CLIENT_METADATA = "EVENT_LISTENER_CLIENT_METADATA";

export const EventListener = (args: { groupId: string; topics: Topic[]; dlq: Topic }): MethodDecorator => {
  return (target, propertyKey, descriptor) => {
    const uniqueTopics = [...new Set(args.topics)];

    Reflect.defineMetadata(
      EVENT_LISTENER_CLIENT_METADATA,
      uniqueTopics.map((topic) => ({ topic, groupId: args.groupId, dlq: args.dlq, handler: descriptor.value })),
      target,
      propertyKey,
    );
  };
};
