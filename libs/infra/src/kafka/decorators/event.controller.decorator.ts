import { Controller } from "@nestjs/common";
import { getKafkaControllerToken } from "../util/token";

export const EVENT_CONTROLLER_CLIENT_METADATA = "EVENT_CONTROLLER_CLIENT_METADATA";

export const EventController = (clientName: string): ClassDecorator => {
  return (target) => {
    Controller(getKafkaControllerToken(clientName))(target);

    Reflect.defineMetadata(EVENT_CONTROLLER_CLIENT_METADATA, clientName, target);
  };
};
