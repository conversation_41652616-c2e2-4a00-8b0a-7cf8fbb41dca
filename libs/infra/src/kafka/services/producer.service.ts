import { <PERSON><PERSON> } from "@nestjs/common";
import { <PERSON><PERSON><PERSON>, Producer, RecordMetadata } from "kafkajs";
import { KafkaEvent } from "../types/kafka.event";
import { KafkaProducerConfig, Topic } from "../types/module.types";

export class ProducerService {
  private readonly logger = new Logger(ProducerService.name);

  public clientName: string;
  public producer: Producer;
  public topics: Topic[];

  constructor(conn: Kafka, clientName: string, args: KafkaProducerConfig) {
    this.clientName = clientName;
    this.producer = conn.producer(args.options);
    this.topics = args.topics;
  }

  async connect() {
    await this.producer.connect();
  }

  async disconnect() {
    await this.producer.disconnect();
  }

  public canPublish(topicName: string): boolean {
    return this.topics.includes(topicName);
  }

  async publish(event: KafkaEvent<any>): Promise<RecordMetadata[]> {
    // if (!this.canPublish(event.topic)) {
    //   this.logger.error(
    //     `Kafka service \`${this.clientName}\` is not allowed to produce message on topic: ${event.topic}!`,
    //   );
    //   return;
    // }

    try {
      return await this.producer.send(event.toRecord());
    } catch (error) {
      this.logger.error(`Failed to publish kafka event to \`${event.topic}\` from service: ${this.clientName}!`);
    }
  }
}
