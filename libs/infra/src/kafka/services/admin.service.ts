import { Logger } from "@nestjs/common";
import { Ad<PERSON>, Kafka } from "kafkajs";

export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  public clientName: string;
  public admin: Admin;

  constructor(conn: Ka<PERSON><PERSON>, clientName: string) {
    this.clientName = clientName;
    this.admin = conn.admin();
  }

  async connect() {
    await this.admin.connect();
    await this.admin.describeCluster();
  }

  async disconnect() {
    await this.admin.disconnect();
  }
}

