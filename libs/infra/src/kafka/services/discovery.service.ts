import { Global, Injectable, Logger } from "@nestjs/common";
import { Controller } from "@nestjs/common/interfaces";
import { ContextIdFactory, ModuleRef, ModulesContainer } from "@nestjs/core";
import { InstanceWrapper } from "@nestjs/core/injector/instance-wrapper";
import { Modu<PERSON> } from "@nestjs/core/injector/module";
import { EVENT_CONTROLLER_CLIENT_METADATA } from "../decorators/event.controller.decorator";
import { EVENT_LISTENER_CLIENT_METADATA } from "../decorators/event.listener.decorator";
import { DiscoveryTarget, EventControllerMetadata, EventListenerMetadata, Topic } from "../types/module.types";

@Global()
@Injectable()
export class DiscoveryService {
  private readonly logger = new Logger(DiscoveryService.name);
  constructor(private modulesContainer: ModulesContainer, private moduleRef: ModuleRef) {}

  async discover(consumerConfig: { clientName: string; groupId: string }): Promise<DiscoveryTarget[]> {
    const modules: Module[] = [...this.modulesContainer.values()];
    const targets = await this.discoverTargets(consumerConfig, modules);

    return targets;
  }

  async discoverTargets(
    consumerConfig: { clientName: string; groupId: string },
    modules: Module[],
  ): Promise<DiscoveryTarget[]> {
    const controllers = modules.flatMap((module) => [...module.controllers.values()]);

    const targets = await Promise.all(
      controllers.map((controller) => this.discoverController(controller, consumerConfig)),
    );

    return targets.flat();
  }

  async discoverController(
    controller: InstanceWrapper<Controller>,
    consumerConfig: { clientName: string; groupId: string },
  ): Promise<DiscoveryTarget[]> {
    const targets: DiscoveryTarget[] = [];

    const meta: EventControllerMetadata = Reflect.getMetadata(
      EVENT_CONTROLLER_CLIENT_METADATA,
      controller.instance.constructor,
    );

    if (meta) {
      const listeners = this.discoverListeners(consumerConfig, controller);
      const contextId = ContextIdFactory.create();
      const instance = await this.moduleRef.resolve(controller.token, contextId, { strict: false });

      listeners.forEach((listener) => {
        const existingTarget = targets.find((t) => t.topic === listener.topic);
        if (existingTarget) {
          existingTarget.handlers.push({
            target: instance,
            dlq: listener.dlq,
            handler: listener.handler.bind(instance),
          });
        } else {
          targets.push({
            topic: listener.topic,
            clientName: consumerConfig.clientName,
            groupId: listener.groupId,
            handlers: [{ target: instance, handler: listener.handler.bind(instance), dlq: listener.dlq }],
          });
        }
      });
    }

    return targets;
  }

  discoverListeners(
    consumerConfig: { clientName: string; groupId: string },
    controller: InstanceWrapper<object>,
  ): { topic: Topic; dlq: Topic; handler: any; groupId: string }[] {
    const listeners: { topic: Topic; dlq: Topic; handler: any; groupId: string }[] = [];

    const prototype = Object.getPrototypeOf(controller.instance);
    const methods = Object.getOwnPropertyNames(prototype);
    for (const method of methods) {
      const meta: EventListenerMetadata = Reflect.getMetadata(EVENT_LISTENER_CLIENT_METADATA, prototype, method);
      if (meta) {
        listeners.push(
          ...meta
            .filter((metaEntry) => metaEntry.groupId === consumerConfig.groupId)
            .map((metaEntry) => ({
              topic: metaEntry.topic,
              dlq: metaEntry.dlq,
              handler: metaEntry.handler,
              groupId: metaEntry.groupId,
            })),
        );
      }
    }

    return listeners.flat();
  }
}
