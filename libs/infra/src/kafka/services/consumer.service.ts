import { Logger } from "@nestjs/common";
import { Consumer, EachMessagePayload, Kafka, KafkaJSNonRetriableError, KafkaJSNumberOfRetriesExceeded } from "kafkajs";
import { KafkaEvent } from "../types/kafka.event";
import { DiscoveryTarget, KafkaConsumerConfig } from "../types/module.types";
import { getKafkaClientToken } from "../util/token";
import { DiscoveryService } from "./discovery.service";
import { ProducerService } from "./producer.service";

export function shouldRestart(error: Error): boolean {
  const isNonRetriableError = error instanceof KafkaJSNonRetriableError;
  const isNumberOfRetriesExceeded = error instanceof KafkaJSNumberOfRetriesExceeded;

  return isNonRetriableError && !isNumberOfRetriesExceeded;
}

export class ConsumerService {
  private readonly logger = new Logger(ConsumerService.name);

  public id: string;
  public clientName: string;
  public clientToken: string;
  public groupId: string;
  public consumer: Consumer;
  public targets: DiscoveryTarget[];
  public removeListener;

  constructor(
    conn: Kafka,
    clientName: string,
    args: KafkaConsumerConfig,
    private discoveryService: DiscoveryService,
    private producerService: ProducerService,
  ) {
    this.clientName = clientName;
    this.groupId = args.options.groupId;
    this.clientToken = getKafkaClientToken(this.clientName);
    this.consumer = conn.consumer(args.options);
  }

  async discover() {
    this.targets = (
      await this.discoveryService.discover({ clientName: this.clientName, groupId: this.groupId })
    ).flat();
    this.logger.debug(`Discovery result for consumer: ${this.clientName} | ${this.groupId}`);
    for (const target of this.targets) {
      this.logger.debug(target.topic);
    }
  }

  async connect() {
    await this.consumer.connect();
    await this.discover();
    await this.subscribe();

    await this.run();

    const { CRASH } = this.consumer.events;

    this.removeListener = this.consumer.on(CRASH, async (payload) => {
      if (!shouldRestart(payload.payload.error)) {
        return;
      }

      await this.disconnect();

      await this.consumer.connect();
      await this.subscribe();
      await this.run();
    });
  }

  async disconnect() {
    await this.removeListener();
    await this.consumer.disconnect();
  }

  async subscribe() {
    const topics = this.targets.map((target) => target.topic);
    this.consumer.subscribe({ topics });
    this.logger.debug(`Consumer ${this.clientName} | ${this.groupId} subscribed to: ["${topics.join(`","`)}"]`);
  }

  async run() {
    this.consumer.run({
      eachMessage: this.consume.bind(this),
    });
  }

  async consume({ topic, partition, message, heartbeat, pause }: EachMessagePayload) {
    const mapping = this.targets.find(
      (target) => target.clientName === this.clientName && target.groupId === this.groupId && topic === target.topic,
    );

    if (!mapping) {
      this.logger.log(`No mapping found! ${this.clientName}, ${this.groupId}, ${topic}`);
      return;
    }

    for (const subscriber of mapping.handlers) {
      await this.processMessage(subscriber, { topic, partition, message, heartbeat, pause }, 0);
    }
  }

  async processMessage(subscriber, record: { topic; partition; message; heartbeat; pause }, retryCount) {
    const maxRetries = 3;

    const event = KafkaEvent.toEvent(record.topic, record.message, record.partition);
    // this.logger.log(`[${this.clientName}|${this.groupId}] ${record.topic}-${event.key}`, JSON.stringify(event.value));

    try {
      await subscriber.handler(event);
    } catch (error) {
      const isDLQ = event.topic === subscriber.dlq;
      if (retryCount < maxRetries && !isDLQ) {
        this.logger.error(
          `[${this.clientName}|${this.groupId}] ${event.topic}-${event.key} will be retried, failed to process with following error:`,
          error,
        );
        return this.processMessage(subscriber, record, retryCount + 1);
      }

      if (!isDLQ) {
        this.producerService.publish(KafkaEvent.toEvent(subscriber.dlq, record.message));
        this.logger.error(
          `[${this.clientName}|${this.groupId}] ${event.topic}-${event.key} sending to DLQ ${subscriber.dlq}.`,
        );
      } else {
        this.logger.error(
          `[${this.clientName}|${this.groupId}] ${event.topic}-${event.key} failed to process with following error:`,
          error,
        );
      }
    }
  }
}
