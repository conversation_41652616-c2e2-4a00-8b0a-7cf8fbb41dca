import {
  Injectable,
  Logger,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import { Kafka, RecordMetadata } from 'kafkajs';

import { AdminService } from './services/admin.service';
import { ConsumerService } from './services/consumer.service';
import { DiscoveryService } from './services/discovery.service';
import { KafkaEvent } from './types/kafka.event';
import { KafkaServiceArguments } from './types/module.types';
import { ProducerService } from './services/producer.service';

@Injectable()
export class KafkaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KafkaClient.name);

  public consumerGroups: Map<string, ConsumerService[]> = new Map<
    string,
    ConsumerService[]
  >();
  public producer: ProducerService;
  public admin: AdminService;

  private options: KafkaServiceArguments;

  constructor(
    conn: Kafka,
    args: KafkaServiceArguments,
    discoveryService: DiscoveryService,
  ) {
    this.producer = new ProducerService(conn, args.name, args.producer);
    this.admin = new AdminService(conn, args.name);
    for (const consumerConfig of args.consumers) {
      const count = consumerConfig?.count ?? 1;

      this.consumerGroups.set(
        consumerConfig.options.groupId,
        Array.from(
          { length: count },
          () =>
            new ConsumerService(
              conn,
              args.name,
              consumerConfig,
              discoveryService,
              this.producer,
            ),
        ),
      );
    }

    this.options = args;
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  async connect() {
    this.logger.log(`Connecting kafka service ${this.options.name}...`);
    await this.admin.connect();
    await this.producer.connect();
    for (const consumerGroup of this.consumerGroups.values()) {
      await Promise.all(consumerGroup.map((consumer) => consumer.connect()));
    }
    this.logger.log(
      `Connected successfully kafka service ${this.options.name}!`,
    );
  }

  async disconnect() {
    this.logger.log(`Disconnecting kafka service ${this.options.name}...`);
    for (const consumerGroup of this.consumerGroups.values()) {
      await Promise.all(consumerGroup.map((consumer) => consumer.disconnect()));
    }
    await this.producer.disconnect();
    await this.admin.disconnect();
    this.logger.log(
      `Disconnected successfully kafka service ${this.options.name}!`,
    );
  }

  async publish(event: KafkaEvent<any>): Promise<RecordMetadata[]> {
    return await this.producer.publish(event);
  }
}
