import { DynamicModule, Logger, Module, Provider } from "@nestjs/common";
import { MetadataScanner } from "@nestjs/core";
import { KafkaLogger } from "@nestjs/microservices";
import { Kafka } from "kafkajs";
import { KafkaClient } from "./kafka.client";
import { DiscoveryService } from "./services/discovery.service";
import { KafkaForRootArgument, KafkaForRootArguments, KafkaRegisterArguments } from "./types/module.types";
import { getKafkaClientToken, getKafkaConnectionToken } from "./util/token";

@Module({
  imports: [MetadataScanner],
  providers: [DiscoveryService],
  exports: [],
})
export class KafkaModule {
  private static readonly logger = new Logger(KafkaModule.name);

  public static forRoot(connections: KafkaForRootArguments): DynamicModule {
    const providers: Provider[] = [];
    for (const connection of connections) {
      providers.push(KafkaModule.buildAsyncConnectionProviders(connection));
    }
    return {
      module: KafkaModule,
      imports: connections.map((conn) => conn.imports || []).flat(),
      exports: providers,
      providers,
    };
  }

  private static buildAsyncConnectionProviders(option: KafkaForRootArgument): Provider {
    return {
      provide: getKafkaConnectionToken(option.name),
      inject: [...(option.inject || [])],
      useFactory: (...args) => {
        const clientOptions = option.useFactory(...args);
        const client = new Kafka({ ...clientOptions, logCreator: KafkaLogger.bind(null, this.logger) });
        return client;
      },
    };
  }

  public static register(services: KafkaRegisterArguments): DynamicModule {
    const providers: Provider[] = [];

    for (const service of services) {
      providers.push({
        provide: getKafkaClientToken(service.name),
        useFactory: (conn: Kafka, discoveryService: DiscoveryService) => {
          return new KafkaClient(conn, service, discoveryService);
        },
        inject: [getKafkaConnectionToken(service.connName), DiscoveryService],
      });
    }

    return {
      module: KafkaModule,
      providers: providers,
      exports: providers,
    };
  }
}
