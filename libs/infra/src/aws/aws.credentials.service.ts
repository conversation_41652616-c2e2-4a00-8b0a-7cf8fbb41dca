import { fromNodeProviderChain } from "@aws-sdk/credential-providers";
import { AwsCredentialIdentityProvider } from "@aws-sdk/types";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class AWSCredentialsService {
  constructor(private configService: ConfigService) {}

  public async getCredentials(
    region: string = this.configService.get<string>("AWS_REGION"),
    profile: string = this.configService.get<string>("AWS_PROFILE"),
  ): Promise<AwsCredentialIdentityProvider> {
    return fromNodeProviderChain({
      profile,
      clientConfig: {
        region,
      },
    });
  }
}
