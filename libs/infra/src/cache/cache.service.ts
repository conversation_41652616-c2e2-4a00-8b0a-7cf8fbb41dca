import { NotImplementedException } from '@nestjs/common';

export class ICacheService {
  public set<T>(key: string, value: T, ttl?: number): Promise<void> {
    throw new NotImplementedException('Not implemented yet!');
  }

  public get<T>(key: string): Promise<T> {
    throw new NotImplementedException('Not implemented yet!');
  }

  public del(key: string): Promise<void> {
    throw new NotImplementedException('Not implemented yet!');
  }

  public invalidate(pattern: string): Promise<void> {
    throw new NotImplementedException('Not implemented yet!');
  }

  public genKey(data: string): string {
    throw new NotImplementedException('Not implemented yet!');
  }
}
