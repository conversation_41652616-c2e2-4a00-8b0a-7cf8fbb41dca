import { CompleteMultipartUploadCommandOutput } from '@aws-sdk/client-s3';
import {
  GetReadRequest,
  GetWriteRequest,
  MultiPartChunkRequest,
  MultiPartEndRequest,
  MultiPartStartRequest,
} from '@goteacher/infra/storage/storage.types';
import { Injectable, NotImplementedException } from '@nestjs/common';

@Injectable()
export class IStorageService {
  public client;
  public async duplicateObject(
    destinationBucket: string,
    objectKeyPath: string,
    newObjectKey: string,
  ): Promise<void> {
    throw new NotImplementedException('Not Implemented!');
  }
  public async initiateMultiPart(args: MultiPartStartRequest): Promise<string> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async getMultiPartChunkURL(
    args: MultiPartChunkRequest,
  ): Promise<{ url: string }> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async completeMultiPart(
    args: MultiPartEndRequest,
  ): Promise<CompleteMultipartUploadCommandOutput> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async getReadUrl(args: GetReadRequest): Promise<{ url: string }> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async getWriteUrl(
    args: GetWriteRequest,
  ): Promise<{ url: string; path: string }> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async getObjectAsString(
    container: string,
    path: string,
  ): Promise<string> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async getObjectAsWebStream(
    container: string,
    path: string,
  ): Promise<ReadableStream> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async getObjectAsByteArray(
    container: string,
    path: string,
  ): Promise<Uint8Array> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async writeObject(
    container: string,
    path: string,
    data: string | Buffer,
  ): Promise<void> {
    throw new NotImplementedException('Not Implemented!');
  }

  public async getChecksum(
    container: string,
    path: string,
  ): Promise<string | null> {
    throw new NotImplementedException('Not Implemented!');
  }
}
