import { ApiProperty } from '@nestjs/swagger';

export function bytesToMegabytes(size: number) {
  return Math.ceil(size / 1024 / 1024);
}

export function megabytesToBytes(size: number) {
  return Math.ceil(size * 1024 * 1024);
}

export function bytesToGigabytes(size: number) {
  return Math.ceil(size / 1024 / 1024 / 1024);
}

export function gigabytesToBytes(size: number) {
  return Math.ceil(size * 1024 * 1024 * 1024);
}

export class Part {
  @ApiProperty({
    description: 'ETag, returned as header from PUT request.',
  })
  ETag: string;

  @ApiProperty({
    description:
      'Part number identfier. Ordering is important since it makes sure that the file is put together correctly.',
  })
  PartNumber: number;
}

export const MAX_SIZE = gigabytesToBytes(5);

export enum PRESIGN_METHOD_TYPE {
  POST = 'POST',
  PUT = 'PUT',
  GET = 'GET',
}

export enum AcceptedMIMEs {
  Video = 'video/mp4',
  VideoAvi = 'video/x-msvideo',
  DocumentPdf = 'application/pdf',
  DocumentWord = 'application/msword',
  DocumentWordx = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  DocumentExcel = 'application/vnd.ms-excel',
  DocumentExcelx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  DocumentPowerpoint = 'application/vnd.ms-powerpoint',
  DocumentPowerpointx = 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ImageBmp = 'image/bmp',
  ImageJpeg = 'image/jpeg',
  ImageXPng = 'image/x-png',
  ImagePng = 'image/png',
  ImageGif = 'image/gif',
  ImageSvg = 'image/svg+xml',
  Archive7z = 'application/x-7z-compressed',
  ArchiveZip = 'application/zip',
  ArchiveTar = 'application/x-tar',
}

export const SIZE_LIMIT: Record<AcceptedMIMEs, number> = {
  'image/bmp': megabytesToBytes(50),
  'image/jpeg': megabytesToBytes(50),
  'image/x-png': megabytesToBytes(50),
  'image/png': megabytesToBytes(50),
  'image/gif': megabytesToBytes(50),
  'image/svg+xml': megabytesToBytes(50),
  'video/mp4': gigabytesToBytes(2),
  'video/x-msvideo': gigabytesToBytes(2),
  'application/pdf': megabytesToBytes(50),
  'application/msword': megabytesToBytes(50),
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    megabytesToBytes(50),
  'application/vnd.ms-excel': megabytesToBytes(50),
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
    megabytesToBytes(50),
  'application/vnd.ms-powerpoint': megabytesToBytes(50),
  'application/vnd.openxmlformats-officedocument.presentationml.presentation':
    megabytesToBytes(50),
  'application/x-7z-compressed': gigabytesToBytes(1),
  'application/zip': gigabytesToBytes(1),
  'application/x-tar': gigabytesToBytes(1),
};

export type MultiPartStartRequest = {
  expiresInMinutes?: number;

  file: {
    fileName: string;
    fileSize: number;
    mime: AcceptedMIMEs;
  };

  target: {
    container: string;
    path: string;
  };
};

export type MultiPartChunkRequest = {
  expiresInMinutes?: number;

  multiPart: {
    id: string;
    partNumber: number;
  };

  target: {
    container: string;
    path: string;
  };
};

export type MultiPartEndRequest = {
  expiresInMinutes?: number;

  multiPart: {
    id: string;
    parts: Part[];
  };

  target: {
    container: string;
    path: string;
  };
};

export type GetReadRequest = {
  expiresInMinutes?: number;
  filename: string;
  target: {
    container: string;
    path: string;
  };
};

export type GetWriteRequest = {
  expiresInMinutes?: number;

  target: {
    container: string;
    path: string;
  };
};
