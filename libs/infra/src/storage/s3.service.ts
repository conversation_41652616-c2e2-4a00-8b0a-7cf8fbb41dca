import {
  CompleteMultipartUploadCommand,
  CompleteMultipartUploadCommandOutput,
  CopyObjectCommand,
  CreateMultipartUploadCommand,
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
  S3Client,
  UploadPartCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { AWSCredentialsService } from '@goteacher/infra/aws/aws.credentials.service';
import { IStorageService } from '@goteacher/infra/storage/storage.service';
import {
  bytesToGigabytes,
  GetReadRequest,
  GetWriteRequest,
  MAX_SIZE,
  MultiPartChunkRequest,
  MultiPartEndRequest,
  MultiPartStartRequest,
  SIZE_LIMIT,
} from '@goteacher/infra/storage/storage.types';
import moment from 'moment';

@Injectable()
export class AWSS3Service implements IStorageService {
  public client: S3Client;

  constructor(
    private configService: ConfigService,
    private awsCredentialsService: AWSCredentialsService,
  ) {
    this.initClient();
  }

  public async duplicateObject(
    destinationBucket: string,
    objectKeyPath: string,
    newObjectKey: string,
  ): Promise<void> {
    await this.client.send(
      new CopyObjectCommand({
        Bucket: destinationBucket,
        CopySource: `/${destinationBucket}/${objectKeyPath}`,
        Key: newObjectKey,
      }),
    );
  }

  private async initClient() {
    const options = {
      credentials: await this.awsCredentialsService.getCredentials(),
      region: this.configService.get<string>('AWS_REGION'),
    };

    this.client = new S3Client(options);
  }

  // WHAT A DIRTY BOY
  public async getReadUrl({
    target,
    filename,
    expiresInMinutes = 15,
  }: GetReadRequest): Promise<{ url: string }> {
    const url = await getSignedUrl(
      this.client,
      new GetObjectCommand({
        Bucket: target.container,
        Key: target.path,
        ...(filename
          ? { ResponseContentDisposition: `attachment; filename="${filename}"` }
          : {}),
      }),
      {
        expiresIn: 3 * 3600 * 24,
        signableHeaders: new Set<string>(),
        signingDate: getFrozenDate(),
      },
    );
    return {
      url,
    };
  }

  public async getWriteUrl({
    target,
    expiresInMinutes = 15,
  }: GetWriteRequest): Promise<{ url: string; path: string }> {
    return {
      url: await getSignedUrl(
        this.client,
        new PutObjectCommand({
          Bucket: target.container,
          Key: target.path,
        }),
        {
          expiresIn: expiresInMinutes * 60,
        },
      ),
      path: target.path,
    };
  }

  public async initiateMultiPart({
    file,
    target,
    expiresInMinutes = 15,
  }: MultiPartStartRequest): Promise<string> {
    if (file.fileSize > MAX_SIZE)
      throw new BadRequestException(
        `Can't upload file bigger than ${bytesToGigabytes(MAX_SIZE)}GB!`,
      );

    if (file.fileSize > SIZE_LIMIT[file.mime])
      throw new BadRequestException(
        `Limit for ${file.mime} is ${SIZE_LIMIT[file.mime]} bytes!`,
      );

    const response = await this.client.send(
      new CreateMultipartUploadCommand({
        Bucket: target.container,
        Key: target.path,
        Expires: new Date(Date.now() + expiresInMinutes * 60),
      }),
    );

    return response.UploadId;
  }

  public async getMultiPartChunkURL({
    target,
    multiPart,
  }: MultiPartChunkRequest): Promise<{ url: string }> {
    return {
      url: await getSignedUrl(
        this.client,
        new UploadPartCommand({
          Bucket: target.container,
          Key: target.path,
          UploadId: multiPart.id,
          PartNumber: multiPart.partNumber,
        }),
      ),
    };
  }
  public async completeMultiPart({
    target,
    multiPart,
  }: MultiPartEndRequest): Promise<CompleteMultipartUploadCommandOutput> {
    const command = new CompleteMultipartUploadCommand({
      Bucket: target.container,
      Key: target.path,
      UploadId: multiPart.id,
      MultipartUpload: {
        Parts: multiPart.parts,
      },
    });

    const response = await this.client.send(command);

    return response;
  }

  public async getObjectAsString(
    container: string,
    path: string,
  ): Promise<string> {
    const response = await this.client.send(
      new GetObjectCommand({
        Bucket: container,
        Key: path,
      }),
    );

    return await response.Body.transformToString();
  }

  public async getObjectAsWebStream(
    container: string,
    path: string,
  ): Promise<ReadableStream> {
    const response = await this.client.send(
      new GetObjectCommand({
        Bucket: container,
        Key: path,
      }),
    );

    return response.Body.transformToWebStream();
  }
  public async getObjectAsByteArray(
    container: string,
    path: string,
  ): Promise<Uint8Array> {
    const response = await this.client.send(
      new GetObjectCommand({
        Bucket: container,
        Key: path,
      }),
    );

    return await response.Body.transformToByteArray();
  }

  public async writeObject(
    container: string,
    path: string,
    data: string | Buffer,
  ): Promise<void> {
    await this.client.send(
      new PutObjectCommand({
        Bucket: container,
        Key: path,
        Body: data,
      }),
    );
  }

  public async getChecksum(container: string, path: string) {
    const response = await this.client.send(
      new HeadObjectCommand({
        Bucket: container,
        Key: path,
      }),
    );

    const eTag = response.ETag ? response.ETag.replace(/"/g, '') : null;
    return eTag;
  }

  getExpirationInSeconds() {
    const currentTime = new Date();
    const todayNoon = new Date(currentTime);
    todayNoon.setHours(12, 0, 0, 0);

    const tomorrow = new Date(currentTime);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowNoon = new Date(tomorrow);
    tomorrowNoon.setHours(12, 0, 0, 0);

    return currentTime < todayNoon
      ? Math.abs(todayNoon.getTime() - currentTime.getTime())
      : Math.abs(tomorrowNoon.getTime() - currentTime.getTime());
  }
}

function getFrozenDate() {
  return moment().startOf('day').toDate();
}
