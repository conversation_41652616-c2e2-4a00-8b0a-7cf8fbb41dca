import { createClient } from '@clickhouse/client';
import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Global()
@Module({
  providers: [
    {
      inject: [ConfigService],
      provide: NodeClickHouseClient,
      useFactory: (configService: ConfigService) =>
        createClient({
          username: configService.get('CLICKHOUSE_USER'),
          password: configService.get('CLICKHOUSE_PASSWORD'),
          database: configService.get('CLICKHOUSE_DB'),
          host: configService.get('CLICKHOUSE_HOST'),
          request_timeout: 60000,
        }),
    },
  ],
  exports: [NodeClickHouseClient],
})
export class ClickhouseModule {}
