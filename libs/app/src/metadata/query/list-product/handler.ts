import { Product } from '@goteacher/app/models/mongo/product.model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ListProductsQuery } from './query';

@QueryHandler(ListProductsQuery)
export class ListProductsHandler implements IQueryHandler<ListProductsQuery> {
  constructor(
    @InjectModel(Product.name)
    private readonly productModel: Model<Product>,
  ) {}

  async execute(query: ListProductsQuery) {
    const filter: any = { active: true };
    // Add search condition if search term is provided
    if (query.search) {
      filter.$or = [
        { name: { $regex: query.search, $options: 'i' } },
        { vendor: { $regex: query.search, $options: 'i' } },
      ];
    }

    const [data, total] = await Promise.all([
      this.productModel
        .find(filter)
        .skip(query.offset)
        .limit(query.limit)
        .sort({ name: 1 })
        .select({ name: 1, vendor: 1, type: 1, domainRules: 1 })
        .lean()
        .exec(),
      this.productModel.countDocuments(filter),
    ]);

    const products = data.map((product) => {
      const fullDomain =
        product.domainRules.sort((a, b) => {
          return b.priority - a.priority;
        })[0]?.pattern || null;
      return {
        ...product,
        fullDomain: fullDomain || null,
      };
    });

    return {
      data: products,
      total,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
