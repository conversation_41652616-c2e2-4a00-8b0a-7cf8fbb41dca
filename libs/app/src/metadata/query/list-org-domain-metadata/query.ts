import { PaymentStatus } from '@goteacher/app/models/mongo';
import {
  ApprovalStatus,
  DataPrivacy,
  DistrictRecommended,
} from '@goteacher/app/models/mongo/org.metadata.model';

export class ListOrgMetadataQuery {
  constructor(obj: ListOrgMetadataQuery) {
    Object.assign(this, obj);
  }

  domains?: string[];
  orgId: string;
  dataPrivacy?: DataPrivacy;
  districtRecommended?: DistrictRecommended;
  approvalStatus?: ApprovalStatus;
  paymentStatus?: PaymentStatus;
}
