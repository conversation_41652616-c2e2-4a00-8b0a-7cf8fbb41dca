import { ListOrgMetadataQuery } from '@goteacher/app/metadata/query/list-org-domain-metadata/query';
import {
  OrganizationDomainMetadata,
  PaymentStatus,
} from '@goteacher/app/models/mongo';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(ListOrgMetadataQuery)
export class ListOrgMetadataHandler
  implements IQueryHandler<ListOrgMetadataQuery>
{
  constructor(
    @InjectModel(OrganizationDomainMetadata.name)
    private readonly metadataModel: Model<OrganizationDomainMetadata>,
  ) {}

  async execute(query: ListOrgMetadataQuery): Promise<any> {
    let filter: any = {
      organizationId: query.orgId,
    };

    if (query.domains) {
      filter = {
        ...filter,
        domain: { $in: query.domains },
      };
    }

    if (query.dataPrivacy) {
      filter = {
        ...filter,
        dataPrivacy: query.dataPrivacy,
      };
    }

    if (query.districtRecommended) {
      filter = {
        ...filter,
        districtRecommended: query.districtRecommended,
      };
    }

    if (query.approvalStatus) {
      filter = {
        ...filter,
        approvalStatus: query.approvalStatus,
      };
    }

    if (query.paymentStatus === PaymentStatus.PAID) {
      filter = {
        ...filter,
        purchasedUntil: { $gte: new Date() },
      };
    }

    if (query.paymentStatus === PaymentStatus.FREE) {
      filter = {
        ...filter,
        $or: [
          { purchasedUntil: { $exists: false } },
          { purchasedUntil: null },
          { purchasedUntil: { $lt: new Date() } },
        ],
      };
    }

    return await this.metadataModel.find(filter);
  }
}
