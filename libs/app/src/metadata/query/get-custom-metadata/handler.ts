import { GetCustomMetadataQuery } from '@goteacher/app/metadata/query/get-custom-metadata/query';
import { DomainMetadata } from '@goteacher/app/models/mongo';
import { CustomMetadata } from '@goteacher/app/models/mongo/custom.metadata.model';
import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(GetCustomMetadataQuery)
export class GetCustomMetadtaQueryHandler
  implements IQueryHandler<GetCustomMetadataQuery> {
  constructor(
    @InjectModel(DomainMetadata.name) private readonly domainMetadataModel: Model<DomainMetadata>,
    @InjectModel(CustomMetadata.name) private readonly customMetadataModel: Model<CustomMetadata>,
  ) { }

  async execute(query: GetCustomMetadataQuery) {
    const customMetadata = await this.customMetadataModel
      .findOne({ domain: query.domain, organizationId: query.orgId })
      .lean();
    if (customMetadata) {
      return customMetadata;
    }

    const domainMetadata = await this.domainMetadataModel
      .findOne({ domain: query.domain })
      .lean();
    if (domainMetadata) {
      return domainMetadata;
    }

    throw new NotFoundException(`Metadata with key ${query.domain} not found`);
  }
}
