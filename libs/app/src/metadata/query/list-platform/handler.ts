import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { ListPlatformsQuery } from '@goteacher/app/metadata/query/list-platform/query';
import { Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

@QueryHandler(ListPlatformsQuery)
export class ListPlatformsHandler implements IQueryHandler<ListPlatformsQuery> {
  private readonly logger = new Logger(ListPlatformsHandler.name);

  constructor(private readonly clickhouse: NodeClickHouseClient) { }

  async execute(query: ListPlatformsQuery) {
    const sql = `     
      SELECT 
        domain
      FROM 
        all_domain_metrics_sc
      WHERE 
        orgId = '${query.orgId}'
        AND domain != 'newtab.'
        ${query.search ? `AND domain LIKE '${query.search.toLowerCase()}%'` : ''} 
      GROUP BY domain
      ORDER BY uniqMerge(active_users) DESC
      LIMIT ${query.limit}
      OFFSET ${query.offset}
      ;
    `;

    this.logger.debug(sql);

    const queryResult = await this.clickhouse.query({
      query: sql,
      format: 'JSON',
    });

    const { data, rows_before_limit_at_least } = await queryResult.json<{
      domain: string;
    }>();

    return {
      data,
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
