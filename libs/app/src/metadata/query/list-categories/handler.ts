import { ListCategoriesQuery } from '@goteacher/app/metadata/query/list-categories/query';
import { MetadataCategory } from '@goteacher/app/models/mongo/metadata.category.model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(ListCategoriesQuery)
export class ListCategoriesHandler
  implements IQueryHandler<ListCategoriesQuery> {
  constructor(
    @InjectModel(MetadataCategory.name)
    private readonly metadataCategoryModel: Model<MetadataCategory>,
  ) { }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async execute(query: ListCategoriesQuery): Promise<any> {
    return await this.metadataCategoryModel.find().lean();
  }
}
