import { GetOrgMetadataQuery } from '@goteacher/app/metadata/query/get-org-domain-metadata/query';
import {
  DomainMetadata,
  OrganizationDomainMetadata,
} from '@goteacher/app/models/mongo';
import { CustomMetadata } from '@goteacher/app/models/mongo/custom.metadata.model';
import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(GetOrgMetadataQuery)
export class GetOrgMetadataQueryHandler
  implements IQueryHandler<GetOrgMetadataQuery> {
  constructor(
    @InjectModel(OrganizationDomainMetadata.name) private readonly orgMetadataModel: Model<OrganizationDomainMetadata>,
    @InjectModel(DomainMetadata.name) private readonly domainMetadataModel: Model<DomainMetadata>,
    @InjectModel(CustomMetadata.name) private readonly customMetadataModel: Model<CustomMetadata>,
  ) { }

  async execute(query: GetOrgMetadataQuery) {
    const orgMetadata = await this.orgMetadataModel
      .findOne({
        domain: query.domain,
        organizationId: query.orgId,
      })
      .lean();

    const domainMetadata = await this.domainMetadataModel
      .findOne({
        domain: query.domain,
      })
      .lean();

    const customMetadata = await this.customMetadataModel
      .findOne({
        domain: query.domain,
        organizationId: query.orgId,
      })
      .lean();

    if (!domainMetadata && !orgMetadata && !customMetadata) {
      throw new NotFoundException('Domain metadata not found');
    }

    return {
      ...orgMetadata,
      ...domainMetadata,
      ...customMetadata,
    };
  }
}
