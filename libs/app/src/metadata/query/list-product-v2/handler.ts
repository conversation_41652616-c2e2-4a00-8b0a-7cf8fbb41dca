import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';

import { ListProductsQueryV2 } from '@goteacher/app/metadata/query/list-product-v2/query';
import { Logger } from '@nestjs/common';
import { NodeClickHouseClient } from '@clickhouse/client/dist/client';

@QueryHandler(ListProductsQueryV2)
export class ListProductsHandlerV2
  implements IQueryHandler<ListProductsQueryV2>
{
  private readonly logger = new Logger(ListProductsHandlerV2.name);

  constructor(private readonly clickhouse: NodeClickHouseClient) {}

  async execute(query: ListProductsQueryV2) {
    const sql = `     
      SELECT 
      DISTINCT p.productId,
      ds.fullDomain
      FROM 
      all_product_metrics_sc p
      LEFT JOIN 
      daily_sessions_cte ds
      ON 
      p.productId = ds.productId
      WHERE 
      1=1
      ${query.search ? `AND ds.fullDomain LIKE '${query.search.toLowerCase()}%'` : ''} 
      ORDER BY uniqMerge(p.active_users) DESC
      LIMIT ${query.limit}
      OFFSET ${query.offset}
      ;
    `;

    this.logger.debug(sql);

    const queryResult = await this.clickhouse.query({
      query: sql,
      format: 'JSON',
    });

    const { data, rows_before_limit_at_least } = await queryResult.json<{
      domain: string;
    }>();

    return {
      data,
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
