import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { CreateCustomMetadataCommand } from '@goteacher/app/metadata/command/create-custom-metadata/command';
import { CustomMetadata } from '@goteacher/app/models/mongo/custom.metadata.model';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@CommandHandler(CreateCustomMetadataCommand)
export class CreateCustomMeradataHandler
  implements ICommandHandler<CreateCustomMetadataCommand> {
  constructor(
    @InjectModel(CustomMetadata.name)
    private readonly customMetadataModel: Model<CustomMetadata>,
  ) { }

  async execute(command: CreateCustomMetadataCommand) {
    const orgId = command.orgId;

    const existingMetadata = await this.customMetadataModel.findOne({
      organizationId: orgId,
      domain: command.metadata.domain,
    });

    if (existingMetadata) {
      Object.assign(existingMetadata, command.metadata);
      await existingMetadata.save();
      return existingMetadata;
    } else {
      const customMetadata = await this.customMetadataModel.create({
        ...command.metadata,
        organizationId: orgId,
      });
      return customMetadata;
    }
  }
}
