import { DeleteCustomMetadataCommand } from '@goteacher/app/metadata/command/delete-custom-metadata/command';
import { CustomMetadata } from '@goteacher/app/models/mongo/custom.metadata.model';
import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@CommandHandler(DeleteCustomMetadataCommand)
export class DeleteCustomMetadataHandler
  implements ICommandHandler<DeleteCustomMetadataCommand> {
  constructor(
    @InjectModel(CustomMetadata.name)
    private readonly customMetadataModel: Model<CustomMetadata>,
  ) { }

  async execute(command: DeleteCustomMetadataCommand): Promise<any> {
    const orgId = command.orgId;

    const toDeleteMetadata = await this.customMetadataModel.findOne({
      _id: command.metadataId,
      organizationId: orgId,
    });

    if (!toDeleteMetadata) {
      throw new NotFoundException('Metadata not found!');
    }

    await this.customMetadataModel.deleteOne({ _id: command.metadataId });
    return { success: true };
  }
}
