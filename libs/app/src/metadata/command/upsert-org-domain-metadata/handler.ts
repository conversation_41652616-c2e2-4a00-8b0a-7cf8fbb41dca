import { UpsertOrgMetadataCommand } from '@goteacher/app/metadata/command/upsert-org-domain-metadata/command';
import { OrganizationDomainMetadata } from '@goteacher/app/models/mongo/org.metadata.model';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@CommandHandler(UpsertOrgMetadataCommand)
export class UpsertOrgMetadataHandler
  implements ICommandHandler<UpsertOrgMetadataCommand>
{
  constructor(
    @InjectModel(OrganizationDomainMetadata.name)
    private orgMetadataModel: Model<OrganizationDomainMetadata>,
  ) {}

  async execute(command: UpsertOrgMetadataCommand) {
    let orgMetadata = await this.orgMetadataModel.findOne({
      domain: command.domain,
      organizationId: command.orgId,
    });

    if (orgMetadata) {
      if (command.dataPrivacy || command.dataPrivacy === null) {
        orgMetadata.set('dataPrivacy', command.dataPrivacy ?? null);
      }

      if (command.districtRecommended || command.districtRecommended === null) {
        orgMetadata.set(
          'districtRecommended',
          command.districtRecommended ?? null,
        );
      }

      if (command.approvalStatus || command.approvalStatus === null) {
        orgMetadata.set('approvalStatus', command.approvalStatus ?? null);
      }

      await orgMetadata.save();
    } else {
      orgMetadata = await this.orgMetadataModel.create({
        domain: command.domain,
        organizationId: command.orgId,
        districtRecommended: command.districtRecommended,
        dataPrivacy: command.dataPrivacy,
        approvalStatus: command.approvalStatus,
      });
    }

    return orgMetadata;
  }
}
