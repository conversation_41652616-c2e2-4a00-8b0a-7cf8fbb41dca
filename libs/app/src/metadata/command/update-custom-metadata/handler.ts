import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';

import { UpdateCustomMetadataCommand } from '@goteacher/app/metadata/command/update-custom-metadata/command';
import { CustomMetadata } from '@goteacher/app/models/mongo/custom.metadata.model';
import { ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@CommandHandler(UpdateCustomMetadataCommand)
export class UpdateCustomMetadataCommandHandler
  implements ICommandHandler<UpdateCustomMetadataCommand> {
  constructor(
    @InjectModel(CustomMetadata.name)
    private readonly customMetadataModel: Model<CustomMetadata>,
  ) { }

  async execute(command: UpdateCustomMetadataCommand) {
    const orgId = command.orgId;

    const customMetadata = await this.customMetadataModel.findById(
      command.metadataId,
    );

    if (customMetadata.organizationId !== orgId) {
      throw new ForbiddenException("You don't have access to this metadata");
    }

    const updatedCustomMetadata =
      await this.customMetadataModel.findByIdAndUpdate(
        { _id: command.metadataId },
        command.metadata,
      );

    return updatedCustomMetadata;
  }
}
