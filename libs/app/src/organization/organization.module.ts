import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { SequelizeModelModule } from '@goteacher/app/models/sequelize';
import { ImportOrganizationCsvHandler } from './command/import-organization-csv';

const CommandHandlers = [ImportOrganizationCsvHandler];

@Module({
  imports: [
    CqrsModule, 
    SequelizeModelModule,
  ],
  providers: [...CommandHandlers],
  exports: [],
})
export class OrganizationModule {}