import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { Address } from '@goteacher/app/models/sequelize/address.model';
import { Country } from '@goteacher/app/models/sequelize/country.model';
import { CsvImportLog, ImportType } from '@goteacher/app/models/sequelize/csv-import-log.model';
import { Grade } from '@goteacher/app/models/sequelize/grade.model';
import { Organisation, OrganisationStatus } from '@goteacher/app/models/sequelize/organisation.model';
import { School, SchoolStatus } from '@goteacher/app/models/sequelize/school.model';
import { State } from '@goteacher/app/models/sequelize/state.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool, UserSchoolStatus } from '@goteacher/app/models/sequelize/user.school.model';
import { calculateCurrentSchoolYear } from '@goteacher/app/utility/school-year';
import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { parse } from 'csv-parse/sync';
import { Sequelize } from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';
import { ImportOrganizationCsvCommand } from './command';

interface UserChangeTracker {
  email: string;
  previousGrade?: string;
  previousSchoolId?: string;
  currentGrade?: string;
  currentSchoolId?: string;
  hasGradeChanged: boolean;
  hasSchoolChanged: boolean;
  shouldDecommission: boolean;
}

@Injectable()
@CommandHandler(ImportOrganizationCsvCommand)
export class ImportOrganizationCsvHandler
  implements ICommandHandler<ImportOrganizationCsvCommand> {
  constructor(
    @InjectModel(Organisation)
    private readonly organizationModel: typeof Organisation,
    @InjectModel(School)
    private readonly schoolModel: typeof School,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(UserSchool)
    private readonly userSchoolModel: typeof UserSchool,
    @InjectModel(Grade)
    private readonly gradeModel: typeof Grade,
    @InjectModel(Address)
    private readonly addressModel: typeof Address,
    @InjectModel(Country)
    private readonly countryModel: typeof Country,
    @InjectModel(State)
    private readonly stateModel: typeof State,
    @InjectModel(CsvImportLog)
    private readonly csvImportLogModel: typeof CsvImportLog,
    private readonly sequelize: Sequelize,
    private readonly clickhouseClient: NodeClickHouseClient,
  ) { }

  async execute(command: ImportOrganizationCsvCommand) {
    const { orgName, orgDomain, orgState, type, csvBuffer, fileName } = command.payload;

    // Calculate current school year automatically
    const currentSchoolYear = calculateCurrentSchoolYear();
    console.log(`Calculated current school year: ${currentSchoolYear}`);

    const transaction = await this.sequelize.transaction();

    try {
      const records = parse(csvBuffer, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      });

      // track emails and user-school pairs from CSV for decommissioning logic
      const csvEmails = new Set<string>();
      const csvUserSchoolPairs = new Set<string>();

      // Phase 1: Ensure organization exists
      let organization = await this.organizationModel.findOne({
        where: { name: orgName },
        transaction,
      });

      if (!organization) {
        organization = await this.organizationModel.create({
          id: uuidv4(),
          name: orgName,
          displayName: orgName,
          domains: orgDomain,
          status: OrganisationStatus.APPROVED,
          deleted: false,
        }, { transaction });
      }

      // Phase 2: Check if this is the first import for this org/school_year/type
      const existingImports = await this.csvImportLogModel.findAll({
        where: {
          organizationId: organization.id,
          schoolYear: currentSchoolYear,
          importType: type === 'student' ? ImportType.STUDENT : ImportType.STAFF,
        },
        transaction,
      });

      const isFirstImport = existingImports.length === 0;
      console.log(`Import type: ${isFirstImport ? 'FIRST' : 'SUBSEQUENT'} import for ${type}s in ${currentSchoolYear}`);

      // Phase 3: Get country and state for addresses
      const country = await this.countryModel.findOne({
        where: { countryName: 'United States of America' },
        transaction,
      });

      if (!country) {
        throw new Error('Country "United States of America" not found in database. Please ensure seed data is loaded.');
      }

      const state = await this.stateModel.findOne({
        where: {
          stateName: orgState,
          countryId: country.id,
        },
        transaction,
      });

      if (!state) {
        throw new Error(`State "${orgState}" not found in database. Please ensure seed data is loaded.`);
      }

      // extract unique schools from CSV and collect emails
      const schoolSet = new Set<string>();
      for (const record of records) {
        if (record.Email) {
          const email = record.Email.toLowerCase().trim();
          csvEmails.add(email);
          
          // Track user-school pairs
          if (record.School) {
            csvUserSchoolPairs.add(`${email}:${record.School}`);
          }
        }
        
        const schoolField = record.School;
        if (schoolField) {
          const schools = schoolField.split(',').map((s: string) => s.trim());
          schools.forEach((school: string) => schoolSet.add(school));
        }
      }

      console.log(`Found ${schoolSet.size} unique schools in CSV`);
      if (schoolSet.size === 0) {
        throw new Error('No schools found in CSV. Please ensure the CSV has a "School" column.');
      }

      // get all existing active users for this organization before processing
      const existingUsers = await this.userModel.findAll({
        include: [{
          model: UserSchool,
          required: true,
          include: [{
            model: School,
            required: true,
            where: { organisationId: organization.id }
          }]
        }],
        where: {
          decommissionedAt: null,
          role: type === 'student' ? 'student' : 'teacher'
        },
        transaction
      });

      console.log(`Found ${existingUsers.length} existing active ${type}s in organization`);

      // Phase 4: Create change tracking map for non-first imports
      const userChangeTracker = new Map<string, UserChangeTracker>();
      
      if (!isFirstImport) {
        console.log('Building user change tracker for existing users...');
        
        for (const user of existingUsers) {
          const email = user.email.toLowerCase();
          const userSchool = user.UserSchool[0]; // Assuming one active school per user
          
          userChangeTracker.set(email, {
            email,
            previousGrade: userSchool?.grade,
            previousSchoolId: userSchool?.schoolId,
            currentGrade: undefined,
            currentSchoolId: undefined,
            hasGradeChanged: false,
            hasSchoolChanged: false,
            shouldDecommission: false,
          });
        }
      }

      const schoolMap = new Map<string, any>();

      for (const schoolName of schoolSet) {
        let school = await this.schoolModel.findOne({
          where: {
            organisationId: organization.id,
            name: schoolName,
          },
          transaction,
        });

        if (!school) {
          console.log(`School ${schoolName} not found. Creating...`);

          // create default address for school
          const address = await this.addressModel.create({
            id: uuidv4(),
            addressLine1: 'Not known',
            addressLine2: 'Not known',
            postalCode: 'Not known',
            city: 'Not known',
            countryId: country.id,
            stateId: state.id,
          }, { transaction });

          school = await this.schoolModel.create({
            id: uuidv4(),
            name: schoolName,
            displayName: schoolName,
            status: SchoolStatus.APPROVED,
            organisationId: organization.id,
            addressId: address.id,
            deleted: false,
          }, { transaction });
        } else {
          console.log(`School ${schoolName} found. Skipping creation...`);
        }

        schoolMap.set(schoolName, school);
      }

      // Phase 5: Load all grades for mapping (only needed for students)
      let gradeMap = new Map();
      if (type === 'student') {
        const gradesDb = await this.gradeModel.findAll({ transaction });

        gradesDb.forEach(grade => {
          if (grade.value === '1') gradeMap.set(1, grade);
          if (grade.value === '2') gradeMap.set(2, grade);
          if (grade.value === '3') gradeMap.set(3, grade);
          if (grade.value === '4') gradeMap.set(4, grade);
          if (grade.value === '5') gradeMap.set(5, grade);
          if (grade.value === '6') gradeMap.set(6, grade);
          if (grade.value === '7') gradeMap.set(7, grade);
          if (grade.value === '8') gradeMap.set(8, grade);
          if (grade.value === '9') gradeMap.set(9, grade);
          if (grade.value === '10') gradeMap.set(10, grade);
          if (grade.value === '11') gradeMap.set(11, grade);
          if (grade.value === '12') gradeMap.set(12, grade);
          if (grade.value === 'COLLEGE') gradeMap.set(13, grade);
          if (grade.value === 'KG') {
            gradeMap.set(0, grade);
            gradeMap.set('Kindergarten', grade);
            gradeMap.set('PK2', grade);
            gradeMap.set('PK3', grade);
            gradeMap.set('PK4', grade);
          }
          if (grade.value === 'PRE-K') {
            gradeMap.set('PreKindergarten', grade);
          }
          // map X grade
          if (grade.value.match(/^\d+$/)) {
            gradeMap.set(`Grade ${grade.value}`, grade);
          }
        });
      }

      let importedCount = 0;
      let failedCount = 0;
      let decommissionedForChangesCount = 0;
      const errors: string[] = [];
      const decommissionedForChanges: string[] = [];

      // Phase 6: Process each user record
      for (let i = 0; i < records.length; i++) {
        const record = records[i];
        const rowNumber = i + 2;

        try {
          if (!record.Email) {
            console.warn(`Skipping row ${rowNumber}: Missing email`);
            failedCount++;
            continue;
          }

          // handle different column names for student vs staff
          const firstName = type === 'student' ? record.First : record.FirstName;
          const lastName = type === 'student' ? record.Last : record.LastName;

          if (!firstName || !lastName) {
            const fieldNames = type === 'student' ? '(First, Last)' : '(FirstName, LastName)';
            errors.push(`Row ${rowNumber}: Missing required fields ${fieldNames}`);
            failedCount++;
            continue;
          }

          if (!record.School) {
            errors.push(`Row ${rowNumber}: Missing school`);
            failedCount++;
            continue;
          }

          // find the school for this user
          const school = schoolMap.get(record.School);
          if (!school) {
            console.error(`Error while processing row ${rowNumber}, school not found: ${record.School}`);
            errors.push(`Row ${rowNumber}: School '${record.School}' not found`);
            failedCount++;
            continue;
          }

          const email = record.Email.toLowerCase().trim();
          const firstNameEscaped = this.escapeAndCapitalize(firstName);
          const lastNameEscaped = this.escapeAndCapitalize(lastName);

          // parse grade (only for students)
          let gradeObj = null;
          let gradeMetadata = {};

          if (type === 'student') {
            if (record.Grade !== undefined && record.Grade !== null && record.Grade !== '') {
              const gradeNum = parseInt(record.Grade);
              if (!isNaN(gradeNum)) {
                gradeObj = gradeMap.get(gradeNum);
              }

              if (!gradeObj) {
                gradeObj = gradeMap.get(record.Grade);
              }

              if (!gradeObj) {
                const gradeMatch = record.Grade.match(/Grade\s+(\d+)/i);
                if (gradeMatch) {
                  gradeObj = gradeMap.get(parseInt(gradeMatch[1]));
                }
              }

              if (gradeObj) {
                gradeMetadata = { grade: gradeObj.id };
              }
            }
          }

          const gradeValue = type === 'student' && gradeObj ? gradeObj.value : 'N/A';

          // Update change tracker for subsequent imports
          if (!isFirstImport && userChangeTracker.has(email)) {
            const tracker = userChangeTracker.get(email)!;
            tracker.currentGrade = gradeValue;
            tracker.currentSchoolId = school.id;
            
            tracker.hasGradeChanged = tracker.previousGrade !== gradeValue;
            tracker.hasSchoolChanged = tracker.previousSchoolId !== school.id;
            tracker.shouldDecommission = tracker.hasGradeChanged || tracker.hasSchoolChanged;
            
            if (tracker.shouldDecommission) {
              console.log(`User ${email} has changes - Grade: ${tracker.previousGrade} -> ${gradeValue}, School: ${tracker.hasSchoolChanged ? 'CHANGED' : 'SAME'}`);
            }
          }

          // check if user exists (including decommissioned)
          let user = await this.userModel.findOne({
            where: { email, decommissionedAt: null },
            transaction,
          });

          console.log(`Email: ${email} Metadata: ${JSON.stringify(gradeMetadata)}`);

          let isNewUser = false;
          const userRole = type === 'student' ? 'student' : 'teacher';
          let updates: any = {};
          let userSchoolUpdates: any = {};

          // Handle user creation/decommissioning logic
          if (!user) {
            console.log(`User ${email} not found. Creating...`);
            isNewUser = true;
            user = await this.createNewUser(email, firstNameEscaped, lastNameEscaped, userRole, gradeMetadata, transaction);
          } else if (user.decommissionedAt) {
            // User was decommissioned but is now back in CSV
            console.log(`User ${email} was decommissioned. Creating new user record...`);
            isNewUser = true;
            user = await this.createNewUser(email, firstNameEscaped, lastNameEscaped, userRole, gradeMetadata, transaction);
          } else if (!isFirstImport && userChangeTracker.has(email) && userChangeTracker.get(email)!.shouldDecommission) {
            // For subsequent imports: decommission user if grade or school changed
            const tracker = userChangeTracker.get(email)!;
            console.log(`Decommissioning user ${email} due to changes (Grade: ${tracker.hasGradeChanged}, School: ${tracker.hasSchoolChanged})`);
            
            // Decommission existing user
            await this.userModel.update(
              { decommissionedAt: new Date() },
              { where: {id: user.id}, transaction}
            );
            
            // Update all UserSchool associations to DECOMMISSIONED
            await this.userSchoolModel.update(
              { status: UserSchoolStatus.DECOMMISSIONED },
              { where: { userId: user.id }, transaction }
            );

            // Update ClickHouse for decommissioned user
            await this.updateClickHouseForDecommission(user.id, organization.id, transaction);
            
            // Create new user record
            isNewUser = true;
            user = await this.createNewUser(email, firstNameEscaped, lastNameEscaped, userRole, gradeMetadata, transaction);
            
            decommissionedForChangesCount++;
            decommissionedForChanges.push(`${email} (${tracker.hasGradeChanged ? 'grade' : ''}${tracker.hasGradeChanged && tracker.hasSchoolChanged ? '+' : ''}${tracker.hasSchoolChanged ? 'school' : ''})`);
          } else {
            console.log(`User ${email} found. Checking for updates...`);
            
            // check if any fields need updating
            const metadataString = JSON.stringify(gradeMetadata);
            updates = {};
            
            if (user.firstName !== firstNameEscaped) updates.firstName = firstNameEscaped;
            if (user.lastName !== lastNameEscaped) updates.lastName = lastNameEscaped;
            if (user.role !== userRole) updates.role = userRole;
            if (user.Metadata !== metadataString && JSON.stringify(user.Metadata) !== metadataString) updates.Metadata = metadataString;
            if (!user.Synced) updates.Synced = true;
            if (!user.isRegistrationCompleted) updates.isRegistrationCompleted = true;
            if (user.deleted) updates.deleted = false;
            
            // only update if there are changes, so not a full update, only what changed
            if (Object.keys(updates).length > 0) {
              console.log(`Updating user ${email} with changes:`, Object.keys(updates).join(', '));
              await user.update(updates, { transaction });
            } else {
              console.log(`No changes needed for user ${email}`);
            }
          }

          // check if user-school association exists
          let userSchool = await this.userSchoolModel.findOne({
            where: {
              userId: user.id,
              schoolId: school.id,
            },
            transaction,
          });

          if (!userSchool) {
            // Create user-school association
            await this.userSchoolModel.create({
              id: uuidv4(),
              status: UserSchoolStatus.APPROVED,
              userId: user.id,
              schoolId: school.id,
              grade: gradeValue,
              schoolYear: currentSchoolYear,
            }, { transaction });
          } else {
            // Check if user-school association needs updating
            userSchoolUpdates = {};
            
            if (userSchool.grade !== gradeValue) userSchoolUpdates.grade = gradeValue;
            if (userSchool.schoolYear !== currentSchoolYear) userSchoolUpdates.schoolYear = currentSchoolYear;
            if (userSchool.status !== UserSchoolStatus.APPROVED) userSchoolUpdates.status = UserSchoolStatus.APPROVED;
            
            if (Object.keys(userSchoolUpdates).length > 0) {
              console.log(`Updating user-school association for ${email} with changes:`, Object.keys(userSchoolUpdates).join(', '));
              await userSchool.update(userSchoolUpdates, { transaction });
            } else {
              console.log(`No user-school changes needed for ${email}`);
            }
          }

          // Only update ClickHouse if it's a new user or if there were changes
          const userHadChanges = Object.keys(updates || {}).length > 0;
          const userSchoolHadChanges = Object.keys(userSchoolUpdates || {}).length > 0;
          
          try {
            if (isNewUser) {
              // for the new users, insert directly
              await this.clickhouseClient.command({
                query: `
                  INSERT INTO goteacher.users
                  (userId, schoolId, orgId, role, grade)
                  VALUES ('${user.id}', '${school.id}', '${organization.id}', '${userRole}', '${gradeValue}')
                `
              });
            } else if (userHadChanges || userSchoolHadChanges) {
              // for existing users with changes: Delete then insert (it is more real time doing so)
              console.log(`Updating ClickHouse for user ${email} due to changes`);
              await this.clickhouseClient.command({
                query: `ALTER TABLE goteacher.users DELETE WHERE userId = '${user.id}'`
              });

              await this.clickhouseClient.command({
                query: `
                  INSERT INTO goteacher.users
                  (userId, schoolId, orgId, role, grade)
                  VALUES ('${user.id}', '${school.id}', '${organization.id}', '${userRole}', '${gradeValue}')
                `
              });
            } else {
              console.log(`No ClickHouse update needed for user ${email}`);
            }
          } catch (chError) {
            console.error(`ClickHouse error for user ${email}:`, chError);
          }

          importedCount++;
          console.log(`User ${i + 1}/${records.length} processed.`);
        } catch (error) {
          errors.push(`Row ${rowNumber}: ${error.message}`);
          failedCount++;
          console.error(`Error processing user ${i + 1}/${records.length}:`, error.message);
        }
      }

      // Phase 7: Decommission users not in CSV (existing logic)
      let decommissionedCount = 0;
      const decommissionedUsers: string[] = [];

      for (const existingUser of existingUsers) {
        if (!csvEmails.has(existingUser.email.toLowerCase())) {
          console.log(`Decommissioning user ${existingUser.email} - not found in CSV`);
          
          // update user with decommissionedAt timestamp
          await existingUser.update({
            decommissionedAt: new Date(),
          }, { transaction });

          // update all UserSchool associations to DECOMMISSIONED
          await this.userSchoolModel.update(
            { status: UserSchoolStatus.DECOMMISSIONED },
            {
              where: { userId: existingUser.id },
              transaction,
            }
          );

          // update ClickHouse - delete and re-insert with decommissionedAt
          await this.updateClickHouseForDecommission(existingUser.id, organization.id, transaction);

          decommissionedCount++;
          decommissionedUsers.push(existingUser.email);
        }
      }

      // Phase 8: Log the import
      await this.csvImportLogModel.create({
        id: uuidv4(),
        organizationId: organization.id,
        schoolYear: currentSchoolYear,
        importType: type === 'student' ? ImportType.STUDENT : ImportType.STAFF,
        fileName,
        metadata: {
          isFirstImport,
          recordCount: records.length,
          importedCount,
          failedCount,
          decommissionedCount,
          decommissionedForChangesCount,
          schoolsProcessed: schoolSet.size,
        },
      }, { transaction });

      await transaction.commit();

      console.log(`\n=== Import Summary ===`);
      console.log(`School Year: ${currentSchoolYear}`);
      console.log(`Import Type: ${isFirstImport ? 'FIRST' : 'SUBSEQUENT'}`);
      console.log(`Total users in CSV: ${records.length}`);
      console.log(`Successfully imported: ${importedCount}`);
      console.log(`Failed to import: ${failedCount}`);
      console.log(`Decommissioned (not in CSV): ${decommissionedCount}`);
      console.log(`Decommissioned (changes): ${decommissionedForChangesCount}`);
      console.log(`Schools created/found: ${schoolSet.size}`);

      if (decommissionedUsers.length > 0) {
        console.log(`\nDecommissioned users (not in CSV): ${decommissionedUsers.join(', ')}`);
      }

      if (decommissionedForChanges.length > 0) {
        console.log(`\nDecommissioned users (changes): ${decommissionedForChanges.join(', ')}`);
      }

      return {
        success: failedCount === 0,
        message: `Successfully imported ${importedCount} ${type}s${failedCount > 0 ? `, ${failedCount} failed` : ''}${decommissionedCount > 0 ? `, ${decommissionedCount} decommissioned (not in CSV)` : ''}${decommissionedForChangesCount > 0 ? `, ${decommissionedForChangesCount} decommissioned (changes)` : ''}`,
        schoolYear: currentSchoolYear,
        isFirstImport,
        importedCount,
        failedCount,
        decommissionedCount,
        decommissionedForChangesCount,
        ...(errors.length > 0 && { errors }),
        ...(decommissionedUsers.length > 0 && { decommissionedUsers }),
        ...(decommissionedForChanges.length > 0 && { decommissionedForChanges }),
      };
    } catch (error) {
      await transaction.rollback();
      throw new Error(`Failed to import ${type}s: ${error.message}`);
    }
  }

  private async createNewUser(
    email: string,
    firstName: string,
    lastName: string,
    role: string,
    gradeMetadata: any,
    transaction: any
  ): Promise<User> {
    return await this.userModel.create({
      id: uuidv4(),
      email,
      firstName,
      lastName,
      role,
      Metadata: JSON.stringify(gradeMetadata),
      Synced: true,
      isRegistrationCompleted: true,
      isOnboardingCompleted: false,
      deleted: false,
    }, { transaction });
  }

  private async updateClickHouseForDecommission(
    userId: string,
    organizationId: string,
    transaction: any
  ): Promise<void> {
    try {
      await this.clickhouseClient.command({
        query: `ALTER TABLE goteacher.users DELETE WHERE userId = '${userId}'`
      });

      // Get user and school info for the decommissioned record
      const user = await this.userModel.findByPk(userId, {
        include: [{
          model: UserSchool,
          include: [School]
        }],
        transaction
      });

      if (user && user.UserSchool && user.UserSchool.length > 0) {
        const userSchool = user.UserSchool[0];
        const gradeValue = userSchool?.grade || 'N/A';
        
        await this.clickhouseClient.command({
          query: `
            INSERT INTO goteacher.users
            (userId, schoolId, orgId, role, grade, decommissionedAt)
            VALUES ('${userId}', '${userSchool.schoolId}', '${organizationId}', '${user.role}', '${gradeValue}', now())
          `
        });
      }
    } catch (chError) {
      console.error(`ClickHouse error while decommissioning user ${userId}:`, chError);
    }
  }

  private escapeAndCapitalize(str: string): string {
    if (!str) return '';
    const escaped = str.replace(/'/g, "''");
    return escaped.charAt(0).toUpperCase() + escaped.slice(1).toLowerCase();
  }
}