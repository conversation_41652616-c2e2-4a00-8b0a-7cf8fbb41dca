import { GetFilterableSubjectsQuery } from '@goteacher/app/analytics/query/filters/subjects/query';
import { Subject } from '@goteacher/app/models/sequelize';
import { parseOrderBy } from '@goteacher/app/utility';
import { I<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetFilterableSubjectsQuery)
export class GetFilterableSubjectsHandler
  implements IQueryHandler<GetFilterableSubjectsQuery>
{
  constructor(
    @InjectModel(Subject) private readonly subjectModel: typeof Subject,
  ) {}

  async execute(query: GetFilterableSubjectsQuery) {
    let whereClause: any = {};

    if (query.search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${query.search}%` } },
        { value: { [Op.iLike]: `%${query.search}%` } },
      ];
    }

    const orderBy = parseOrderBy(query.order);

    const subjectsCount = await this.subjectModel.count({ where: whereClause });
    const subjects = await this.subjectModel.findAll({
      where: whereClause,
      limit: query.limit,
      offset: query.offset,
      order: orderBy,
    });

    return {
      data: subjects,
      total: subjectsCount,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
