import { GetFilterableSchoolsQuery } from '@goteacher/app/analytics/query/filters/schools/query';
import {
  School,
  SchoolStatus,
} from '@goteacher/app/models/sequelize/school.model';
import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import { parseOrderBy } from '@goteacher/app/utility';
import { I<PERSON>ueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetFilterableSchoolsQuery)
export class GetFilterableSchoolsHandler
  implements IQueryHandler<GetFilterableSchoolsQuery>
{
  constructor(
    @InjectModel(School) private readonly schoolModel: typeof School,
  ) {}

  async execute(query: GetFilterableSchoolsQuery) {
    if (query.ctx.user.role === UserRole.ADMINISTRATOR) {
      return await this.adminExecute(query);
    } else {
      return await this.otherExecute(query);
    }
  }

  async adminExecute(query: GetFilterableSchoolsQuery) {
    const organisationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );
    const whereClause: any = {
      organisationId: {
        [Op.in]: organisationIds,
      },
      status: SchoolStatus.APPROVED,
    };

    if (query.search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${query.search}%` } },
        { displayName: { [Op.iLike]: `%${query.search}%` } },
      ];
    }

    const orderBy = parseOrderBy(query.order);

    const schoolsCount = await this.schoolModel.count({ where: whereClause });
    const schools = await this.schoolModel.findAll({
      where: whereClause,
      limit: query.limit,
      offset: query.offset,
      order: orderBy,
    });

    return {
      data: schools,
      total: schoolsCount,
      limit: query.limit,
      offset: query.offset,
    };
  }

  async otherExecute(query: GetFilterableSchoolsQuery) {
    const schoolIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.id,
    );
    const whereClause: any = {
      id: {
        [Op.in]: schoolIds,
      },
    };

    if (query.search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${query.search}%` } },
        { displayName: { [Op.iLike]: `%${query.search}%` } },
      ];
    }

    const orderBy = parseOrderBy(query.order);

    const schoolsCount = await this.schoolModel.count({ where: whereClause });
    const schools = await this.schoolModel.findAll({
      where: whereClause,
      limit: query.limit,
      offset: query.offset,
      order: orderBy,
    });

    return {
      data: schools,
      total: schoolsCount,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
