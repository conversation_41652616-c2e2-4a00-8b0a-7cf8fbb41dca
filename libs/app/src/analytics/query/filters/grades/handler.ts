import { GetFilterableGradesQuery } from '@goteacher/app/analytics/query/filters/grades/query';
import { Grade } from '@goteacher/app/models/sequelize';
import { parseOrderBy } from '@goteacher/app/utility';
import { IQ<PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetFilterableGradesQuery)
export class GetFilterableGradesHandler
  implements IQueryHandler<GetFilterableGradesQuery>
{
  constructor(@InjectModel(Grade) private readonly gradeModel: typeof Grade) {}

  async execute(query: GetFilterableGradesQuery) {
    let whereClause: any = {};

    if (query.licenseable) {
      whereClause.licenseable = true;
    } else {
      whereClause.filterable = true;
    }

    if (query.search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${query.search}%` } },
        { value: { [Op.iLike]: `%${query.search}%` } },
      ];
    }

    const orderBy = parseOrderBy(query.order);

    const gradesCount = await this.gradeModel.count({ where: whereClause });
    const grades = await this.gradeModel.findAll({
      where: whereClause,
      limit: query.limit,
      offset: query.offset,
      order: orderBy,
    });

    return {
      data: grades,
      total: gradesCount,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
