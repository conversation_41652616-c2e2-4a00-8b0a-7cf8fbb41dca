import { GetFilterableStudentsQuery } from '@goteacher/app/analytics/query/filters/students/query';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User, UserRole } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { parseOrderBy } from '@goteacher/app/utility';
import { IQuery<PERSON>and<PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetFilterableStudentsQuery)
export class GetFilterableStudentsHandler
  implements IQueryHandler<GetFilterableStudentsQuery>
{
  constructor(
    @InjectModel(User) private readonly userModel: typeof User,
    @InjectModel(School) private readonly schoolModel: typeof School,
  ) {}

  async execute(query: GetFilterableStudentsQuery) {
    if (query.ctx.user.role === UserRole.ADMINISTRATOR) {
      return await this.adminExecute(query);
    } else {
      return await this.otherExecute(query);
    }
  }

  async adminExecute(query: GetFilterableStudentsQuery) {
    const organisationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const organisationSchools = await this.schoolModel.findAll({
      where: {
        organisationId: {
          [Op.in]: organisationIds,
        },
      },
      attributes: ['id'],
    });

    const userWhereClause: any = {
      deleted: false,
      role: UserRole.STUDENT,
    };
    const userSchoolWhereClause: any = {
      schoolId: {
        [Op.in]: organisationSchools.map((school) => school.id),
      },
    };

    if (query.search) {
      userWhereClause[Op.or] = [
        { firstName: { [Op.iLike]: `%${query.search}%` } },
        { lastName: { [Op.iLike]: `%${query.search}%` } },
        { email: { [Op.iLike]: `%${query.search}%` } },
      ];
    }

    const orderBy = parseOrderBy(query.order);

    const studentsCount = await this.userModel.count({
      where: userWhereClause,
      include: [
        {
          model: UserSchool,
          where: userSchoolWhereClause,
          attributes: ['schoolId'],
        },
      ],
      distinct: true,
    });
    const students = await this.userModel.findAll({
      where: userWhereClause,
      attributes: ['id', 'firstName', 'lastName', 'email', 'picture'],
      include: [
        {
          model: UserSchool,
          where: userSchoolWhereClause,
          attributes: ['schoolId'],
        },
      ],
      limit: query.limit,
      offset: query.offset,
      order: orderBy,
    });

    return {
      data: students,
      total: studentsCount,
      limit: query.limit,
      offset: query.offset,
    };
  }

  async otherExecute(query: GetFilterableStudentsQuery) {
    const schoolIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.id,
    );

    const userWhereClause: any = {
      deleted: false,
      role: UserRole.STUDENT,
    };
    const userSchoolWhereClause: any = {
      schoolId: {
        [Op.in]: schoolIds,
      },
    };

    if (query.search) {
      userWhereClause[Op.or] = [
        { firstName: { [Op.iLike]: `%${query.search}%` } },
        { lastName: { [Op.iLike]: `%${query.search}%` } },
        { email: { [Op.iLike]: `%${query.search}%` } },
      ];
    }

    const orderBy = parseOrderBy(query.order);

    const studentsCount = await this.userModel.count({
      where: userWhereClause,
      include: [
        {
          model: UserSchool,
          where: userSchoolWhereClause,
          attributes: ['schoolId'],
        },
      ],
      distinct: true,
    });
    const students = await this.userModel.findAll({
      where: userWhereClause,
      attributes: ['id', 'firstName', 'lastName', 'email', 'picture'],
      include: [
        {
          model: UserSchool,
          where: userSchoolWhereClause,
          attributes: ['schoolId'],
        },
      ],
      limit: query.limit,
      offset: query.offset,
      order: orderBy,
    });

    return {
      data: students,
      total: studentsCount,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
