import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { ReqContext } from '@goteacher/app/auth';
import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';
import { OrderByRequest } from '@goteacher/app/utility';

export class GetGradesAnalyticsQuery extends BaseDateRangeQuery {
  constructor(obj: GetGradesAnalyticsQuery) {
    super();
    Object.assign(this, obj);
  }

  domain?: string;
  productId?: string;

  userGroup?: UserGroup = UserGroup.BOTH;
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;
  schoolIds?: string[];
  grades?: string[];

  order?: OrderByRequest[];
  forceRefresh?: boolean = false;
  ctx: ReqContext;

}
