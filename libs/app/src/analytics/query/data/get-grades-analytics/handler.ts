import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetGradesAnalyticsQuery } from '@goteacher/app/analytics/query/data/get-grades-analytics/query';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@QueryHandler(GetGradesAnalyticsQuery)
export class GetGradesAnalyticsQueryHandler
  implements IQueryHandler<GetGradesAnalyticsQuery> {
  private readonly logger = new Logger(GetGradesAnalyticsQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(School) private schoolModel: typeof School,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetGradesAnalyticsQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    const sqlQuery = `
      WITH 
      daily_grade_activity AS (
        SELECT
          ism.userId AS userId,
          ism.grade AS grade,
          ${query.productId ? 'ism.productId,' : ''}
          ${query.domain ? 'ism.domain,' : ''}        
          toStartOfDay(ism.startTime) AS activity_day,
          sum(ism.duration) AS daily_total_seconds,
          uniq(ism.sessionId) AS session_count,
          uniq(ism.userId) AS active_users_count,
          count(*) AS daily_page_views
        FROM goteacher.interaction_summaries ism
        WHERE
          ism.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND ism.eventName = 'INTERACTION_SUMMARY'          
          ${query.productId ? `AND ism.productId = '${query.productId}'` : ''}                  
          ${query.domain ? `AND ism.domain = '${query.domain}'` : ''}                  
          ${query.schoolIds?.length ? `AND ism.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}        
          ${query.fromDate ? `AND ism.startTime >= ${Math.floor(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND ism.startTime <= ${Math.floor(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND ism.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND ism.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) > 7 AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) < 15` : ''}
        GROUP BY
          ism.userId,
          ism.grade,
          ${query.productId ? `ism.productId,` : ''}
          ${query.domain ? 'ism.domain,' : ''}
          activity_day
      ),
      
      daily_idle_activity AS (
        SELECT
          ism.grade AS grade,
          ${query.productId ? 'ism.productId,' : ''}
          ${query.domain ? 'ism.domain,' : ''}
          toStartOfDay(ism.startTime) AS activity_day,
          sum(ism.duration) AS daily_idle_seconds,
          count(*) AS idle_ping_count
        FROM goteacher.interaction_summaries ism
        WHERE
          ism.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND ism.eventName = 'IDLE_PING'
          ${query.productId ? `AND ism.productId = '${query.productId}'` : ''}
          ${query.domain ? `AND ism.domain = '${query.domain}'` : ''}
          ${query.schoolIds?.length ? `AND ism.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND ism.startTime >= ${Math.floor(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND ism.startTime <= ${Math.floor(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND ism.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND ism.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) > 7 AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) < 15` : ''}
        GROUP BY
          ism.userId,
          ism.grade,
          ${query.productId ? `ism.productId,` : ''}
          ${query.domain ? 'ism.domain,' : ''}
          activity_day
      ),
      
      activity_sessions_summary_cte AS (
        SELECT        
          grade,
          ${query.productId ? `productId,` : ''}
          ${query.domain ? 'domain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'activity_day AS day,' : 'toStartOfWeek(activity_day) AS day,'}
          sum(daily_total_seconds) AS sum_duration,
          sum(session_count) AS total_sessions,
          uniq(userId) AS total_active_users,
          sum(daily_page_views) AS sum_page_views
        FROM daily_grade_activity
        GROUP BY
          grade
          ${query.productId ? ',productId' : ''}
          ${query.domain ? ',domain' : ''}        
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      ),
      
      idle_sessions_summary_cte AS (
        SELECT
          grade,
          ${query.productId ? 'productId,' : ''}
          ${query.domain ? 'domain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'activity_day AS day,' : 'toStartOfWeek(activity_day) AS day,'}
          sum(daily_idle_seconds) AS sum_idle_duration,
          sum(idle_ping_count) AS total_idle_pings
        FROM daily_idle_activity
        GROUP BY
          grade
          ${query.productId ? ',productId' : ''}
          ${query.domain ? ',domain' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      ),
      
      user_actual_daily_activity_cte AS (
        SELECT 
          ism.userId AS userId,
          ism.grade AS grade,
          toStartOfDay(ism.startTime) AS activity_day,
          sum(ism.duration) AS total_duration_on_activity_day
        FROM goteacher.interaction_summaries ism
        WHERE 
          ism.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND ism.eventName = 'INTERACTION_SUMMARY'          
          ${query.fromDate ? `AND ism.startTime >= ${Math.floor(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND ism.startTime <= ${Math.floor(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND ism.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND ism.role = 'student'` : ''}
        GROUP BY 
          ism.userId,
          ism.grade,
          activity_day
      ),
      
      quantile_daily_users_cte AS (
        SELECT 
          userId,
          grade,
          quantileExact(0.75)(total_duration_on_activity_day) AS p75_user_daily_duration
        FROM user_actual_daily_activity_cte
        WHERE total_duration_on_activity_day > 0
        GROUP BY
          userId,
          grade
      ),
      
      quantile_grade_users_cte AS (
        SELECT 
          grade,
          quantileExact(0.75)(p75_user_daily_duration) AS p75_user_daily_duration_by_grade
        FROM quantile_daily_users_cte
        GROUP BY 
          grade
      )
      SELECT 
        ass.grade AS grade,
        ${query.productId ? 'ass.productId,' : ''}
        ${query.domain ? 'ass.domain,' : ''}
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'ass.day,'}
              
        sum(ass.total_sessions) AS count_sessions,
        sum(ass.sum_duration) AS sum_time_spent,
        sum(ass.sum_duration) / sum(ass.total_sessions) AS avg_session_duration,
      
        sum(ass.sum_page_views) AS page_views,
        sum(ass.total_active_users) AS active_users,
        qgu.p75_user_daily_duration_by_grade,
        sum(COALESCE(iss.sum_idle_duration, 0)) AS total_idle_time,
        sum(COALESCE(iss.total_idle_pings, 0)) AS idle_ping_count,

        ${dateDiffInDays > 14
        ? `sum_time_spent / ${dateDiffInDays} * 7 AS avg_screen_time`
        : `sum_time_spent / ${dateDiffInDays} AS avg_screen_time`}
                
      FROM activity_sessions_summary_cte ass
      LEFT JOIN quantile_grade_users_cte qgu ON ass.grade = qgu.grade
      LEFT JOIN idle_sessions_summary_cte iss ON ass.grade = iss.grade
        ${query.productId ? 'AND ass.productId = iss.productId' : ''}
        ${query.domain ? 'AND ass.domain = iss.domain' : ''}
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'AND ass.day = iss.day'}
      GROUP BY 
        ass.grade
        ${query.productId ? ',ass.productId' : ''}
        ${query.domain ? ',ass.domain' : ''}
        ${query.timeGranularity !== TimeGranularity.ALL ? ',ass.day' : ''}
        ,qgu.p75_user_daily_duration_by_grade
    `;

    this.logger.debug(sqlQuery);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult =
      await this.cacheService.get<any>(
        cachingKey,
      );

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    let enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.GRADE,
    ], "raw", userOrganizationIds);

    // adjust screen time
    enrichedData = enrichedData.map((d: any) => {
      if (d.avg_screen_time) {
        d.avg_screen_time = +d.active_users > 0 ? d.avg_screen_time / d.active_users : 0;
      }
      return d;
    });

    await this.cacheService.set(cachingKey, enrichedData, 6 * 60 * 60);

    return enrichedData;
  }
}
