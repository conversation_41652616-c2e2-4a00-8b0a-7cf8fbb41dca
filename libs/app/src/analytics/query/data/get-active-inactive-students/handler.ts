import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { I<PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetActiveInactiveStudentsQuery } from '@goteacher/app/analytics/query/data/get-active-inactive-students/query';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

export interface ActiveInactiveStudents {
  day?: Date | string;
  active_students: number;
  inactive_students: number;
  total_students: number;
}

@QueryHandler(GetActiveInactiveStudentsQuery)
export class GetActiveInactiveStudentsQueryHandler
  implements IQueryHandler<GetActiveInactiveStudentsQuery> {
  private readonly logger = new Logger(
    GetActiveInactiveStudentsQueryHandler.name,
  );
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetActiveInactiveStudentsQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const sqlQuery = `
      WITH daily_stats AS (
        SELECT 
          ${query.timeGranularity === TimeGranularity.DAILY
        ? `ds.day AS day,`
        : query.timeGranularity === TimeGranularity.WEEKLY
          ? `toStartOfWeek(ds.day) AS day,`
          : ``
      }
          uniq(ds.userId) AS active_students,
          ds.orgId as orgId
        FROM 
          ${query.productId ? `daily_product_metrics_per_user_sc` : `daily_domain_metrics_per_user_sc`} ds
        INNER JOIN goteacher.users u on u.userId = ds.userId
        WHERE 
          ${query.productId ? `ds.productId = '${query.productId}'` : `ds.domain = '${query.domain}'`}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.grade = 'N/A'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND toInt8OrNull(u.grade) IS NOT null` : ''}
          ${query.fromDate ? `AND toDateTime(ds.day) >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND toDateTime(ds.day) <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          AND orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
        GROUP BY 
          orgId
          ${query.timeGranularity !== TimeGranularity.ALL ? ',day' : ''}
      )
      SELECT   
          ${query.timeGranularity !== TimeGranularity.ALL ? 'day,' : ''}
          active_students AS active_students          
      FROM 
          daily_stats dstats      
      GROUP BY 
          ${query.timeGranularity !== TimeGranularity.ALL ? 'day,' : ''}
          active_students          
      ${query.timeGranularity !== TimeGranularity.ALL ? 'ORDER BY day' : ''}
    `;

    this.logger.debug(`sqlQuery: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: ActiveInactiveStudents[];
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    const maxActiveStudents = Math.max(
      ...data.map((item: ActiveInactiveStudents) => item.active_students),
    );
    const minActiveStudents = Math.min(
      ...data.map((item: ActiveInactiveStudents) => item.active_students),
    );

    await this.cacheService.set(
      cachingKey,
      { data: data, maxActiveStudents, minActiveStudents },
      6 * 60 * 60,
    );
    return {
      data: data,
      maxActiveStudents,
      minActiveStudents,
    };
  }
}
