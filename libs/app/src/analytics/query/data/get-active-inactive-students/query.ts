import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { ReqContext } from '@goteacher/app/auth';
import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';

export class GetActiveInactiveStudentsQuery extends BaseDateRangeQuery {
  constructor(obj: GetActiveInactiveStudentsQuery) {
    super();
    Object.assign(this, obj);
  }

  ctx: ReqContext;

  domain?: string;
  productId?: string;


  timeGranularity?: TimeGranularity = TimeGranularity.DAILY;
  userGroup?: UserGroup = UserGroup.BOTH;
  schoolIds?: string[];
  grades?: string[];

  forceRefresh?: boolean;
}
