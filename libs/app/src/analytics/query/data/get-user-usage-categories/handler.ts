import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { isMoreThanTwoWeeks } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { GetUserUsageCategoriesQuery } from './query';

@QueryHandler(GetUserUsageCategoriesQuery)
export class GetUserUsageCategoriesHandler
  implements IQueryHandler<GetUserUsageCategoriesQuery> {
  private readonly logger = new Logger(GetUserUsageCategoriesHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetUserUsageCategoriesQuery): Promise<any> {
    const userOrganizationIds: string[] = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const shouldUseWeekly = query.fromDate
      ? isMoreThanTwoWeeks(query.fromDate, query.toDate)
      : false;

    const sqlQuery = `
      WITH activity_sessions_cte AS (
        SELECT
          orgId,
          userId,
          domain,
          sessionId,          
          sum(duration) as session_duration          
        FROM goteacher.interaction_summaries
        WHERE
          orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND userId = '${query.userId}'
          AND eventName = 'INTERACTION_SUMMARY'          
          ${query.fromDate ? `AND startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}          
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 15` : ''}
        GROUP BY
          orgId,
          userId,
          sessionId,
          domain
      ), activity_sessions_summary_cte AS (
        SELECT          
          COALESCE(
            NULLIF(dictGet('goteacher.domain_categories_dict', 'category', (domain, orgId)), ''),
            dictGet('goteacher.domain_categories_dict', 'category', (domain, '00000000-0000-0000-0000-000000000000'))
          ) AS category,
          sum(session_duration) as sum_duration                              
        FROM activity_sessions_cte
        GROUP BY          
          category
      ), total_duration_cte AS (             
        SELECT sum(sum_duration) AS total_duration
        FROM activity_sessions_summary_cte
      )          
      SELECT
        ass.category,
        ass.sum_duration,
        round(ass.sum_duration / td.total_duration * 100, 2) AS percent_of_total
      FROM activity_sessions_summary_cte AS ass
      CROSS JOIN total_duration_cte td
      ORDER BY percent_of_total DESC;
    `;

    this.logger.debug(`sqlQuery get-user-usage-categories: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);

    return { data };
  }
}
