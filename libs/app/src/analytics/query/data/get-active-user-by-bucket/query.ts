import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';

import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';
import { ReqContext } from '@goteacher/app/auth';

export class GetActiveUsersBucketQuery extends BaseDateRangeQuery {
  constructor(obj: GetActiveUsersBucketQuery) {
    super();
    Object.assign(this, obj);
  }

  ctx: ReqContext;

  domain?: string;

  productId?: string;

  timeGranularity?: TimeGranularity = TimeGranularity.WEEKLY;
  userGroup?: UserGroup = UserGroup.BOTH;
  schoolIds?: string[];
  grades?: string[];

  forceRefresh?: boolean;
}
