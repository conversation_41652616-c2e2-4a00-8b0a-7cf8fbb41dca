import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';

import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';
import { PaginationRequest } from '@goteacher/app/utility';
import { ReqContext } from '@goteacher/app/auth';

abstract class PaginatedDateRangeQuery
  extends PaginationRequest
  implements BaseDateRangeQuery
{
  fromDate?: Date;
  toDate?: Date;
}

export class GetTopUsersQuery extends PaginatedDateRangeQuery {
  constructor(obj: GetTopUsersQuery) {
    super(obj);
    Object.assign(this, obj);
  }

  domain?: string;

  productId?: string;

  fromDate: Date;
  toDate: Date;

  search?: string;
  userGroup?: UserGroup = UserGroup.BOTH;
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;
  schoolIds?: string[];
  grades?: string[];

  forceRefresh?: boolean = false;
  ctx: ReqContext;
}
