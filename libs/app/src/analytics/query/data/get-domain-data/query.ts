import { UserGroup } from '@goteacher/app/analytics/types';
import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';

export class GetDomainDataQuery extends BaseDateRangeQuery {
  constructor(obj: GetDomainDataQuery) {
    super();
    Object.assign(this, obj);
  }

  ctx: any;
  forceRefresh?: boolean;

  domains?: string[];

  productIds?: string[];

  timeGranularity?: string;
  userGroup?: UserGroup = UserGroup.BOTH;
  schoolIds?: string[];
  grades?: string[];
}
