import { TimeGranularity } from '@goteacher/app/analytics/types';
import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';

export class GetUserData extends BaseDateRangeQuery {
  constructor(obj: GetUserData) {
    super();
    Object.assign(this, obj);
  }

  ctx: any;
  forceRefresh?: boolean;

  userId: string;

  timeGranularity?: TimeGranularity = TimeGranularity.ALL;
}
