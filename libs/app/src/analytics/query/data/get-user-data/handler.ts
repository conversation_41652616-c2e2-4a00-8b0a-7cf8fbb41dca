import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { IQuery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetUserData } from '@goteacher/app/analytics/query/data/get-user-data/query';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

@QueryHandler(GetUserData)
export class GetUserDataHandler implements IQueryHandler<GetUserData> {
  private readonly logger = new Logger(GetUserDataHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetUserData): Promise<any> {
    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.ceil(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24)
        ) + 1
        : 1;

    let toDateExclusiveEnd: Date | undefined;
    if (query.toDate) {
      toDateExclusiveEnd = new Date(query.toDate);
      toDateExclusiveEnd.setDate(toDateExclusiveEnd.getDate() + 1);
    }

    const sqlQuery = `
      WITH user_info AS (
        SELECT 
          DISTINCT (u.userId),
          u.schoolId, 
          u.orgId,
          u.role
        FROM goteacher.interaction_summaries u
        WHERE u.userId = '${query.userId}'
      ),
      user_sessions_cte AS (
        SELECT
          ism.sessionId as sessionId,
          ism.duration as session_duration,
          ism.startTime as startTime
        FROM goteacher.interaction_summaries as ism
        WHERE ism.userId = '${query.userId}'
          AND ism.eventName = 'INTERACTION_SUMMARY'
          ${query.fromDate ? `AND ism.startTime >= ${Math.floor(query.fromDate.getTime() / 1000)}` : ''}
          ${toDateExclusiveEnd ? `AND ism.startTime < ${Math.floor(toDateExclusiveEnd.getTime() / 1000)}` : ''}
      ),
      user_overall_summary_cte AS (
        SELECT
          sum(session_duration) as sum_time_spent_period,
          avg(session_duration) as avg_session_duration_period,
          count(DISTINCT sessionId) as count_sessions_period
        FROM user_sessions_cte
      ),
      user_daily_activity_cte AS (
        SELECT
          toStartOfDay(startTime) as activity_day,
          sum(session_duration) as daily_total_seconds
        FROM user_sessions_cte
        GROUP BY
          activity_day
      ),
      user_daily_stats_cte AS (
        SELECT
          quantileExact(0.5)(uda.daily_total_seconds) AS median_daily_seconds,
          quantileExact(0.75)(uda.daily_total_seconds) AS p75_daily_seconds,
          (
            SELECT arrayAvg(
                      arraySlice(
                        arraySort(s_arr),
                        toUInt64(floor(length(s_arr) * 0.1)) + 1,
                        toUInt64(length(s_arr) - 2 * floor(length(s_arr) * 0.1))
                      )
                   )
            FROM (SELECT groupArray(daily_total_seconds) AS s_arr FROM user_daily_activity_cte WHERE daily_total_seconds > 0)
          ) AS trimmed_mean_daily_seconds
        FROM user_daily_activity_cte uda
        WHERE uda.daily_total_seconds > 0
      ),
      org_all_users_each_day_activity_cte AS (
        SELECT
          ism.userId as userId,
          toStartOfDay(ism.startTime) as activity_day, 
          sum(ism.duration) as daily_total_seconds_for_user 
        FROM goteacher.interaction_summaries as ism
        CROSS JOIN user_info ui
        WHERE ism.orgId = ui.orgId
          AND ism.schoolId = ui.schoolId 
          AND ism.role = ui.role
          ${query.fromDate ? `AND ism.startTime >= ${Math.floor(query.fromDate.getTime() / 1000)}` : ''}
          ${toDateExclusiveEnd ? `AND ism.startTime < ${Math.floor(toDateExclusiveEnd.getTime() / 1000)}` : ''}
        GROUP BY userId, activity_day
      ),
      org_users_median_daily_screen_time_cte AS (
        SELECT
          org_user_daily.userId as userId,
          quantileExact(0.5)(COALESCE(org_user_daily.daily_total_seconds_for_user, 0)) as median_daily_screen_time_seconds_for_org_user
        FROM org_all_users_each_day_activity_cte org_user_daily
        WHERE org_user_daily.daily_total_seconds_for_user > 0 
        GROUP BY org_user_daily.userId 
      ),
      ranked_org_users_cte AS (
        SELECT
          userId,
          median_daily_screen_time_seconds_for_org_user,
          rank() OVER (ORDER BY median_daily_screen_time_seconds_for_org_user DESC) as screen_time_median_rank,
          count() OVER () as total_users_ranked
        FROM org_users_median_daily_screen_time_cte
      ),
      organization_user_count_cte AS (
        SELECT
          count(DISTINCT ism.userId) as total_users_in_comparison_group
        FROM goteacher.interaction_summaries ism
        CROSS JOIN user_info ui
        WHERE ism.orgId = ui.orgId
          AND ism.schoolId = ui.schoolId
          AND ism.role = ui.role
          ${query.fromDate ? `AND ism.startTime >= ${Math.floor(query.fromDate.getTime() / 1000)}` : ''}
          ${toDateExclusiveEnd ? `AND ism.startTime < ${Math.floor(toDateExclusiveEnd.getTime() / 1000)}` : ''}
      )
      SELECT
        ui.userId as userId,
        COALESCE(uos.sum_time_spent_period, 0) as sum_time_spent,
        COALESCE(uos.avg_session_duration_period, 0) as avg_session_duration,
        COALESCE(uos.count_sessions_period, 0) as count_sessions,
        COALESCE(uds.median_daily_seconds, 0) as median_daily_screen_time_seconds,
        COALESCE(uds.p75_daily_seconds, 0) as p75_daily_screen_time_seconds,
        COALESCE(uds.trimmed_mean_daily_seconds, 0) as trimmed_mean_daily_screen_time_seconds,
        (CASE 
            WHEN ${dateDiffInDays} > 14 THEN COALESCE(uds.median_daily_seconds, 0) * 7
         END) as median_weekly_screen_time_seconds_calc,
        COALESCE(r_org.screen_time_median_rank, 0) as screen_time_median_rank,
        COALESCE(r_org.total_users_ranked, 0) as total_users_ranked,
        COALESCE(ouc.total_users_in_comparison_group, 0) as total_users_in_comparison_group,
        COALESCE(r_org.median_daily_screen_time_seconds_for_org_user, 0) as user_median_seconds_for_ranking
      FROM user_info ui
      LEFT JOIN user_overall_summary_cte AS uos ON 1=1
      LEFT JOIN user_daily_stats_cte AS uds ON 1=1
      LEFT JOIN organization_user_count_cte AS ouc ON 1=1
      LEFT JOIN ranked_org_users_cte AS r_org ON r_org.userId = ui.userId
    `;

    this.logger.debug(`sqlQuery: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      console.log('Returning cached data for GetUserData');
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    console.log('data', data);

    const enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.USER,
    ]);

    await this.cacheService.set(
      cachingKey,
      { data: enrichedData },
      6 * 60 * 60, // 6 hours TTL
    );

    return { data: enrichedData };
  }
}
