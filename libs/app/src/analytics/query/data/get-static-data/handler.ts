import { GetStaticDataQuery } from '@goteacher/app/analytics/query/data/get-static-data/query';
import { IStorageService } from '@goteacher/infra/storage/storage.service';
import { NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON>uery<PERSON>andler, QueryHandler } from '@nestjs/cqrs';

@QueryHandler(GetStaticDataQuery)
export class GetStaticDataQueryHandler
  implements IQueryHandler<GetStaticDataQuery>
{
  constructor(
    private storageService: IStorageService,
    private configService: ConfigService,
  ) {}

  async execute(query: GetStaticDataQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );
    const domain = query.domain.replace('.', '_');

    const data = {};

    await Promise.all(
      userOrganizationIds.map(async (orgId) => {
        try {
          const orgData = await this.storageService.getObjectAsString(
            this.configService.get('DATA_BUCKET'),
            `${orgId}_${domain}.json`,
          );
          data[orgId] = JSON.parse(orgData);
        } catch {
          throw new NotFoundException('No data found!');
        }
      }),
    );

    return data;
  }
}
