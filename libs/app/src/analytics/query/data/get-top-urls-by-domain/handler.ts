import { isMoreThanTwoWeeks, parseOrderBy } from '@goteacher/app/utility';
import { IQueryH<PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTopUrlsByDomainQuery } from '@goteacher/app/analytics/query/data/get-top-urls-by-domain/query';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

@QueryHandler(GetTopUrlsByDomainQuery)
export class GetTopUrlsByDomainQueryHandler
  implements IQueryHandler<GetTopUrlsByDomainQuery> {
  private readonly logger = new Logger(GetTopUrlsByDomainQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetTopUrlsByDomainQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    const shouldUseWeekly = query.fromDate
      ? isMoreThanTwoWeeks(query.fromDate, query.toDate)
      : false;

    const tableToBeUsed = shouldAggregateAll
      ? 'all_url_metrics_sc'
      : query.timeGranularity === TimeGranularity.DAILY
        ? 'daily_url_metrics_sc'
        : shouldUseWeekly
          ? 'weekly_url_metrics_sc'
          : 'daily_url_metrics_sc';

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );        

    const sqlQuery = `
      WITH activity_sessions_cte AS (
        SELECT 
          domain,
          url,
          sessionId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(startTime) AS day,' : 'toStartOfWeek(startTime) AS day,'} 
          sum(duration) as session_duration          
        FROM goteacher.activity_sessions as as
        LEFT JOIN goteacher.users u on u.userId = as.userId
        WHERE
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})          
          AND as.domain = '${query.domain}'             
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) > 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14` : ''}
        GROUP BY
          domain,
          url,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sessionId
      ), activity_sessions_summary_cte AS (
        SELECT 
          domain,
          url,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sum(session_duration) as sum_duration,
          avg(session_duration) as avg_duration,
          count(sessionId) as count_sessions,
          uniqState(userId) as active_users,          
          sum(page_views) as sum_page_views          
        FROM activity_sessions_cte
        GROUP BY
          domain,
          url,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT 
        ${query.timeGranularity === 'all' ? '' : 'day,'}  
        domain,
        url,                

        ass.sum_duration as sum_time_spent,
        ass.avg_duration as avg_session_duration,
        ass.avg_duration as avg_time_spent,      
        ass.count_sessions as count_sessions,
        ass.sum_page_views as page_views,
        ass.active_users as active_users
      FROM activity_sessions_summary_cte as ass
      GROUP BY
        domain,
        url,
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day'}
      ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
      LIMIT ${query.limit} 
      OFFSET ${query.offset};      
    `;
    this.logger.debug(`sqlQuery get-top-urls-by-domain: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
      total: number;
      limit: number;
      offset: number;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();
    await this.cacheService.set(
      cachingKey,
      {
        data,
        total: rows_before_limit_at_least,
        limit: query.limit,
        offset: query.offset,
      },
      6 * 60 * 60,
    );

    return {
      data,
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
