import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { ReqContext } from '@goteacher/app/auth';
import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';
import { ApprovalStatus, PaymentStatus } from '@goteacher/app/models/mongo';
import { PaginationRequest } from '@goteacher/app/utility';

abstract class PaginatedDateRangeQuery
  extends PaginationRequest
  implements BaseDateRangeQuery
{
  fromDate?: Date;
  toDate?: Date;
}

export class GetTopProductsQuery extends PaginatedDateRangeQuery {
  constructor(obj: GetTopProductsQuery) {
    super();
    Object.assign(this, obj);
  }

  ctx: ReqContext;
  forceRefresh?: boolean;

  timeGranularity?: TimeGranularity = TimeGranularity.WEEKLY;
  userGroup?: UserGroup = UserGroup.BOTH;
  schoolIds?: string[];
  grades?: string[];
  price?: PaymentStatus;
  search?: string;
  approvalStatus?: ApprovalStatus;
}
