import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTopProductsQuery } from '@goteacher/app/analytics/query/data/get-analytics-list-products/query';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service/enrichment.service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import {
  Contract,
  OrganizationDomainMetadata,
} from '@goteacher/app/models/mongo';
import { parseOrderBy } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(GetTopProductsQuery)
export class GetTopProductsQueryHandler
  implements IQueryHandler<GetTopProductsQuery> {
  private readonly logger = new Logger(GetTopProductsQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    @InjectModel(OrganizationDomainMetadata.name)
    private readonly metadataModel: Model<OrganizationDomainMetadata>,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetTopProductsQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    const filterDomainClause = '';
    // if (query.price) {
    //   const contractsDomains = await this.contractModel
    //     .find({
    //       organizationId: {
    //         $in: userOrganizationIds,
    //       },
    //       active: true,
    //       deleted: false,
    //       subscriptionStartDate: { $lte: new Date() },
    //       subscriptionEndDate: { $gte: new Date() },
    //     })
    //     .select('domain');

    //   if (query.price === PaymentStatus.PAID)
    //     filterDomainClause = `AND domain IN (${contractsDomains.map((c) => `'${c.domain}'`).join(',')})`;
    //   else if (query.price === PaymentStatus.FREE)
    //     filterDomainClause = `AND domain NOT IN (${contractsDomains.map((c) => `'${c.domain}'`).join(',')})`;
    // }

    // if (query.approvalStatus) {
    //   const metadataDomains = await this.metadataModel
    //     .find({
    //       organizationId: {
    //         $in: userOrganizationIds,
    //       },
    //       approvalStatus: query.approvalStatus,
    //     })
    //     .select('domain');
    //   const inDomains = metadataDomains.map((d) => d.domain);
    //   filterDomainClause += `AND domain IN (${inDomains.map((d) => `'${d}'`).join(',')})`;
    // }

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    const calculatedRange = dateDiffInDays > 14 ? dateDiffInDays / 7 : dateDiffInDays;
    let avgTimeSpentCalculation = `sum(dscte.sum_time_spent) / ${calculatedRange} as avg_screen_time,`;
    if (query.userGroup === UserGroup.TEACHER) {
      avgTimeSpentCalculation = `sum(dscte.sum_time_spent_teacher) / ${calculatedRange} as avg_screen_time,`;
    }
    if (query.userGroup === UserGroup.STUDENT) {
      avgTimeSpentCalculation = `sum(dscte.sum_time_spent_student) / ${calculatedRange} as avg_screen_time,`;
    }

    const sqlQuery = `
      WITH activity_sessions_cte AS (
        SELECT 
          productId,
          userId,
          sessionId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(as.startTime) AS day,' : 'toStartOfWeek(as.startTime) AS day,'} 
          sum(duration) as session_duration,
          count(DISTINCT as.url) as page_views
        FROM goteacher.activity_sessions as as
        LEFT JOIN goteacher.users u on u.userId = as.userId
        WHERE
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          ${filterDomainClause}
          ${query.search ? `AND domain LIKE '%${query.search}%'` : ''}
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) > 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14` : ''}
        GROUP BY
          productId,
          userId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sessionId
      ), activity_sessions_summary_cte AS (
        SELECT 
          productId,          
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sum(session_duration) as sum_duration,
          avg(session_duration) as avg_duration,
          count(sessionId) as count_sessions,
          sum(page_views) as sum_page_views,
          uniqState(userId) as active_users
        FROM activity_sessions_cte
        GROUP BY
          productId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT 
        ${query.timeGranularity === 'all' ? '' : 'day,'}  
        productId,              
        sum(ass.count_sessions) as count_sessions,        
        sum(ass.sum_duration) as sum_time_spent,        
        avg(ass.avg_duration) as avg_session_duration,        
        avg(ass.avg_duration) as avg_time_spent,        
        sum(ass.sum_page_views) as page_views,                
        uniqMerge(ass.active_users) as active_users,           
        sum_time_spent / ${dateDiffInDays} as avg_screen_time
      FROM activity_sessions_summary_cte ass
      GROUP BY
        domain
        ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
      LIMIT ${query.limit} 
      OFFSET ${query.offset};          
    `;
    this.logger.debug(`sqlQuery get-analytics-list-products: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
      total: number;
      limit: number;
      offset: number;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    let enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.PRODUCT,
    ]);
    // const filteredProducts = enrichedData.filter(d => d.productName !== 'Other');

    // adjust screen time
    enrichedData = enrichedData.map((d: any) => {
      if (d.avg_screen_time) {
        if (query.userGroup === UserGroup.BOTH) {
          d.avg_screen_time = +d.active_users > 0 ? d.avg_screen_time / d.active_users : 0;
        } else if (query.userGroup === UserGroup.TEACHER) {
          d.avg_screen_time = +d.active_users_teacher > 0 ? d.avg_screen_time / d.active_users_teacher : 0;
        } else if (query.userGroup === UserGroup.STUDENT) {
          d.avg_screen_time = +d.active_users_student > 0 ? d.avg_screen_time / d.active_users_student : 0;
        }
      }
      return d;
    });

    await this.cacheService.set(
      cachingKey,
      {
        data: enrichedData,
        total: rows_before_limit_at_least,
        limit: query.limit,
        offset: query.offset,
      },
      6 * 60 * 60,
    );

    return {
      data: enrichedData,
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
