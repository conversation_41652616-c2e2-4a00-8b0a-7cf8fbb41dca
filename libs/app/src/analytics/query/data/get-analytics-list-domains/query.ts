import { ApprovalStatus, PaymentStatus } from '@goteacher/app/models/mongo';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { AgreementStatus } from '@goteacher/app/analytics/service/enrichment.service';

import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';
import { PaginationRequest } from '@goteacher/app/utility';
import { ReqContext } from '@goteacher/app/auth';

abstract class PaginatedDateRangeQuery
  extends PaginationRequest
  implements BaseDateRangeQuery
{
  fromDate?: Date;
  toDate?: Date;
}

export class GetTopDomainsQuery extends PaginatedDateRangeQuery {
  constructor(obj: GetTopDomainsQuery) {
    super();
    Object.assign(this, obj);
  }

  ctx: ReqContext;
  forceRefresh?: boolean;

  timeGranularity?: TimeGranularity = TimeGranularity.WEEKLY;
  userGroup?: UserGroup = UserGroup.BOTH;
  schoolIds?: string[];
  grades?: string[];
  price?: PaymentStatus;
  search?: string;
  approvalStatus?: ApprovalStatus;
  riskLevel?: string;
  categories?: string[];
  sdpcAgreementStatus?: AgreementStatus;
}
