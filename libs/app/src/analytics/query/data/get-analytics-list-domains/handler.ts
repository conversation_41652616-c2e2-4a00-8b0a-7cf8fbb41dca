import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTopDomainsQuery } from '@goteacher/app/analytics/query/data/get-analytics-list-domains/query';
import {
  AgreementStatus,
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service/enrichment.service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import {
  Contract,
  OrganizationDomainMetadata,
  PaymentStatus,
  SDPCAgreement,
} from '@goteacher/app/models/mongo';
import { CustomMetadata } from '@goteacher/app/models/mongo/custom.metadata.model';
import { DomainMetadata } from '@goteacher/app/models/mongo/domain.metadata.model';
import { extractDomainFromUrl, parseOrderBy } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { I<PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(GetTopDomainsQuery)
export class GetTopDomainsQueryHandler
  implements IQueryHandler<GetTopDomainsQuery> {
  private readonly logger = new Logger(GetTopDomainsQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    @InjectModel(OrganizationDomainMetadata.name)
    private readonly metadataModel: Model<OrganizationDomainMetadata>,
    @InjectModel(DomainMetadata.name) private readonly domainMetadataModel: Model<DomainMetadata>,
    @InjectModel(CustomMetadata.name) private readonly customMetadataModel: Model<CustomMetadata>,
    @InjectModel(SDPCAgreement.name) private readonly sdpcAgreementModel: Model<SDPCAgreement>,
    private readonly enrichmentService: EnrichmentService,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetTopDomainsQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    //test districtId DEV: '9139'

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    let filterDomainClause = '';
    if (query.price) {
      const contractsDomains = await this.contractModel
        .find({
          organizationId: {
            $in: userOrganizationIds,
          },
          // active: true,
          deleted: false,
          // subscriptionStartDate: { $lte: new Date() },
          // subscriptionEndDate: { $gte: new Date() },
        })
        .select('domain');

      if (query.price === PaymentStatus.PAID) {
        if (contractsDomains.length === 0) {
          return {
            data: [],
            total: 0,
            limit: query.limit,
            offset: query.offset,
          };
        }
        filterDomainClause = ` AND domain IN (${contractsDomains.map((c) => `'${c.domain}'`).join(',')})`;
      } else if (query.price === PaymentStatus.FREE) {
        if (contractsDomains.length > 0) {
          filterDomainClause = ` AND domain NOT IN (${contractsDomains.map((c) => `'${c.domain}'`).join(',')})`;
        }
      }
    }

    if (query.approvalStatus) {
      const metadataDomains = await this.metadataModel
        .find({
          organizationId: {
            $in: userOrganizationIds,
          },
          approvalStatus: query.approvalStatus,
        })
        .select('domain');
      const inDomains = metadataDomains.map((d) => d.domain);
      if (inDomains.length === 0) {
        return {
          data: [],
          total: 0,
          limit: query.limit,
          offset: query.offset,
        };
      }
      filterDomainClause += ` AND domain IN (${inDomains.map((d) => `'${d}'`).join(',')})`;
    }

    if (query.riskLevel) {
      const customMetadataDomains = await this.customMetadataModel
        .find({
          riskLevel: query.riskLevel,
        })
        .select('domain');
      let inDomains = customMetadataDomains.map((d) => d.domain);

      const metadataDomains = await this.domainMetadataModel
        .find({
          riskLevel: query.riskLevel,
          domain: { $nin: inDomains },
        })
        .select('domain');

      inDomains = [...inDomains, ...metadataDomains.map((d) => d.domain)];

      if (inDomains.length === 0) {
        return {
          data: [],
          total: 0,
        };
      }
      filterDomainClause += ` AND domain IN (${inDomains.map((d) => `'${d}'`).join(',')})`;
    }

    if (query.categories) {
      const customMetadataDomains = await this.customMetadataModel
        .find({
          categories: { $in: query.categories },
        })
        .select('domain');
      let inDomains = customMetadataDomains.map((d) => d.domain);

      const metadataDomains = await this.domainMetadataModel
        .find({
          categories: { $in: query.categories },
          domain: { $nin: inDomains },
        })
        .select('domain');

      inDomains = [...inDomains, ...metadataDomains.map((d) => d.domain)];

      if (inDomains.length === 0) {
        return {
          data: [],
          total: 0,
        };
      }
      filterDomainClause += ` AND domain IN (${inDomains.map((d) => `'${d}'`).join(',')})`;
    }


    // Helper function to extract domain from website URL (shared between filter and status calculation)
    const extractDomainForMatching = (website: string): string => {
      return (extractDomainFromUrl(website) || '').toLowerCase();
    };

    this.logger.debug(`SDPC Filter Check - sdpcAgreementStatus: ${query.sdpcAgreementStatus}, sdpcDistrictId: ${query.ctx.sdpcDistrictId}`);
    
    if (query.sdpcAgreementStatus && query.ctx.sdpcDistrictId) {
      this.logger.debug(`SDPC Filter - Status: ${query.sdpcAgreementStatus}, District: ${query.ctx.sdpcDistrictId}`);
      
      // Get all SDPC agreements for this district
      const allSdpcAgreements = await this.sdpcAgreementModel.find({
        districtid: query.ctx.sdpcDistrictId,
      }).select('website date_expired date_approved');
      
      this.logger.debug(`SDPC Filter - Found ${allSdpcAgreements.length} agreements for district`);

      let filterDomains: string[] = [];

      if (query.sdpcAgreementStatus === AgreementStatus.ACTIVE) {
        // Active: has agreement with valid dates
        const today = new Date();
        this.logger.debug(`SDPC Filter - Checking for ACTIVE agreements. Today: ${today.toISOString()}`);
        
        filterDomains = allSdpcAgreements
          .filter((agreement) => {
            const dateApproved = agreement.date_approved ? new Date(agreement.date_approved) : null;
            const dateExpired = agreement.date_expired ? new Date(agreement.date_expired) : null;
            
            this.logger.debug(`SDPC Filter - Agreement: ${agreement.website}, approved: ${dateApproved?.toISOString()}, expired: ${dateExpired?.toISOString()}`);
            
            if (dateApproved && dateExpired) {
              return today >= dateApproved && today <= dateExpired;
            } else if (dateApproved && !dateExpired) {
              // No expiry date means still active
              return today >= dateApproved;
            }
            return false;
          })
          .map(agreement => extractDomainForMatching(agreement.website))
          .filter(Boolean);
        
        this.logger.debug(`SDPC Filter - Active domains found: ${JSON.stringify(filterDomains)}`);
      } else if (query.sdpcAgreementStatus === AgreementStatus.EXPIRED) {
        // Expired: has expired agreement
        const today = new Date();
        this.logger.debug(`SDPC Filter - Checking for EXPIRED agreements`);
        
        filterDomains = allSdpcAgreements
          .filter((agreement) => {
            const dateExpired = agreement.date_expired ? new Date(agreement.date_expired) : null;
            const isExpired = dateExpired && today > dateExpired;
            this.logger.debug(`SDPC Filter - Agreement: ${agreement.website}, expired: ${dateExpired?.toISOString()}, isExpired: ${isExpired}`);
            return isExpired;
          })
          .map(agreement => extractDomainForMatching(agreement.website))
          .filter(Boolean);
        
        this.logger.debug(`SDPC Filter - Expired domains found: ${JSON.stringify(filterDomains)}`);
      } else if (query.sdpcAgreementStatus === AgreementStatus.INACTIVE) {
        // Inactive: no agreement at all
        this.logger.debug(`SDPC Filter - Checking for INACTIVE (no agreement) domains`);
        
        const agreementDomains = allSdpcAgreements
          .map(agreement => extractDomainForMatching(agreement.website))
          .filter(Boolean);
        
        this.logger.debug(`SDPC Filter - Domains with agreements: ${JSON.stringify(agreementDomains)}`);
        
        // We need to exclude domains that have agreements
        if (agreementDomains.length > 0) {
          filterDomainClause += ` AND domain NOT IN (${agreementDomains.map((d) => `'${d}'`).join(',')})`;
          this.logger.debug(`SDPC Filter - Added NOT IN clause for INACTIVE filter`);
        } else {
          this.logger.debug(`SDPC Filter - No agreements found, all domains are INACTIVE`);
        }
        // If no agreements exist, all domains are inactive - no additional filter needed
      }

      // Apply filter for ACTIVE and INACTIVE_W_CONTRACT statuses
      if (query.sdpcAgreementStatus !== AgreementStatus.INACTIVE) {
        this.logger.debug(`SDPC Filter - Filter domains for ${query.sdpcAgreementStatus}: ${JSON.stringify(filterDomains)}`);
        
        if (filterDomains.length === 0) {
          this.logger.debug(`SDPC Filter - No domains match criteria, returning empty result`);
          // No domains match the criteria
          return {
            data: [],
            total: 0,
            limit: query.limit,
            offset: query.offset,
          };
        }
        filterDomainClause += ` AND domain IN (${filterDomains.map((d) => `'${d}'`).join(',')})`;
      }
      
      this.logger.debug(`SDPC Filter - Final filterDomainClause: ${filterDomainClause}`);
    }

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    this.logger.debug(`SDPC Filter - Final filterDomainClause before SQL: ${filterDomainClause}`);
    
    let sqlQuery = '';
    
    // When we need median calculations and have date ranges, use CTE approach
    if (query.fromDate && query.toDate && query.timeGranularity !== TimeGranularity.DAILY && query.timeGranularity !== TimeGranularity.WEEKLY) {
      sqlQuery = `
        WITH domains_with_interactions AS (
          SELECT DISTINCT domain
          FROM goteacher.interaction_summaries
          WHERE
            orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            AND domain != 'newtab.'
            AND eventName = 'INTERACTION_SUMMARY'
            ${filterDomainClause}
            ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
            ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.fromDate ? `AND startTime >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
            ${query.toDate ? `AND startTime <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
            ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
            ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
            ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
        ),
        daily_aggregates AS (
          SELECT 
            toStartOfDay(startTime) AS day,
            domain,
            uniqIf(sessionId, eventName = 'INTERACTION_SUMMARY') as daily_sessions,
            sumIf(duration, eventName = 'INTERACTION_SUMMARY') as daily_time_spent,
            sumIf(duration, eventName = 'IDLE_PING') as daily_idle_time,
            sum(duration) as daily_total_time,
            uniqIf(userId, eventName = 'INTERACTION_SUMMARY') as daily_active_users,
            countIf(eventName = 'INTERACTION_SUMMARY' AND role = 'teacher') as daily_teacher_interactions,
            countIf(eventName = 'INTERACTION_SUMMARY' AND role = 'student') as daily_student_interactions,
            uniqIf(userId, eventName = 'INTERACTION_SUMMARY' AND role = 'teacher') as daily_active_teachers,
            uniqIf(userId, eventName = 'INTERACTION_SUMMARY' AND role = 'student') as daily_active_students
          FROM goteacher.interaction_summaries
          WHERE
            orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            AND domain != 'newtab.'
            AND domain GLOBAL IN (SELECT domain FROM domains_with_interactions)
            ${filterDomainClause}
            ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
            ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.fromDate ? `AND startTime >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
            ${query.toDate ? `AND startTime <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
            ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
            ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
            ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
          GROUP BY day, domain
        ),
        domain_unique_urls AS (
          SELECT 
            domain,
            uniqIf(url, eventName = 'INTERACTION_SUMMARY') as unique_urls_total
          FROM goteacher.interaction_summaries
          WHERE
            orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            AND domain != 'newtab.'
            AND domain GLOBAL IN (SELECT domain FROM domains_with_interactions)
            ${filterDomainClause}
            ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
            ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.fromDate ? `AND startTime >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
            ${query.toDate ? `AND startTime <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
            ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
            ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
            ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
          GROUP BY domain
        )
        SELECT 
          da.domain,
          sum(da.daily_sessions) as count_sessions,
          sum(da.daily_time_spent) as sum_time_spent,
          sum(da.daily_idle_time) as total_idle_time,
          sum(da.daily_total_time) as total_time,
          avg(da.daily_time_spent) as avg_session_duration,
          avg(da.daily_time_spent) as avg_time_spent,
          du.unique_urls_total as page_views,
          max(da.daily_active_users) as active_users,
          sum(da.daily_time_spent) / ${dateDiffInDays} as avg_screen_time,
          median(da.daily_sessions) as median_daily_sessions,
          median(da.daily_time_spent) as median_daily_time_spent,
          median(da.daily_idle_time) as median_daily_idle_time,
          median(da.daily_active_users) as median_daily_active_users
        FROM daily_aggregates da
        JOIN domain_unique_urls du ON da.domain = du.domain
        GROUP BY da.domain, du.unique_urls_total
        ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
        LIMIT ${query.limit} 
        OFFSET ${query.offset};
      `;
    } else if (query.timeGranularity === TimeGranularity.DAILY || query.timeGranularity === TimeGranularity.WEEKLY) {
      // For DAILY and WEEKLY, we want median calculations per time period
      const timeFunction = query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay' : 'toStartOfWeek';
      
      sqlQuery = `
        WITH domains_with_interactions AS (
          SELECT DISTINCT domain
          FROM goteacher.interaction_summaries
          WHERE
            orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            AND domain != 'newtab.'
            AND eventName = 'INTERACTION_SUMMARY'
            ${filterDomainClause}
            ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
            ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.fromDate ? `AND startTime >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
            ${query.toDate ? `AND startTime <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
            ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
            ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
            ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
        ),
        hourly_aggregates AS (
          SELECT 
            ${timeFunction}(startTime) AS period,
            toStartOfHour(startTime) AS hour,
            domain,
            uniqIf(sessionId, eventName = 'INTERACTION_SUMMARY') as hourly_sessions,
            sumIf(duration, eventName = 'INTERACTION_SUMMARY') as hourly_time_spent,
            sumIf(duration, eventName = 'IDLE_PING') as hourly_idle_time,
            sum(duration) as hourly_total_time,
            uniqIf(userId, eventName = 'INTERACTION_SUMMARY') as hourly_active_users
          FROM goteacher.interaction_summaries
          WHERE
            orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            AND domain != 'newtab.'
            AND domain GLOBAL IN (SELECT domain FROM domains_with_interactions)
            ${filterDomainClause}
            ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
            ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.fromDate ? `AND startTime >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
            ${query.toDate ? `AND startTime <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
            ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
            ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
            ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
          GROUP BY period, hour, domain
        ),
        period_urls AS (
          SELECT 
            ${timeFunction}(startTime) AS period,
            domain,
            uniqIf(url, eventName = 'INTERACTION_SUMMARY') as unique_urls_per_period
          FROM goteacher.interaction_summaries
          WHERE
            orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            AND domain != 'newtab.'
            AND domain GLOBAL IN (SELECT domain FROM domains_with_interactions)
            ${filterDomainClause}
            ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
            ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.fromDate ? `AND startTime >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
            ${query.toDate ? `AND startTime <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
            ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
            ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
            ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
          GROUP BY period, domain
        )
        SELECT 
          ha.period AS day,
          ha.domain,
          sum(ha.hourly_sessions) as count_sessions,
          sum(ha.hourly_time_spent) as sum_time_spent,
          sum(ha.hourly_idle_time) as total_idle_time,
          sum(ha.hourly_total_time) as total_time,
          avg(ha.hourly_time_spent) as avg_session_duration,
          avg(ha.hourly_time_spent) as avg_time_spent,
          pu.unique_urls_per_period as page_views,
          max(ha.hourly_active_users) as active_users,
          sum(ha.hourly_time_spent) / ${dateDiffInDays} as avg_screen_time,
          median(ha.hourly_sessions) as median_hourly_sessions,
          median(ha.hourly_time_spent) as median_hourly_time_spent,
          median(ha.hourly_idle_time) as median_hourly_idle_time,
          median(ha.hourly_active_users) as median_hourly_active_users
        FROM hourly_aggregates ha
        JOIN period_urls pu ON ha.period = pu.period AND ha.domain = pu.domain
        GROUP BY ha.period, ha.domain, pu.unique_urls_per_period
        ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
        LIMIT ${query.limit} 
        OFFSET ${query.offset};
      `;
    } else {
      // Original query for ALL without date ranges
      sqlQuery = `
        WITH domains_with_interactions AS (
          SELECT DISTINCT domain
          FROM goteacher.interaction_summaries
          WHERE
            orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            AND domain != 'newtab.'
            AND eventName = 'INTERACTION_SUMMARY'
            ${filterDomainClause}
            ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
            ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.fromDate ? `AND startTime >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
            ${query.toDate ? `AND startTime <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
            ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
            ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
            ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
        )
        SELECT 
          domain,
          uniqIf(sessionId, eventName = 'INTERACTION_SUMMARY') as count_sessions,
          sumIf(duration, eventName = 'INTERACTION_SUMMARY') as sum_time_spent,
          sumIf(duration, eventName = 'IDLE_PING') as total_idle_time,
          sum(duration) as total_time,
          avgIf(duration, eventName = 'INTERACTION_SUMMARY') as avg_session_duration,
          avgIf(duration, eventName = 'INTERACTION_SUMMARY') as avg_time_spent,
          uniqIf(url, eventName = 'INTERACTION_SUMMARY') as page_views,
          uniqIf(userId, eventName = 'INTERACTION_SUMMARY') as active_users,
          sumIf(duration, eventName = 'INTERACTION_SUMMARY') / ${dateDiffInDays} as avg_screen_time
        FROM goteacher.interaction_summaries isu
        WHERE
          orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND domain != 'newtab.'
          AND domain GLOBAL IN (SELECT domain FROM domains_with_interactions)
          ${filterDomainClause}
          ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
          ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND startTime >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
          ${query.toDate ? `AND startTime <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
        GROUP BY
          domain
        ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
        LIMIT ${query.limit} 
        OFFSET ${query.offset};                    
      `;
    }
    this.logger.debug(`sqlQuery get-analytics-list-domains: ${sqlQuery}`);

    // Create a comprehensive cache key that includes all query parameters
    const cacheKeyParams = {
      sqlQuery,
      organizationIds: userOrganizationIds.sort().join(','),
      timeGranularity: query.timeGranularity,
      userGroup: query.userGroup,
      schoolIds: query.schoolIds?.sort().join(','),
      grades: query.grades?.sort().join(','),
      price: query.price,
      search: query.search,
      approvalStatus: query.approvalStatus,
      riskLevel: query.riskLevel,
      categories: query.categories?.sort().join(','),
      sdpcAgreementStatus: query.sdpcAgreementStatus,
      fromDate: query.fromDate?.toISOString(),
      toDate: query.toDate?.toISOString(),
      limit: query.limit,
      offset: query.offset,
      order: JSON.stringify(query.order),
    };
    
    const cachingKey = this.cacheService.genKey(JSON.stringify(cacheKeyParams));
    const cacheResult = await this.cacheService.get<{
      data: any;
      total: number;
      limit: number;
      offset: number;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    let enrichedData = await this.enrichmentService.enrich(
      data,
      [EnrichmentColumn.DOMAIN_METADATA, EnrichmentColumn.AGREEMENT],
      false,
      userOrganizationIds,
      undefined,
      undefined,
      query.ctx.sdpcDistrictId,
    );

    const agreements = enrichedData.map((d) => d.agreements).flat() || [];

    // Get all SDPC agreements for this district for status calculation
    const allSdpcAgreementsForStatus = query.ctx.sdpcDistrictId ? await this.sdpcAgreementModel.find({
      districtid: query.ctx.sdpcDistrictId,
    }).select('website date_expired date_approved') : [];

    const agreement_status = enrichedData.map((d) => {
      // Use the same data source as filtering logic
      const agreement = allSdpcAgreementsForStatus.find((dm) => {
        if (!dm?.website || !d.domain) return false;
        return extractDomainForMatching(dm.website) === d.domain.toLowerCase();
      });
      if (agreement) {
        const today = new Date();
        const dateApproved = agreement.date_approved
          ? new Date(agreement.date_approved)
          : null;
        const dateExpired = agreement.date_expired
          ? new Date(agreement.date_expired)
          : null;

        if (dateApproved && dateExpired) {
          if (today >= dateApproved && today <= dateExpired) {
            return AgreementStatus.ACTIVE;
          } else {
            return AgreementStatus.EXPIRED;
          }
        } else if (dateApproved && !dateExpired) {
          return AgreementStatus.ACTIVE;
        } else {
          return AgreementStatus.INACTIVE;
        }
      }
      return AgreementStatus.INACTIVE;
    });

    enrichedData = enrichedData.map((d, index) => {
      const agreement = agreements.find((dm) => {
        if (!dm?.website || !d.domain) return false;
        return extractDomainForMatching(dm.website) === d.domain.toLowerCase();
      });
      return {
        ...d,
        agreement_details: agreement || null,
        agreement_status: agreement_status[index],
      };
    });

    // adjust screen time
    enrichedData = enrichedData.map((d: any) => {
      if (d.avg_screen_time) {
        if (query.userGroup === UserGroup.BOTH) {
          d.avg_screen_time = +d.active_users > 0 ? d.avg_screen_time / d.active_users : 0;
        } else if (query.userGroup === UserGroup.TEACHER) {
          d.avg_screen_time = +d.active_users_teacher > 0 ? d.avg_screen_time / d.active_users_teacher : 0;
        } else if (query.userGroup === UserGroup.STUDENT) {
          d.avg_screen_time = +d.active_users_student > 0 ? d.avg_screen_time / d.active_users_student : 0;
        }
      }
      return d;
    });

    await this.cacheService.set(
      cachingKey,
      {
        data: enrichedData,
        total: rows_before_limit_at_least,
        limit: query.limit,
        offset: query.offset,
      },
      6 * 60 * 60,
    );

    return {
      data: enrichedData,
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
