import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import {
  <PERSON>umn,
  Filter,
  GetDataQuery,
  Join,
} from '@goteacher/app/analytics/query/data/get-data/query';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import {
  Contract,
  OrganizationDomainMetadata,
  PaymentStatus,
} from '@goteacher/app/models/mongo';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { PaginationResponse, parseOrderBy } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel as InjectModelMongoose } from '@nestjs/mongoose';
import { InjectModel } from '@nestjs/sequelize';
import { uniqBy } from 'lodash';
import { Model } from 'mongoose';

@QueryHandler(GetDataQuery)
export class GetDataQueryHandler implements IQueryHandler<GetDataQuery> {
  private readonly logger = new Logger(GetDataQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(School) private readonly schoolModel: typeof School,
    @InjectModelMongoose(Contract.name)
    private readonly contractModel: Model<Contract>,
    @InjectModelMongoose(OrganizationDomainMetadata.name)
    private readonly metadataModel: Model<OrganizationDomainMetadata>,
  ) {}

  async execute(query: GetDataQuery) {
    const sqlQuery = await this.getQuery(query);
    this.logger.debug(sqlQuery);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult =
      await this.cacheService.get<PaginationResponse<unknown>>(cachingKey);

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    const enrichedData = await this.enrichmentService.enrich(
      data,
      (query?.enrichment
        ?.map((e) => e.enrichColumns)
        .flat(1) as EnrichmentColumn[]) ?? [],
    );

    const response: PaginationResponse<unknown> = {
      data: enrichedData as unknown[],
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  initializeDates(query: GetDataQuery) {
    const today = new Date();

    const year = today.getFullYear();
    const month = today.getMonth();
    const day = today.getDate();

    // const startDate = new Date(year, month, day, 0, 1, 0, 0);
    // const endDate = new Date(year, month, day, 23, 59, 0, 0);
    const fromDate = query.fromDate;
    const toDate = query.toDate;

    return { fromDate, toDate };
  }

  sanitizeFilters(query: GetDataQuery) {
    return uniqBy(query.filters, (f) => f.column);
  }

  async getQuery(query: GetDataQuery) {
    const filters = await this.parseFilters(query);

    const selectClause = this.getSelectClause(query);
    const orderByClause = this.getOrderByClause(query);
    const sourceSubQuery = this.getSourceSubquery(query, filters);
    const joinedSubQueries = this.getJoinedSubqueries(query, filters);

    const sql = `
      SELECT ${selectClause} 
      FROM (${sourceSubQuery}) source_table 
      ${joinedSubQueries} 
      ${orderByClause} 
      LIMIT ${query.limit} 
      OFFSET ${query.offset}
    `;
    return sql;
  }

  getSelectClause(query: GetDataQuery) {
    const sourceColumns = query.columns.map(
      (col) => `source_table.${col.column}`,
    );
    const joinColumns = (query.join ?? [])
      .map((join, index) =>
        join.columns
          .filter((col) => !!col.aggFunction)
          .map((col) => `joined_table_${index}.${col.column}`),
      )
      .flat();

    return [...sourceColumns, ...joinColumns].join(', ');
  }

  getOrderByClause(query: GetDataQuery) {
    if (!query.order) return '';
    const orderBy = parseOrderBy(query.order);
    return `ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')}`;
  }

  async parseFilters(query: GetDataQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );
    let sanitizedFilters = this.sanitizeFilters(query);

    const { fromDate, toDate } = await this.initializeDates(query);
    if (fromDate) {
      sanitizedFilters.push({
        column: 'day',
        value: [Math.round(fromDate.getTime() / 1000)],
        type: '>=',
      });
    }

    if (toDate) {
      sanitizedFilters.push({
        column: 'day',
        value: [Math.round(toDate.getTime() / 1000)],
        type: '<=',
      });
    }

    sanitizedFilters.push({
      column: 'orgId',
      value: userOrganizationIds,
      type: 'in',
    });

    const schoolFilter = sanitizedFilters.find((f) => f.column === 'schoolId');
    let schoolIds = schoolFilter?.value ?? [];
    if (schoolIds.length) {
      sanitizedFilters.push({
        column: 'schoolId',
        value: schoolIds,
        type: 'in',
      });
    }

    sanitizedFilters = await this.handleExtraFilters(query, sanitizedFilters);

    return sanitizedFilters;
  }

  async handleExtraFilters(
    query: GetDataQuery,
    sanitizedFilters: Filter[],
  ): Promise<Filter[]> {
    const customFilters = query.filters.filter((f) =>
      [
        'paymentStatus',
        'dataPrivacy',
        'approvalStatus',
        'districtRecommended',
      ].includes(f.column),
    );
    if (!customFilters.length) return sanitizedFilters;

    sanitizedFilters = sanitizedFilters.filter(
      (f) =>
        ![
          'paymentStatus',
          'dataPrivacy',
          'approvalStatus',
          'districtRecommended',
        ].includes(f.column),
    );

    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    let applyInFilter = false;
    let applyNotInFilter = false;
    let inDomains: string[] = [];
    let notInDomains: string[] = [];
    let whereClause: any = {
      organizationId: {
        $in: userOrganizationIds,
      },
    };
    const dataPrivacyFilter = customFilters.find(
      (f) => f.column === 'dataPrivacy',
    );
    const districtRecommendedFilter = customFilters.find(
      (f) => f.column === 'districtRecommended',
    );
    const approvalStatusFilter = customFilters.find(
      (f) => f.column === 'approvalStatus',
    );
    const paymentStatusFilter = customFilters.find(
      (f) => f.column === 'paymentStatus',
    );

    const isFilteredByMetadata =
      !!dataPrivacyFilter ||
      !!districtRecommendedFilter ||
      !!approvalStatusFilter;
    const isFilteredByPayment = !!paymentStatusFilter;

    if (dataPrivacyFilter) {
      whereClause = {
        ...whereClause,
        dataPrivacy: dataPrivacyFilter.value,
      };
    }

    if (districtRecommendedFilter) {
      whereClause = {
        ...whereClause,
        districtRecommended: districtRecommendedFilter.value,
      };
    }

    if (approvalStatusFilter) {
      whereClause = {
        ...whereClause,
        approvalStatus: approvalStatusFilter.value,
      };
    }

    if (isFilteredByMetadata) {
      const metadataDomains = await this.metadataModel
        .find(whereClause)
        .select('domain');
      inDomains = metadataDomains.map((d) => d.domain);
      applyInFilter = true;
    }

    if (isFilteredByPayment) {
      const isPaid =
        (paymentStatusFilter.value[0] as unknown as PaymentStatus) ==
        PaymentStatus.PAID;
      const isFree =
        (paymentStatusFilter.value[0] as unknown as PaymentStatus) ==
        PaymentStatus.FREE;

      const contractsDomains = await this.contractModel
        .find({
          organizationId: {
            $in: userOrganizationIds,
          },
          active: true,
          deleted: false,
          subscriptionStartDate: { $lte: new Date() },
          subscriptionEndDate: { $gte: new Date() },
        })
        .select('domain');

      if (isPaid && !isFilteredByMetadata) {
        inDomains = contractsDomains.map((d) => d.domain);
        applyInFilter = true;
      } else if (isPaid && isFilteredByMetadata) {
        inDomains = inDomains.filter((d) =>
          contractsDomains.some((c) => c.domain === d),
        );
        applyInFilter = true;
      } else if (isFree) {
        notInDomains = contractsDomains.map((d) => d.domain);
        applyNotInFilter = true;
      }
    }

    if (applyInFilter)
      sanitizedFilters.push({
        column: 'domain',
        value: inDomains,
        type: 'in',
      });
    if (applyNotInFilter)
      sanitizedFilters.push({
        column: 'domain',
        value: notInDomains,
        type: 'not in',
      });

    return sanitizedFilters;
  }

  getSourceSubquery(query: GetDataQuery, filters: Filter[]) {
    const selectClause = query.columns
      .map((col) => this.getColumnName(col))
      .join(', ');
    const groupByClause = this.getGroupByClause(query);
    const groupByClauseSql = groupByClause ? ` group by ${groupByClause}` : '';

    return `SELECT ${selectClause} FROM ${this.getTableName(query)} WHERE ${this.getWhereClause(filters)} ${groupByClauseSql}`;
  }

  getColumnName = (request: Column) =>
    request.aggFunction
      ? `${request.aggFunction}(${request.column}) as ${request.column}`
      : request.column;

  getGroupByClause = (query: GetDataQuery) => {
    if (!query.columns.some((metric) => metric.aggFunction)) return '';
    const groupByMetrics = query.columns
      .filter((metric) => !metric.aggFunction)
      .map((metric) => metric.column);
    return groupByMetrics.length ? `${groupByMetrics.join(',')}` : '';
  };

  getTableName = (query: GetDataQuery) =>
    `${query.timeGranularity}_${query.entityType}`;

  getWhereClause = (filters: Filter[]) => {
    return filters
      .map((filter) => {
        const compareType = filter.type ?? 'in';
        const statementBuilderFunc = (cType: string) => {
          switch (cType) {
            case 'in':
              return `${filter.column} in (${filter.value.map((v) => `${this.stringifyParameter(v)}`).join(',')})`;
            case '=':
              return `${filter.column} = ${this.stringifyParameter(filter.value[0])}`;
            case '<=':
              return `${filter.column} <= ${this.stringifyParameter(filter.value[0])}`;
            case '>=':
              return `${filter.column} >= ${this.stringifyParameter(filter.value[0])}`;
            case '<':
              return `${filter.column} < ${this.stringifyParameter(filter.value[0])}`;
            case '>':
              return `${filter.column} > ${this.stringifyParameter(filter.value[0])}`;
            case 'like':
              return `${filter.column} ilike ${this.stringifyParameter(filter.value[0], true)}`;
            case 'not in':
              return `${filter.column} not in (${filter.value.map((v) => `${this.stringifyParameter(v)}`).join(',')})`;
            case '!=':
              return `${filter.column} != ${this.stringifyParameter(filter.value[0])}`;
          }
        };
        if (filter.value.length) return statementBuilderFunc(compareType);
        else return `${filter.column} ${compareType} (null)`;
      })
      .join(' and ');
  };

  private stringifyParameter = (
    parameter: unknown,
    withLike: boolean = false,
  ) => {
    if (typeof parameter === 'string') {
      return withLike ? `'%${parameter}%'` : `'${parameter}'`;
    }
    if (typeof parameter === 'number') {
      return `${parameter}`;
    }
  };

  getJoinedSubqueries = (query: GetDataQuery, filters: Filter[]) => {
    if (!query.join || !query.join.length) return '';

    return query.join.map((joinTable, index) =>
      this.getJoinSubquery(query, filters, joinTable, index),
    );
  };

  private getJoinSubquery = (
    query: GetDataQuery,
    filters: Filter[],
    joinTable: Join,
    index: number,
  ) => {
    const selectClause = joinTable.columns
      .map((col) => this.getColumnName(col))
      .join(', ');
    const groupByClause = this.getGroupByClause(query);
    const groupByClauseSql = groupByClause ? ` group by ${groupByClause}` : '';

    const onClause = joinTable.on
      .map(
        (onCol) => ` source_table.${onCol} = joined_table_${index}.${onCol} `,
      )
      .join(' AND ');

    const sql = `${joinTable.joinType} JOIN ( 
      SELECT ${selectClause} FROM ${joinTable.entityType} WHERE ${this.getWhereClause(filters)} ${groupByClauseSql} 
    ) joined_table_${index} ON ${onClause}`;
    return sql;
  };
}
