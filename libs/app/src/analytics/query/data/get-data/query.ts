import { EnrichmentColumn } from '@goteacher/app/analytics/service';
import { TimeGranularity } from '@goteacher/app/analytics/types';
import { ReqContext } from '@goteacher/app/auth';
import { PaginationRequest } from '@goteacher/app/utility';

export class GetDataQuery extends PaginationRequest {
  constructor(obj: GetDataQuery) {
    super(obj);
    Object.assign(this, obj);
  }

  ctx: ReqContext;
  fromDate: Date;
  toDate: Date;
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;
  entityType: string;
  forceRefresh: boolean;

  // BODY
  filters: Filter[];
  columns: Column[];
  join: Join[];
  enrichment: Enrichment[];
}

export class Filter {
  column: string;
  type?: string;
  value: unknown[];
}

export class Column {
  column: string;
  aggFunction?: string;
}

export class Join {
  entityType: string;
  joinType: string;
  columns: Column[];
  on: [string];
}

export class Enrichment {
  enrichColumns: EnrichmentColumn[];
}
