import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTopSchoolsByDomainQuery } from '@goteacher/app/analytics/query/data/get-top-schools-by-domain/query';
import { TopSchoolsByDomainResponse } from '@goteacher/app/analytics/query/data/get-top-schools-by-domain/response';
import { GetTopUsersQuery } from '@goteacher/app/analytics/query/data/get-top-users/query';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import {
  getAcademicYear,
  PaginationResponse,
  parseOrderBy
} from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetTopSchoolsByDomainQuery)
export class GetTopSchoolsByDomainQueryHandler
  implements IQueryHandler<GetTopSchoolsByDomainQuery> {
  private readonly logger = new Logger(GetTopSchoolsByDomainQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(School) private schoolModel: typeof School,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetTopSchoolsByDomainQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const searchableSchoolIds = await this.getSearchableSchoolIds(
      userOrganizationIds,
      query,
    );

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'active_users', orderDirection: 'desc' }],
    );

    const sqlQuery = `
      SELECT
        ${query.timeGranularity === TimeGranularity.DAILY ? `toStartOfDay(is.startTime) as day,` : ''}
        ${query.timeGranularity === TimeGranularity.WEEKLY ? `toStartOfWeek(is.startTime) as day,` : ''} 

        is.schoolId as schoolId,
        count (DISTINCT is.userId) as active_users,
        count (DISTINCT is.sessionId) as count_sessions,
        sum(is.duration) as sum_time_spent,
        avg(is.duration) as avg_session_duration,
        count (DISTINCT is.url) as page_views
      FROM goteacher.interaction_summaries as is
      WHERE         
          orgId = '${userOrganizationIds[0]}'        
          ${query.fromDate ? `AND is.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND is.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}                
          ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
          ${searchableSchoolIds.length ? `AND schoolId IN (${searchableSchoolIds.map((sui) => `'${sui}'`).join(',')})` : ''} 
      GROUP BY schoolId ${query.timeGranularity !== TimeGranularity.ALL ? ', day' : ''}
      ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')}     
      LIMIT ${query.limit} 
      OFFSET ${query.offset};
    `;

    this.logger.debug(sqlQuery);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult =
      await this.cacheService.get<
        PaginationResponse<TopSchoolsByDomainResponse>
      >(cachingKey);

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    // we want to add missing schools with 0 active users based on the query.schoolIds
    const fullSchoolsData = query.schoolIds ? query.schoolIds.map((schoolId) => {
      const schoolData = (data as any[]).find((d) => d.schoolId === schoolId);
      if (schoolData) return schoolData;
      return {
        schoolId,
        active_users: 0,
        active_users_student: 0,
        active_users_teacher: 0,
      };
    }) : data;

    const schoolYears = userOrganizationIds.includes(
      'cc5b397a-0e70-4bca-97f5-193ea80a2034',
    )
      ? [getAcademicYear(query.fromDate), getAcademicYear(query.toDate)]
      : undefined;
    const enrichedData = await this.enrichmentService.enrich(
      fullSchoolsData,
      [EnrichmentColumn.SCHOOL],
      true,
      userOrganizationIds,
      schoolYears,
      { role: query.userGroup, grades: query.grades },
    );

    const response: PaginationResponse<TopSchoolsByDomainResponse> = {
      data: enrichedData as TopSchoolsByDomainResponse[],
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  async getSearchableSchoolIds(
    orgIds: string[],
    query: GetTopUsersQuery,
  ): Promise<string[]> {
    if (!query.search) return [];

    const schoolIds = await this.schoolModel.findAll({
      where: {
        organisationId: {
          [Op.in]: orgIds,
        },
        name: {
          [Op.iLike]: `%${query.search}%`,
        },
        displayName: {
          [Op.iLike]: `%${query.search}%`,
        },
      },
      attributes: ['id'],
    });

    return schoolIds.map((u) => u.id);
  }

  getVisitsSQL(
    query: GetTopSchoolsByDomainQuery,
    orgIds: string[],
    searchableSchoolIds: string[],
  ): string {
    const orgId = orgIds[0];
    const domainOrProductCondition = query.productId
      ? `AND st.productId = '${query.productId}'`
      : `AND st.domain = '${query.domain}'`;

    const groupByField = query.productId ? 'productId' : 'domain';
    return `
      SELECT 
        st.sessionId AS sessionId,
        st.tabId AS tabId,
        st.userId AS userId,
        
        ${orgId === '7d872d49-0a41-40a1-96aa-f980ad5f025e' ? 'st.orgId' : 'COALESCE(u.orgId, st.orgId)'} AS orgId,
        ${orgId === '7d872d49-0a41-40a1-96aa-f980ad5f025e' ? 'st.schoolId' : 'COALESCE(u.schoolId, st.schoolId)'} AS schoolId,
        ${orgId === '7d872d49-0a41-40a1-96aa-f980ad5f025e' ? 'st.role' : 'COALESCE(u.role, st.role)'} AS role,
        ${orgId === '7d872d49-0a41-40a1-96aa-f980ad5f025e' ? 'st.grade' : 'COALESCE(u.grade, st.grade)'} AS grade,

        st.${groupByField} AS ${groupByField},
        st.url AS url,        
        min(st.visit_start) AS visit_start,
        max(st.visit_end) AS visit_end,
        toStartOfDay(min(st.visit_start)) AS day,
        toStartOfWeek(min(st.visit_start)) AS week,
        any(st.visit_timezone) AS visit_timezone,
        dateDiff('s', visit_start, visit_end) AS time_spent,
        if(
            (
                toHour(
                    addHours(visit_start, toInt32OrZero(visit_timezone))
                ) >= 7
            )
            AND (
                toHour(
                    addHours(visit_start, toInt32OrZero(visit_timezone))
                ) <= 14
            ),
            1,
            0
        ) AS in_class,

        count(DISTINCT st.url) as page_views
      FROM goteacher.interaction_summaries as is      
      WHERE         
        orgId = '${orgId}'
        ${domainOrProductCondition}
        ${query.fromDate ? `AND st.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
        ${query.toDate ? `AND st.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}                
        ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
        ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
        ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
        ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
        ${searchableSchoolIds.length ? `AND schoolId IN (${searchableSchoolIds.map((sui) => `'${sui}'`).join(',')})` : ''} 
      GROUP BY
          sessionId,
          tabId,
          userId,
          orgId,
          schoolId,
          role,
          grade,
          ${groupByField},
          url                         
     `;
  }

  getDailySessionsSQL(query: GetTopSchoolsByDomainQuery): string {
    return `
      SELECT 
        schoolId,
        orgId,
        grade,        
        ${query.productId ? 'productId,' : 'domain,'}
        ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'day,' : 'toStartOfWeek(day) AS day,'} 
        
        uniqState(userId) as new_active_users,        
        
        uniqState(sessionId) as count_sessions,
        sum(time_spent) as sum_time_spent,

        sum(page_views) as page_views
    FROM visits_info_cte
    GROUP BY        
        schoolId,
        orgId,
        grade,        
        ${query.timeGranularity === 'all' ? '' : 'day,'}
        ${query.productId ? 'productId' : 'domain'}
    `;
  }
}
