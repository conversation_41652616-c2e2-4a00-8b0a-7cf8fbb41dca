import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTopUsersCsvQuery } from '@goteacher/app/analytics/query/data/get-top-users-csv/query';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { isMoreThanTwoWeeks, parseOrderBy } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger, NotFoundException } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { stringify } from 'csv-stringify/sync';
import { Op } from 'sequelize';

@QueryHandler(GetTopUsersCsvQuery)
export class GetTopUsersCsvQueryHandler
  implements IQueryHandler<GetTopUsersCsvQuery> {
  private readonly logger = new Logger(GetTopUsersCsvQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(School) private schoolModel: typeof School,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetTopUsersCsvQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const searchableUserIds = await this.getSearchableUserIds(
      userOrganizationIds,
      query,
    );

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    const shouldUseWeekly = query.fromDate
      ? isMoreThanTwoWeeks(query.fromDate, query.toDate)
      : false;

    // let process in batches
    const BATCH_SIZE = 1000;
    const MAX_USERS = 100000; // this will be a safety limit for the max users to process - better to have a top limit
    let offset = 0;
    let allCsvRows = [];
    let hasMore = true;

    const sqlQuery = `
      WITH daily_user_activity AS (
        SELECT
          ism.userId as userId,
          ${query.productId ? `ism.productId AS productId,` : 'ism.domain AS domain,'}
          toStartOfDay(ism.startTime) AS day,
          sum(ism.duration) as daily_total_seconds,
          uniqState(ism.sessionId) as session_state
        FROM goteacher.interaction_summaries ism
        WHERE
          ism.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND ism.eventName = 'INTERACTION_SUMMARY'
          ${query.productId ? `AND ism.productId = '${query.productId}'` : `AND ism.domain = '${query.domain}'`}
          ${query.schoolIds?.length ? `AND ism.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND ism.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND ism.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND ism.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${searchableUserIds.length ? `AND ism.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND ism.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND ism.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) > 7 AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) < 14` : ''}
        GROUP BY userId, ${query.productId ? `productId,` : 'domain,'} day
      ),
      user_median_cte AS (
        SELECT
          userId,
          ${query.productId ? `productId,` : 'domain,'}
          quantileExact(0.5)(daily_total_seconds) as median_daily_seconds
        FROM daily_user_activity
        GROUP BY userId, ${query.productId ? `productId` : 'domain'}
      ),
      activity_sessions_summary_cte AS (
        SELECT
          userId,
          ${query.productId ? `productId,` : 'domain,'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'day,' : 'toStartOfWeek(day) AS day,'}
          sum(daily_total_seconds) as sum_duration,
          uniqMerge(session_state) as count_sessions
        FROM daily_user_activity
        GROUP BY
          userId,
          ${query.productId ? `productId` : 'domain'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT 
        ${query.timeGranularity !== TimeGranularity.ALL ? `ass.day as day,` : ''}
        ass.userId as userId, 
        metrics.page_views as page_views,
        ass.count_sessions as count_sessions,
        COALESCE(umc.median_daily_seconds, 0) as median_daily_time_spent,
        ass.sum_duration as sum_time_spent
      FROM activity_sessions_summary_cte ass
      LEFT JOIN user_median_cte umc ON ass.userId = umc.userId AND ${query.productId ? 'ass.productId = umc.productId' : 'ass.domain = umc.domain'}     
      LEFT JOIN 
        (
          SELECT 
            ${query.timeGranularity === TimeGranularity.DAILY ? `admps.day as day,` : ''}
            ${query.timeGranularity === TimeGranularity.WEEKLY ? `toStartOfWeek(admps.day) as day,` : ''}            
            admps.userId as userId,
            countMergeOrDefault(page_views) as page_views
          FROM 
            ${shouldAggregateAll ? (query.productId ? 'all_product_metrics_per_user_sc' : 'all_domain_metrics_per_user_sc') : shouldUseWeekly ? (query.productId ? 'weekly_product_metrics_per_user_sc' : 'weekly_domain_metrics_per_user_sc') : query.productId ? 'daily_product_metrics_per_user_sc' : 'daily_domain_metrics_per_user_sc'} admps         
          WHERE 
            ${query.productId ? `admps.productId = '${query.productId}'` : `admps.domain = '${query.domain}'`} 
            AND admps.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            ${query.schoolIds?.length
        ? `AND admps.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})`
        : ''
      }
            ${query.fromDate ? `AND admps.day >= toDateTime(${Math.round(query.fromDate.getTime() / 1000)})` : ''}
            ${query.toDate ? `AND admps.day <= toDateTime(${Math.round(query.toDate.getTime() / 1000)})` : ''}
          GROUP BY admps.userId ${query.timeGranularity !== TimeGranularity.ALL ? ', day' : ''}
        ) metrics ON ass.userId = metrics.userId ${query.timeGranularity !== TimeGranularity.ALL ? 'AND ass.day = metrics.day' : ''}    
        ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
       `;

    while (hasMore && offset < MAX_USERS) {
      const batchQuery = `${sqlQuery} LIMIT ${BATCH_SIZE} OFFSET ${offset}`;
      
      this.logger.log(`Processing batch: offset=${offset}, limit=${BATCH_SIZE}`);
      
      try {
        const queryResult = await this.clickhouse.query({
          query: batchQuery,
          format: 'JSON',
        });
        const { data } = await queryResult.json();

        if (data.length === 0) {
          hasMore = false;
          break;
        }

        const enrichedData = await this.enrichmentService.enrich(data, [
          EnrichmentColumn.USER,
        ]);

        const csvRows = enrichedData
          .map((row) => {
            const { user_details, userId, ...rest } = row;
            if (!user_details) {
              return null;
            }
            return {
              Email: user_details.email,
              'First Name': user_details.firstName,
              'Last Name': user_details.lastName,
              School: user_details.schoolName,
              Grade: user_details.grade,
              Role: user_details.role,
              ...rest,
            };
          })
          .filter((r) => r !== null);

        allCsvRows = allCsvRows.concat(csvRows);
        
        if (data.length < BATCH_SIZE) {
          hasMore = false;
        }
        
        offset += BATCH_SIZE;
      } catch (error) {
        this.logger.error(`Error processing batch at offset ${offset}:`, error);
        throw error;
      }
    }

    if (allCsvRows.length === 0) {
      throw new NotFoundException('No users found!');
    }

    this.logger.log(`Total users processed: ${allCsvRows.length}`);

    const csvContent = stringify(allCsvRows, { header: true });

    return Buffer.from(csvContent);
  }

  async getSearchableUserIds(
    orgIds: string[],
    query: GetTopUsersCsvQuery,
  ): Promise<string[]> {
    if (!query.search) return [];

    const schoolIds = await this.schoolModel.findAll({
      where: {
        organisationId: {
          [Op.in]: orgIds,
        },
      },
      attributes: ['id'],
    });

    const userIds = await this.userModel.findAll({
      where: {
        [Op.or]: [
          {
            email: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            firstName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            lastName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
        ],
      },
      attributes: ['id'],
      include: [
        {
          model: UserSchool,
          where: {
            schoolId: {
              [Op.in]: query.schoolIds?.length
                ? query.schoolIds
                : schoolIds.map((s) => s.id),
            },
          },
          include: [
            {
              model: School,
              where: {
                organisationId: {
                  [Op.in]: orgIds,
                },
              },
            },
          ],
        },
      ],
    });

    return userIds.map((u) => u.id);
  }

}
