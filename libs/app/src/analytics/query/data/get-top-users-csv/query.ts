import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { ReqContext } from '@goteacher/app/auth';
import { OrderByRequest } from '@goteacher/app/utility';

export class GetTopUsersCsvQuery {
  constructor(obj: GetTopUsersCsvQuery) {
    Object.assign(this, obj);
  }

  domain?: string;

  productId?: string;

  fromDate: Date;
  toDate: Date;

  search?: string;
  userGroup?: UserGroup = UserGroup.BOTH;
  timeGranularity?: TimeGranularity = TimeGranularity.WEEKLY;
  schoolIds?: string[];
  grades?: string[];

  ctx: ReqContext;

  forceRefresh?: boolean;
  order?: OrderByRequest[];
}
