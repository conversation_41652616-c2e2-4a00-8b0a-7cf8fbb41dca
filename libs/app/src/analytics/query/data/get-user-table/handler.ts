import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetUserTableQuery } from '@goteacher/app/analytics/query/data/get-user-table/query';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import {
  PaginationResponse,
  parseOrderBy
} from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQuery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetUserTableQuery)
export class GetUserTableQueryHandler
  implements IQueryHandler<GetUserTableQuery> {
  private readonly logger = new Logger(GetUserTableQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(School) private schoolModel: typeof School,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetUserTableQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const searchableUserIds = await this.getSearchableUserIds(
      userOrganizationIds,
      query,
    );

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    console.log('dateDiffInDays', dateDiffInDays);

    const sqlQuery = `
      WITH daily_user_activity AS (
        SELECT
          ism.userId as userId,
          ${query.productId ? 'ism.productId,' : ''}
          ${query.domain ? 'ism.domain,' : ''}
          toStartOfDay(ism.startTime) as activity_day,
          sum(ism.duration) as daily_total_seconds,
          uniq(ism.sessionId) as session_count,
          uniq(ism.userId) as active_users_count,
          count(*) as daily_page_views
        FROM goteacher.interaction_summaries ism
        WHERE
          ism.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND ism.eventName = 'INTERACTION_SUMMARY'
          ${query.productId ? `AND ism.productId = '${query.productId}'` : ''}
          ${query.domain ? `AND ism.domain = '${query.domain}'` : ''}
          ${query.schoolIds?.length ? `AND ism.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND ism.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND ism.startTime >= ${Math.floor(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND ism.startTime <= ${Math.floor(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND ism.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND ism.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) > 7 AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) < 15` : ''}
          ${searchableUserIds.length ? `AND ism.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''}
        GROUP BY
          ism.userId,
          activity_day
          ${query.productId ? `,ism.productId` : ''}
          ${query.domain ? `,ism.domain` : ''}
      ),
      daily_idle_activity AS (
        SELECT
          ism.userId as userId,
          ${query.productId ? 'ism.productId,' : ''}
          ${query.domain ? 'ism.domain,' : ''}
          toStartOfDay(ism.startTime) as activity_day,
          sum(ism.duration) as daily_idle_seconds,
          count(*) as idle_ping_count
        FROM goteacher.interaction_summaries ism
        WHERE
          ism.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND ism.eventName = 'IDLE_PING'
          ${query.productId ? `AND ism.productId = '${query.productId}'` : ''}
          ${query.domain ? `AND ism.domain = '${query.domain}'` : ''}
          ${query.schoolIds?.length ? `AND ism.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND ism.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND ism.startTime >= ${Math.floor(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND ism.startTime <= ${Math.floor(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND ism.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND ism.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) > 7 AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) < 15` : ''}
          ${searchableUserIds.length ? `AND ism.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''}
        GROUP BY
          ism.userId,
          activity_day
          ${query.productId ? `,ism.productId` : ''}
          ${query.domain ? `,ism.domain` : ''}
      ),
      user_median_daily_cte AS (
        SELECT
          dua.userId as userId,
          quantileExact(0.5)(dua.daily_total_seconds) AS median_daily_seconds_for_user
        FROM daily_user_activity dua
        WHERE dua.daily_total_seconds > 0
        GROUP BY
          dua.userId
      ),
      activity_sessions_summary_cte AS (
        SELECT
          userId,
          ${query.productId ? 'productId,' : ''}
          ${query.domain ? 'domain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'activity_day AS day,' : 'toStartOfWeek(activity_day) AS day,'}
          sum(daily_total_seconds) as sum_duration,
          sum(session_count) as total_sessions,
          uniq(userId) as total_active_users,
          sum(daily_page_views) as sum_page_views
        FROM daily_user_activity
        GROUP BY
          userId
          ${query.productId ? `,productId` : ''}
          ${query.domain ? `,domain` : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      ),
      idle_sessions_summary_cte AS (
        SELECT
          userId,
          ${query.productId ? 'productId,' : ''}
          ${query.domain ? 'domain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'activity_day AS day,' : 'toStartOfWeek(activity_day) AS day,'}
          sum(daily_idle_seconds) as sum_idle_duration,
          sum(idle_ping_count) as total_idle_pings
        FROM daily_idle_activity
        GROUP BY
          userId
          ${query.productId ? `,productId` : ''}
          ${query.domain ? `,domain` : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT
        ass.userId as userId,
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'ass.day as day,'}  
        ${query.productId ? 'ass.productId as productId,' : ''}        
        ${query.domain ? 'ass.domain as domain,' : ''}
        (COALESCE(umd.median_daily_seconds_for_user, 0)) as median_screen_time,
        sum(ass.sum_duration) as sum_time_spent,
        sum(ass.sum_duration) / sum(ass.total_sessions) as avg_session_duration,
        sum(ass.sum_duration) / sum(ass.total_sessions) as avg_time_spent,
        sum(ass.total_sessions) as count_sessions,
        sum(ass.sum_page_views) as page_views,
        sum(ass.total_active_users) as active_users,
        if(sum_time_spent < 60, 'inactive', 'active') as user_status,
        sum(COALESCE(iss.sum_idle_duration, 0)) as total_idle_time,
        sum(COALESCE(iss.total_idle_pings, 0)) as idle_ping_count,

        ${dateDiffInDays > 14
        ? `(COALESCE(umd.median_daily_seconds_for_user, 0)) * 7 as avg_screen_time`
        : `(COALESCE(umd.median_daily_seconds_for_user, 0)) as avg_screen_time`
      }
      FROM activity_sessions_summary_cte as ass
      LEFT JOIN user_median_daily_cte umd ON ass.userId = umd.userId
      LEFT JOIN idle_sessions_summary_cte iss ON ass.userId = iss.userId
        ${query.productId ? 'AND ass.productId = iss.productId' : ''}
        ${query.domain ? 'AND ass.domain = iss.domain' : ''}
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'AND ass.day = iss.day'}
      GROUP BY
        userId,
        median_screen_time
        ${query.productId ? `, productId` : ''}
        ${query.domain ? `, domain` : ''}
        ${query.timeGranularity === TimeGranularity.ALL ? '' : `, day`}
      ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
      LIMIT ${query.limit} 
      OFFSET ${query.offset};
    `;

    this.logger.debug(sqlQuery);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult =
      await this.cacheService.get<PaginationResponse<any>>(cachingKey);

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    const enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.USER,
    ]);

    const response: PaginationResponse<any> = {
      data: enrichedData,
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  async getSearchableUserIds(
    orgIds: string[],
    query: GetUserTableQuery,
  ): Promise<string[]> {
    if (!query.search) return [];

    const schoolIds = await this.schoolModel.findAll({
      where: {
        organisationId: {
          [Op.in]: orgIds,
        },
      },
      attributes: ['id'],
    });

    const userIds = await this.userModel.findAll({
      where: {
        [Op.or]: [
          {
            email: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            firstName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            lastName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
        ],
      },
      attributes: ['id'],
      include: [
        {
          model: UserSchool,
          where: {
            schoolId: {
              [Op.in]: query.schoolIds?.length
                ? query.schoolIds
                : schoolIds.map((s) => s.id),
            },
          },
          include: [
            {
              model: School,
              where: {
                organisationId: {
                  [Op.in]: orgIds,
                },
              },
            },
          ],
        },
      ],
    });

    return userIds.map((u) => u.id);
  }

}
