import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { I<PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetSessionDurationsQuery } from '@goteacher/app/analytics/query/data/get-sessions-duration/query';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

export interface SessionsDuration {
  day?: Date | string;
  count_sessions_5: number;
  count_sessions_10: number;
  count_sessions_20: number;
  count_sessions_20_plus: number;
}

@QueryHandler(GetSessionDurationsQuery)
export class GetSessionDurationsQueryHandler
  implements IQueryHandler<GetSessionDurationsQuery> {
  private readonly logger = new Logger(GetSessionDurationsQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetSessionDurationsQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const sqlQuery = `
      WITH session_totals AS (
        SELECT
          sessionId,
          ${query.timeGranularity !== TimeGranularity.ALL ? query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(startTime) AS day,' : 'toStartOfWeek(startTime) AS day,' : ''}
          sum(duration) as total_duration
        FROM goteacher.interaction_summaries
        WHERE
          orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND eventName = 'INTERACTION_SUMMARY'
          ${query.productId ? `AND productId = '${query.productId}'` : `AND domain = '${query.domain}'`}
          ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 14` : ''}
        GROUP BY sessionId ${query.timeGranularity !== TimeGranularity.ALL ? ', day' : ''}
      )
      SELECT
        ${query.timeGranularity !== TimeGranularity.ALL ? 'day,' : ''}
        countIf(total_duration <= 300) AS count_sessions_5,
        countIf(total_duration > 300 AND total_duration <= 600) AS count_sessions_10,
        countIf(total_duration > 600 AND total_duration <= 1200) AS count_sessions_20,
        countIf(total_duration > 1200) AS count_sessions_20_plus
      FROM session_totals
      ${query.timeGranularity !== TimeGranularity.ALL ? `GROUP BY day` : ''}
      ${query.timeGranularity !== TimeGranularity.ALL ? `ORDER BY day ASCENDING` : ''}
      ;
    `;

    this.logger.debug(`sqlQuery: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: SessionsDuration[];
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);

    return { data: data };
  }
}
