import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { GetUserSessionsQuery } from './query';

@QueryHandler(GetUserSessionsQuery)
export class GetUserSessionsHandler
  implements IQueryHandler<GetUserSessionsQuery> {
  private readonly logger = new Logger(GetUserSessionsHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetUserSessionsQuery): Promise<any> {
    const userOrganizationId = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    )[0];

    const sqlQuery = `
      WITH raw AS
      (
        SELECT
            *,        
            lagInFrame(domain) OVER w AS prev_dom,
            lagInFrame(endTime) OVER w AS prev_end
        FROM goteacher.interaction_summaries
        WHERE userId = '${query.userId}'
          AND eventName = 'INTERACTION_SUMMARY'
          AND startTime >= ${Math.round(query.fromDate.getTime() / 1000)}
          AND startTime <= ${Math.round(query.toDate.getTime() / 1000)}
          AND duration > 0
          ${userOrganizationId === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 15` : ''}
        WINDOW w AS (PARTITION BY userId ORDER BY startTime)
      ), flagged AS
      (
        SELECT
            *,            
            if(
                domain != prev_dom OR dateDiff('second', prev_end, startTime) > 60,
                1, 0
            ) AS is_new,
            sum(is_new) OVER w2 AS session_no
        FROM raw
        WINDOW w2 AS
            (PARTITION BY userId ORDER BY startTime
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)
      )
      SELECT    
        -- session_no,        
        domain,
        min(addHours(startTime, toInt32OrZero(clientTimezone))) AS session_start,
        max(addHours(endTime, toInt32OrZero(clientTimezone)))   AS session_end,
        sum(duration)                                            AS duration
      FROM flagged
      GROUP BY
          domain, session_no
      ORDER BY
          session_start;
    `;

    this.logger.debug(`sqlQuery get-user-sessions: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);

    return { data };
  }
}
