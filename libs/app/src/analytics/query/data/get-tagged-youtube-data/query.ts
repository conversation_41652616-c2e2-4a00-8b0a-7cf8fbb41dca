import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';

import { ReqContext } from '@goteacher/app/auth';
import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';
import { PaginationRequest } from '@goteacher/app/utility';

abstract class PaginatedDateRangeQuery
  extends PaginationRequest
  implements BaseDateRangeQuery
{
  fromDate?: Date;
  toDate?: Date;
}

export class GetTaggedYoutubeDataQuery extends PaginatedDateRangeQuery {
  constructor(obj: GetTaggedYoutubeDataQuery) {
    super(obj);

    Object.assign(this, obj);
  }

  ctx: ReqContext;
  forceRefresh?: boolean;

  fromDate?: Date;
  toDate?: Date;

  timeGranularity?: TimeGranularity = TimeGranularity.WEEKLY;
  userGroup?: UserGroup = UserGroup.BOTH;
  schoolIds?: string[];
  subjects?: string[];
  topics?: string[];
  grades?: string[];
  search?: string;

  url?: string;
}
