import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTaggedYoutubeDataQuery } from '@goteacher/app/analytics/query/data/get-tagged-youtube-data/query';
import { TimeGranularity } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { TaggedYoutubeVideo } from '@goteacher/app/models/mongo';
import { isMoreThanTwoWeeks, parseOrderBy } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(GetTaggedYoutubeDataQuery)
export class GetTaggedYoutubeDataQueryHandler
  implements IQueryHandler<GetTaggedYoutubeDataQuery>
{
  private readonly logger = new Logger(GetTaggedYoutubeDataQueryHandler.name);

  constructor(
    @InjectModel(TaggedYoutubeVideo.name)
    private readonly taggedYoutubeVideoModel: Model<TaggedYoutubeVideo>,
    private clickhouse: NodeClickHouseClient,
    private cacheService: ICacheService,
  ) {}

  @NormalizeDateRange()
  async execute(query: GetTaggedYoutubeDataQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );
    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    const shouldUseWeekly = query.fromDate
      ? isMoreThanTwoWeeks(query.fromDate, query.toDate)
      : false;

    const tableToBeUsed = shouldAggregateAll
      ? 'all_url_metrics_sc'
      : query.timeGranularity === TimeGranularity.DAILY
        ? 'daily_url_metrics_sc'
        : shouldUseWeekly
          ? 'weekly_url_metrics_sc'
          : 'daily_url_metrics_sc';

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    let taggedYoutubeData;
    let totalMongoData;
    if (query.url) {
      taggedYoutubeData = await this.taggedYoutubeVideoModel.find({
        url: query.url,
      });
    } else {
      this.logger.debug(`subjects: ${JSON.stringify(query.subjects)}`);
      this.logger.debug(`topics: ${JSON.stringify(query.topics)}`);
      const filter: any = {};
      if (query.topics?.length) {
        filter.topic = { $in: query.topics };
      }
      if (query.subjects?.length) {
        filter.subject = { $in: query.subjects };
      }

      taggedYoutubeData = await this.taggedYoutubeVideoModel
        .find(filter)
        .limit(query.limit)
        .skip(query.offset);

      totalMongoData = await this.taggedYoutubeVideoModel.countDocuments();
    }

    const urls = taggedYoutubeData.map((video) => video.url);

    const sqlQuery = `
    WITH visits_info_cte as (
      ${this.getVisitsSQL(query, userOrganizationIds)}
    ), daily_sessions_cte AS (
      SELECT 
      userId,
      schoolId,
      orgId,
      grade,
      url,
      domain,
      ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'day,' : 'toStartOfWeek(day) AS day,'} 
      avgOrNull(time_spent) AS avg_time_spent,
      avgIfOrNull(time_spent, role = 'student') AS avg_time_spent_student,
      avgIfOrNull(time_spent, role = 'teacher') AS avg_time_spent_teacher,
      avgIfOrNull(time_spent, role = 'administrator') AS avg_time_spent_administrator,
      sumOrDefault(time_spent) AS sum_time_spent,
      sumIfOrDefault(time_spent, role = 'student') AS sum_time_spent_student,
      sumIfOrDefault(time_spent, role = 'teacher') AS sum_time_spent_teacher,
      sumIfOrDefault(time_spent, role = 'administrator') AS sum_time_spent_administrator,
      uniqState(sessionId) AS count_sessions,
      uniqStateIf(sessionId, role = 'student') AS count_sessions_student,
      uniqStateIf(sessionId, role = 'teacher') AS count_sessions_teacher,
      uniqStateIf(sessionId, role = 'administrator') AS count_sessions_administrator,
      avgIfOrNull(time_spent, in_class) AS avg_time_spent_in_class,
      avgIfOrNull(
        time_spent,
        in_class
        AND (role = 'student')
      ) AS avg_time_spent_in_class_student,
      avgIfOrNull(
        time_spent,
        in_class
        AND (role = 'teacher')
      ) AS avg_time_spent_in_class_teacher,
      avgIfOrNull(
        time_spent,
        in_class
        AND (role = 'administrator')
      ) AS avg_time_spent_in_class_administrator,
      sumIfOrDefault(time_spent, in_class) AS sum_time_spent_in_class,
      sumIfOrDefault(
        time_spent,
        in_class
        AND (role = 'student')
      ) AS sum_time_spent_in_class_student,
      sumIfOrDefault(
        time_spent,
        in_class
        AND (role = 'teacher')
      ) AS sum_time_spent_in_class_teacher,
      sumIfOrDefault(
        time_spent,
        in_class
        AND (role = 'administrator')
      ) AS sum_time_spent_in_class_administrator,
      uniqStateIf(sessionId, in_class) AS count_sessions_in_class,
      uniqStateIf(
        sessionId,
        in_class
        AND (role = 'student')
      ) AS count_sessions_in_class_student,
      uniqStateIf(
        sessionId,
        in_class
        AND (role = 'teacher')
      ) AS count_sessions_in_class_teacher,
      uniqStateIf(
        sessionId,
        in_class
        AND (role = 'administrator')
      ) AS count_sessions_in_class_administrator,
      avgIfOrNull(time_spent, NOT in_class) AS avg_time_spent_out_class,
      avgIfOrNull(
        time_spent,
        (NOT in_class)
        AND (role = 'student')
      ) AS avg_time_spent_out_class_student,
      avgIfOrNull(
        time_spent,
        (NOT in_class)
        AND (role = 'teacher')
      ) AS avg_time_spent_out_class_teacher,
      avgIfOrNull(
        time_spent,
        (NOT in_class)
        AND (role = 'administrator')
      ) AS avg_time_spent_out_class_administrator,
      sumIfOrDefault(time_spent, NOT in_class) AS sum_time_spent_out_class,
      sumIfOrDefault(
        time_spent,
        (NOT in_class)
        AND (role = 'student')
      ) AS sum_time_spent_out_class_student,
      sumIfOrDefault(
        time_spent,
        (NOT in_class)
        AND (role = 'teacher')
      ) AS sum_time_spent_out_class_teacher,
      sumIfOrDefault(
        time_spent,
        (NOT in_class)
        AND (role = 'administrator')
      ) AS sum_time_spent_out_class_administrator,
      uniqStateIf(sessionId, NOT in_class) AS count_sessions_out_class,
      uniqStateIf(
        sessionId,
        (NOT in_class)
        AND (role = 'student')
      ) AS count_sessions_out_class_student,
      uniqStateIf(
        sessionId,
        (NOT in_class)
        AND (role = 'teacher')
      ) AS count_sessions_out_class_teacher,
      uniqStateIf(
        sessionId,
        (NOT in_class)
        AND (role = 'administrator')
      ) AS count_sessions_out_class_administrator
    FROM visits_info_cte
    GROUP BY
      userId,
      schoolId,
      orgId,
      grade,
      url,
      ${query.timeGranularity === 'all' ? '' : 'day,'} 
      domain
    )
    SELECT 
      ${query.timeGranularity === 'all' ? '' : 'day,'}  
      domain,
      url,
      page_views,
      page_views_student,
      page_views_teacher,
      active_users,
      active_users_student,
      active_users_teacher,
      avg_time_spent,
      avg_time_spent_student,
      avg_time_spent_teacher,
      sum_time_spent,
      sum_time_spent_student,
      sum_time_spent_teacher,
      count_sessions,
      count_sessions_student,
      count_sessions_teacher,
    FROM (
      SELECT 
      ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'} 
      domain, 
      url,
      countMerge(page_views) as page_views,
      countMerge(page_views_student) as page_views_student,
      countMerge(page_views_teacher) as page_views_teacher,
      uniqMerge(active_users) as active_users,
      uniqMerge(active_users_student) as active_users_student,
      uniqMerge(active_users_teacher) as active_users_teacher
      FROM goteacher.${tableToBeUsed} 
      WHERE 
      orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
      AND domain = 'youtube.com'
      AND url IN (${urls.map((url) => `'${url}'`).join(',')})
      ${query.search ? `AND url ILIKE '%${query.search}%'` : ''} 
      ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
      ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
      ${query.fromDate ? `AND day >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
      ${query.toDate ? `AND day <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
      GROUP BY 
      ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}   
      domain,
      url
    ) x
    LEFT JOIN (
      SELECT 
      ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}  
      domain, 
      url,
      avg(dscte.avg_time_spent) as avg_time_spent, 
      avg(dscte.avg_time_spent_student) as avg_time_spent_student, 
      avg(dscte.avg_time_spent_teacher) as avg_time_spent_teacher, 
      sum(dscte.sum_time_spent) as sum_time_spent, 
      sum(dscte.sum_time_spent_student) as sum_time_spent_student, 
      sum(dscte.sum_time_spent_teacher) as sum_time_spent_teacher, 
      uniqMerge(count_sessions) as count_sessions,
      uniqMerge(count_sessions_student) as count_sessions_student,
      uniqMerge(count_sessions_teacher) as count_sessions_teacher
      FROM daily_sessions_cte as dscte
      WHERE url IN (${urls.map((url) => `'${url}'`).join(',')})
      GROUP BY 
      ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'} 
      domain,
      url 
    ) y ON x.domain = y.domain AND x.url = y.url ${query.timeGranularity === TimeGranularity.ALL ? '' : 'AND x.day = y.day'}
    ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
    `;
    // LIMIT ${query.limit}
    // OFFSET ${query.offset};
    this.logger.debug(`sqlQuery: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
      total: number;
      limit: number;
      offset: number;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });

    const { data } = await queryResult.json();

    return {
      data: data.map((item: any) => ({
        ...item,
        ...taggedYoutubeData.find((video) => video.url === item.url)._doc,
      })),
      // data: taggedYoutubeData.map((video) => {
      //   const item: any = data.find((d: any) => d.url === video.url);
      //   return {
      //     ...item,
      //     ...video._doc,
      //   };
      // }),
      total: totalMongoData,
      limit: query.limit,
      offset: query.offset,
    };
  }

  getVisitsSQL(query: GetTaggedYoutubeDataQuery, orgIds: string[]): string {
    return `
        SELECT 
          st.sessionId AS sessionId,
          st.tabId AS tabId,
          st.userId AS userId,
          st.orgId AS orgId,
          st.schoolId AS schoolId,
          st.role AS role,
          st.grade AS grade,
          st.domain AS domain,
          st.url AS url,
          min(st.visit_start) AS visit_start,
          max(st.visit_end) AS visit_end,
          toStartOfDay(min(st.visit_start)) AS day,
          toStartOfWeek(min(st.visit_start)) AS week,
          any(st.visit_timezone) AS visit_timezone,
            dateDiff('s', visit_start, visit_end) AS time_spent,
            if(
                (
                    toHour(
                        addHours(visit_start, toInt32OrZero(visit_timezone))
                    ) >= 7
                )
                AND (
                    toHour(
                        addHours(visit_start, toInt32OrZero(visit_timezone))
                    ) <= 14
                ),
                1,
                0
            ) AS in_class
        FROM goteacher.visits_sc as st
        WHERE 
          st.orgId IN (${orgIds.map((o) => `'${o}'`).join(',')})
          AND st.domain = 'youtube.com'
          ${query.search ? `AND st.domain LIKE '%${query.search}%'` : ''}
          ${query.schoolIds?.length ? `AND st.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND st.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND st.visit_start >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND st.visit_end <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
        GROUP BY
            sessionId,
            tabId,
            userId,
            orgId,
            schoolId,
            role,
            grade,
            domain,
            url
        --HAVING
          --time_spent > 10
       `;
  }
}
