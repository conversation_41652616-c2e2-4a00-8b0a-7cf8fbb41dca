import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';
import { ReqContext } from '@goteacher/app/auth';
import { UserGroup } from '@goteacher/app/analytics/types';

export class GetUserByBucketQuery extends BaseDateRangeQuery {
  constructor(obj: GetUserByBucketQuery) {
    super();
    Object.assign(this, obj);
  }

  domains: string[];

  productIds?: string[];

  schoolIds?: string[];
  grades?: string[];
  userGroup?: UserGroup = UserGroup.BOTH;

  ctx: ReqContext;
  forceRefresh?: boolean = false;
}
