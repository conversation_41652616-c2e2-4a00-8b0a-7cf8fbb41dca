import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';

import { ReqContext } from '@goteacher/app/auth';
import { BaseDateRangeQuery } from '@goteacher/app/common/base-queries/base-date-range-query';
import { PaginationRequest } from '@goteacher/app/utility';

abstract class PaginatedDateRangeQuery
  extends PaginationRequest
  implements BaseDateRangeQuery
{
  fromDate?: Date;
  toDate?: Date;
}

export class GetTopGradesByDomainQuery extends PaginatedDateRangeQuery {
  constructor(obj: GetTopGradesByDomainQuery) {
    super(obj);
    Object.assign(this, obj);
  }

  domain?: string;

  productId?: string;

  search?: string;
  userGroup?: UserGroup = UserGroup.BOTH;
  timeGranularity?: TimeGranularity = TimeGranularity.ALL;
  schoolIds?: string[];
  grades?: string[];

  forceRefresh?: boolean = false;
  ctx: ReqContext;
}
