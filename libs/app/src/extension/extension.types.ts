import { ApiProperty } from '@nestjs/swagger';

/**
 * Time window configuration for when tracking is allowed
 */
export interface TimeWindow {
  /** Start time in HH:MM format (24-hour) */
  start: string;
  /** End time in HH:MM format (24-hour) */
  end: string;
  /** Days of week when this window applies (0=Sunday, 1=Monday, etc.) */
  daysOfWeek: number[];
}

/**
 * Timeframe restriction configuration
 */
export interface TimeframeRestriction {
  enabled: boolean;
  allowedWindow?: TimeWindow;
  /** Timezone for the time windows (e.g., 'America/New_York') */
  timezone?: string;
}

/**
 * Scraping configuration
 */
export interface ScrapingConfig {
  screenshot: boolean;
  metadata: boolean;
}

/**
 * Extension configuration interface
 */
export interface ExtensionConfig {
  allowScraping: ScrapingConfig;
  timeframeRestriction: TimeframeRestriction;
  consecutiveIdlePings: number;
  heartbeatInterval: number; // in seconds  
  sessionTimeout: number; // in minutes
}

export const DEFAULT_EXTENSION_CONFIG: ExtensionConfig = {
  allowScraping: {
    screenshot: false,
    metadata: true,
  },
  timeframeRestriction: {
    enabled: false,
    // allowedWindow: {
    //   start: '08:00',
    //   end: '17:00',
    //   daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
    // },
    // timezone: 'America/New_York',
  },
  consecutiveIdlePings: 5,
  heartbeatInterval: 30, // 30 seconds
  sessionTimeout: 30, // 30 minutes
};

/**
 * Extension configuration response DTO
 */
export class ExtensionConfigResponseDto implements ExtensionConfig {
  @ApiProperty({
    description: 'Scraping configuration',
    example: {
      screenshot: false,
      metadata: true,
    },
  })
  allowScraping: ScrapingConfig;

  @ApiProperty({
    description: 'Timeframe restrictions',
    example: {
      enabled: true,
      allowedWindow: {
        start: '08:00',
        end: '17:00',
        daysOfWeek: [1, 2, 3, 4, 5],
      },
      timezone: 'America/New_York',
    },
  })
  timeframeRestriction: TimeframeRestriction;

  @ApiProperty({
    description: 'Number of consecutive idle pings before considering user inactive',
    example: 5,
  })
  consecutiveIdlePings: number;

  @ApiProperty({
    description: 'Frequency in seconds for interaction summary batching and ping events',
    example: 30,
  })
  heartbeatInterval: number;

  @ApiProperty({
    description: 'Session expiration time in minutes',
    example: 30,
  })
  sessionTimeout: number;
}
