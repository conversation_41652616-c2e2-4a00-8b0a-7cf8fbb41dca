import { DEFAULT_EXTENSION_CONFIG, ExtensionConfig } from '@goteacher/app/extension/extension.types';
import { GetExtensionConfigQuery } from '@goteacher/app/extension/query/get-extension-config/query';
import { ExtensionConfigModel } from '@goteacher/app/models/sequelize/extension-config.model';
import { ICacheService } from '@goteacher/infra/cache';
import { Injectable, Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@Injectable()
@QueryHandler(GetExtensionConfigQuery)
export class GetExtensionConfigHandler
  implements IQueryHandler<GetExtensionConfigQuery> {
  private readonly logger = new Logger(GetExtensionConfigHandler.name);

  constructor(
    private cacheService: ICacheService,
    @InjectModel(ExtensionConfigModel)
    private extensionConfigModel: typeof ExtensionConfigModel,
  ) { }

  async execute(query: GetExtensionConfigQuery): Promise<ExtensionConfig> {
    const cacheKey = `extension-config-${query.orgId}`;

    const cachedConfig = await this.cacheService.get<ExtensionConfig>(cacheKey);
    if (cachedConfig) {
      this.logger.debug(`Extension config cache hit for orgId: ${query.orgId}`);
      return cachedConfig;
    }

    this.logger.debug(`Extension config cache miss for orgId: ${query.orgId}`);

    // Look for organization-specific configuration first
    let config = await this.extensionConfigModel.findOne({
      where: {
        orgId: query.orgId,
        isActive: true
      },
      order: [['updatedAt', 'DESC']], // Get the most recent if multiple exist
    });

    if (!config) {
      config = await this.extensionConfigModel.findOne({
        where: {
          orgId: '*',
          isActive: true
        },
        order: [['updatedAt', 'DESC']],
      });
    }

    const finalConfig: ExtensionConfig = config?.config || DEFAULT_EXTENSION_CONFIG;

    // Cache the result for 1 hour
    await this.cacheService.set(cacheKey, finalConfig, 60 * 60);

    return finalConfig;
  }
}
