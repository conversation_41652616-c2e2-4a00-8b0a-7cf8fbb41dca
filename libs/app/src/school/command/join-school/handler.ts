import {
  Organisation,
  OrganisationStatus,
} from '@goteacher/app/models/sequelize/organisation.model';
import {
  School,
  SchoolStatus,
} from '@goteacher/app/models/sequelize/school.model';
import {
  UserSchool,
  UserSchoolStatus,
} from '@goteacher/app/models/sequelize/user.school.model';
import { JoinSchoolCommand } from '@goteacher/app/school/command/join-school/command';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@CommandHandler(JoinSchoolCommand)
export class JoinSchoolCommandHandler
  implements ICommandHandler<JoinSchoolCommand>
{
  constructor(
    @InjectModel(School) private readonly schoolModel: typeof School,
    @InjectModel(UserSchool)
    private readonly userSchoolModel: typeof UserSchool,
  ) {}

  async execute(command: JoinSchoolCommand): Promise<any> {
    const userSchool = await this.userSchoolModel.findOne({
      where: {
        userId: command.ctx.user.id,
        schoolId: command.schoolId,
      },
    });

    if (userSchool) {
      return userSchool;
    }

    const school = await this.schoolModel.findOne({
      where: {
        id: command.schoolId,
      },
      include: [
        {
          model: Organisation,
        },
      ],
    });

    const domain = command.ctx.user.email.split('@')[1];
    const userMatchesOrgDomain = school.organisation.domains.includes(domain);
    const orgOrSchoolApproved =
      school.status === SchoolStatus.APPROVED ||
      school.organisation.status === OrganisationStatus.APPROVED;

    let status = UserSchoolStatus.PENDING;
    if (userMatchesOrgDomain && orgOrSchoolApproved) {
      status = UserSchoolStatus.APPROVED;
    }

    return await this.userSchoolModel.create({
      userId: command.ctx.user.id,
      schoolId: command.schoolId,
      status,
    });
  }
}
