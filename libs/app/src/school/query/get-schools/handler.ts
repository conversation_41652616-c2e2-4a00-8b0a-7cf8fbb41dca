import { Organisation } from '@goteacher/app/models/sequelize/organisation.model';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { GetSchoolsQuery } from '@goteacher/app/school/query/get-schools/query';
import { parseOrderBy } from '@goteacher/app/utility';
import { IQ<PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetSchoolsQuery)
export class GetSchoolsHandler implements IQueryHandler<GetSchoolsQuery> {
  constructor(
    @InjectModel(Organisation)
    private readonly organisationModel: typeof Organisation,
    @InjectModel(School) private readonly schoolModel: typeof School,
  ) {}

  async execute(query: GetSchoolsQuery): Promise<any> {
    const domain = query.ctx.user.email.split('@')[1];

    let schools = [];
    let schoolsCount = 0;
    const organisations = await this.organisationModel.findAll({
      where: {
        domains: {
          [Op.iLike]: `%${domain}%`,
        },
      },
    });

    if (organisations) {
      const whereClause = {
        organisationId: {
          [Op.in]: organisations.map((organisation) => organisation.id),
        },
        deleted: false,
      };

      const orderBy = parseOrderBy(
        query.order || [{ orderBy: 'displayName', orderDirection: 'asc' }],
      );

      if (query.search) {
        whereClause[Op.or] = [
          {
            name: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            displayName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
        ];
      }

      schoolsCount = await this.schoolModel.count({ where: whereClause });
      schools = await this.schoolModel.findAll({
        attributes: ['id', 'name', 'displayName'],
        where: whereClause,
        limit: query.limit,
        offset: query.offset,
        order: orderBy,
      });
    }

    return {
      data: schools,
      total: schoolsCount,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
