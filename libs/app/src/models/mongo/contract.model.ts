import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiExtraModels, ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { HydratedDocument } from 'mongoose';
import { validate as validateUUID } from 'uuid';

export enum PaymentStatus {
  FREE = 'FREE',
  PAID = 'PAID',
}

export enum PaymentMode {
  FIXED_FEE = 'FIXED_FEE',
  PRICE_PER_LICENSE = 'PRICE_PER_LICENSE',
}

export enum AudienceType {
  TEACHER = 'TEACHER',
  STUDENT = 'STUDENT',
  BOTH = 'BOTH',
  CUSTOM = 'CUSTOM',
}

export enum LicensableGrades {
  KG = 'KG',
  FIRST = '1',
  SECOND = '2',
  THIRD = '3',
  FOURTH = '4',
  FIFTH = '5',
  SIXTH = '6',
  SEVENTH = '7',
  EIGHTH = '8',
  NINTH = '9',
  TENTH = '10',
  ELEVENTH = '11',
  TWELFTH = '12',
}

export enum GradeGroup {
  KINDERGARDEN = 'KINDERGARDEN',
  ELEMENTARY = 'ELEMENTARY',
  MIDDLE = 'MIDDLE',
  HIGH_SCHOOL = 'HIGH_SCHOOL',
}

export class Audience {
  @ApiProperty({
    description: 'Audience type',
    enum: AudienceType,
    default: AudienceType.BOTH,
  })
  @IsOptional()
  @IsEnum(AudienceType)
  @Prop({ type: String, enum: AudienceType, default: AudienceType.BOTH })
  type: AudienceType;

  @ApiProperty({
    description: 'Grade groups for easier grade selection. Only applicable when type is STUDENT or BOTH',
    enum: GradeGroup,
    isArray: true,
    required: false,
  })
  @ValidateIf(o => o.type === AudienceType.STUDENT || o.type === AudienceType.BOTH)
  @IsOptional()
  @IsEnum(GradeGroup, { each: true })
  @Prop({
    type: [String],
    enum: GradeGroup,
    default: [],
    validate: {
      validator: function (this: Audience, value: GradeGroup[]) {
        return !this.type ||
          [AudienceType.STUDENT, AudienceType.BOTH].includes(this.type) ||
          !value?.length;
      },
      message: 'Grade groups can only be set when audience type is STUDENT or BOTH'
    }
  })
  gradeGroups?: GradeGroup[];

  @ApiProperty({
    description: 'Audience value, if empty applies all grades',
    default: [],
  })
  @IsArray()
  @IsEnum(LicensableGrades, { each: true })
  @Prop({ type: [LicensableGrades], default: [] })
  grades: LicensableGrades[];

  @ApiProperty({
    description: 'Schools, if empty applies to all schools',
    default: [],
  })
  @IsArray()
  @IsString({ each: true })
  @Prop({ type: [String], default: [] })
  schools: string[];
}

export class PaymentTerm {
  @ApiProperty({
    description: 'Payment mode',
    enum: PaymentMode,
    required: true,
  })
  @IsEnum(PaymentMode)
  @Prop({ type: String, enum: PaymentMode, required: true })
  paymentMode: PaymentMode;

  @ApiProperty({
    description: 'Audience',
    type: Audience,
    required: true,
  })
  @ValidateNested()
  @Type(() => Audience)
  @Prop({ type: Audience, required: false })
  audience?: Audience;
}

export class PaymentTermFixedFee extends PaymentTerm {
  @ApiProperty({ description: 'Total cost', type: Number, required: true })
  @IsNumber()
  @Prop({ type: Number, required: true })
  totalCost: number;
}

export class PaymentTermLicence extends PaymentTerm {
  @ApiProperty({
    description: 'Number of licenses',
    type: Number,
    required: true,
  })
  @IsNumber()
  @Prop({ type: Number, required: true })
  numberOfLicenses: number;

  @ApiProperty({
    description: 'Price per license',
    type: Number,
    required: true,
  })
  @IsNumber()
  @Prop({ type: Number, required: true })
  pricePerLicense: number;

  @ApiProperty({
    description: 'Total cost of the contract',
    type: Number,
    required: true,
  })
  @IsNumber()
  @Prop({ type: Number, required: true })
  totalCost: number;
}

@ApiExtraModels(PaymentTermFixedFee, PaymentTermLicence)
@Schema({
  collection: 'contracts',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class Contract {
  @ApiProperty({
    example: 'org.example.com',
    required: false,
    description: 'Domain name - required if productId is not provided',
  })
  @Prop({
    required: false,
    index: true,
    type: String,
    validate: {
      validator: function (this: Contract) {
        return (
          !!(this.domain || this.productId) && !(this.domain && this.productId)
        );
      },
      message: 'Either domain or productId must be provided, but not both',
    },
  })
  domain?: string;

  @ApiProperty({
    example: '67371943db5db89eb879318b',
    description: 'Product ID - required if domain is not provided',
    required: false,
  })
  @Prop({
    required: false,
    index: true,
    type: String,
  })
  productId?: string;

  @ApiProperty({
    example: 'Product name',
    description: 'Product name',
    required: false,
  })
  @Prop({
    required: false,
    index: true,
    type: String,
  })
  productName?: string;

  @ApiProperty({
    example: 'Product full domain',
    description: 'Product full domain',
    required: false,
  })
  @Prop({
    required: false,
    index: true,
    type: String,
  })
  fullDomain?: string;

  @ApiProperty({
    example: 'c2a1e5a0-3a45-4a10-8e5f-1e3d4f5e6b2a',
    description: 'Organization ID',
    required: true,
  })
  @Prop({
    required: true,
    index: true,
    type: String,
    validate: {
      validator: validateUUID,
      message: (props) => `${props.value} is not a valid UUID!`,
    },
  })
  organizationId: string;

  @ApiProperty({ description: 'Contract title', required: false, type: String })
  @Prop({ type: String, required: false })
  title?: string;

  @ApiProperty({ description: 'Contract title', required: false, type: String })
  @Prop({ type: Number, required: false })
  number: number;

  @ApiProperty({ description: 'Deleted', required: false, type: Boolean })
  @Prop({ index: true, type: Boolean, default: false })
  deleted: boolean;

  @ApiProperty({
    description: 'Subscription start date',
    required: true,
    type: Date,
  })
  @Prop({ type: Date, required: true, settings: { storage: 'iso' } })
  subscriptionStartDate: Date;

  @ApiProperty({
    description: 'Subscription end date',
    required: true,
    type: Date,
  })
  @Prop({ type: Date, required: true, settings: { storage: 'iso' } })
  subscriptionEndDate: Date;

  @ApiProperty({ description: 'Active subscription', type: Boolean })
  @Prop({ type: Boolean, default: true })
  active: boolean;

  @ApiProperty({ description: 'Extra fees', type: Number, default: 0 })
  @Prop({ type: Number, default: 0 })
  extraFees: number;

  @ApiProperty({
    description: 'Terms',
    isArray: true,
    type: PaymentTerm,
    discriminator: {
      propertyName: 'paymentMode',
      mapping: {
        [PaymentMode.FIXED_FEE]: getSchemaPath(PaymentTermFixedFee),
        [PaymentMode.PRICE_PER_LICENSE]: getSchemaPath(PaymentTermLicence),
      },
    },
    required: true,
  })
  @Prop({
    type: Array<PaymentTermFixedFee | PaymentTermLicence>,
    required: true,
  })
  terms: Array<PaymentTermFixedFee | PaymentTermLicence>;
}

export type ContractDocument = HydratedDocument<Contract>;

export const ContractSchema = SchemaFactory.createForClass(Contract);
