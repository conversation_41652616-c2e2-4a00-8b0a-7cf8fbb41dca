import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export interface PageMetadataDocument extends PageMetadata, Document { }

@Schema({
  collection: 'page-metadata',
  timestamps: true,
})
export class PageMetadata {
  @Prop({ required: true, unique: true, index: true })
  url: string;

  @Prop({ required: true, index: true })
  domain: string;

  @Prop({ type: Array })
  contents?: Array<object>;

  @Prop({ type: Array })
  metadata?: Array<object>;

  @Prop({ type: Array })
  headers?: Array<string>;

  @Prop({ type: Array })
  ldJson?: Array<string>;

  @Prop({ type: Boolean, default: false })
  isProcessed: boolean;

  @Prop({ index: true })
  createdAt?: Date;

  @Prop({ index: true })
  updatedAt?: Date;
}

export const PageMetadataSchema = SchemaFactory.createForClass(PageMetadata);

// Add indexes for better query performance
PageMetadataSchema.index({ domain: 1 });
PageMetadataSchema.index({ url: 1 });
PageMetadataSchema.index({ createdAt: -1 });

// Optimize for MongoDB performance with high volume writes
PageMetadataSchema.set('autoIndex', process.env.NODE_ENV !== 'production');
PageMetadataSchema.set('bufferCommands', false);
