import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class Tags {
  @ApiProperty({ description: 'Subject', type: String })
  @Prop({ required: true, type: String })
  subject: string;

  @ApiProperty({ description: 'Topic', type: String })
  @Prop({ required: true, type: String })
  topic: string;
}

export class TaggedContent {
  @ApiProperty({ description: 'Tagged video ID', type: Tags })
  @Type(() => Tags)
  @Prop({ required: true, type: Tags })
  tagged_video_id: Tags;

  @ApiProperty({ description: 'Tagged tittle', type: Tags })
  @Type(() => Tags)
  @Prop({ required: true, type: Tags })
  tagged_tittle: Tags;

  @ApiProperty({ description: 'Tagged about', type: Tags })
  @Type(() => Tags)
  @Prop({ required: true, type: Tags })
  tagged_about: Tags;

  @ApiProperty({ description: 'Tagged video transcription', type: Tags })
  @Type(() => Tags || String)
  @Prop({ required: true, type: Tags })
  tagged_video_transcription: Tags | string;
}

@Schema({
  collection: 'tagged_youtube_videos',
  timestamps: true,
})
export class TaggedYoutubeVideo {
  @ApiProperty({ description: 'URL', type: String })
  @Prop({ required: true, type: String })
  url: string;

  @ApiProperty({ description: 'Page views', type: String })
  @Prop({ required: true, type: String })
  page_views: string;

  @ApiProperty({ description: 'Video ID', type: String })
  @Prop({ required: true, type: String })
  video_id: string;

  @ApiProperty({ description: 'Title', type: String })
  @Prop({ required: true, type: String })
  title: string;

  @ApiProperty({ description: 'Subject', type: String })
  @Prop({ required: true, type: String })
  subject: string;

  @ApiProperty({ description: 'Topic', type: String })
  @Prop({ required: true, type: String })
  topic: string;

  @ApiProperty({ description: 'Description', type: String })
  @Type(() => TaggedContent)
  @Prop({ required: true, type: TaggedContent })
  tagged_content: TaggedContent;
}

export const TaggedYoutubeVideoSchema =
  SchemaFactory.createForClass(TaggedYoutubeVideo);
