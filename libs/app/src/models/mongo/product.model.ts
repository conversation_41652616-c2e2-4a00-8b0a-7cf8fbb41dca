import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';

export enum ProductType {
  LMS = 'LMS',
  CONTENT = 'CONTENT',
  TOOL = 'TOOL',
}

@Schema({
  collection: 'products',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class Product {
  @ApiProperty({ description: 'Product name', type: String })
  @Prop({ required: true, type: String })
  name: string;

  @ApiProperty({ description: 'Vendor name', type: String })
  @Prop({ required: true, type: String, index: true })
  vendor: string;

  @ApiProperty({ description: 'Product type', enum: ProductType })
  @Prop({ required: true, type: String, enum: ProductType, index: true })
  type: ProductType;

  @ApiProperty({ description: 'Product description', type: String })
  @Prop({ required: false, type: String })
  description?: string;

  @ApiProperty({ description: 'Domain rules' })
  @Prop({
    type: [
      {
        pattern: { type: String, required: true },
        priority: { type: Number, required: true, default: 1 },
        validFrom: { type: Date, required: true, default: () => new Date() },
        validTo: { type: Date, required: false, default: null },
      },
    ],
    default: [],
    validate: {
      validator: function (rules: any[]) {
        return rules.length <= 100;
      },
      message: 'A product cannot have more than 100 domain rules',
    },
  })
  domainRules: Array<{
    pattern: string;
    priority: number;
    validFrom: Date;
    validTo?: Date;
  }>;

  @ApiProperty({ description: 'Is product active', type: Boolean })
  @Prop({ type: Boolean, default: true, index: true })
  active: boolean;
}

export const ProductSchema = SchemaFactory.createForClass(Product);

// Add compound indexes
ProductSchema.index({ vendor: 1, type: 1 });
ProductSchema.index({ 'domainRules.pattern': 1, active: 1 });
ProductSchema.index({ 'domainRules.validFrom': 1, 'domainRules.validTo': 1 });
