import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { validate as validateUUID } from 'uuid';

export enum AuditAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

@Schema({
  collection: 'audit',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class Audit {
  @Prop({
    required: true,
    index: true,
    type: String,
    validate: {
      validator: validateUUID,
      message: (props) => `${props.value} is not a valid UUID!`,
    },
  })
  actorId: string;

  @Prop({ required: true, index: true, type: String, enum: AuditAction })
  action: AuditAction;

  @Prop({ required: true, index: true, type: String })
  contractId: string;
}

export type AuditDocument = HydratedDocument<Audit>;
export const AuditSchema = SchemaFactory.createForClass(Audit);
