import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export interface PageScreenshotDocument extends PageScreenshot, Document { }

@Schema({
  collection: 'page-screenshots',
  timestamps: true,
})
export class PageScreenshot {
  @Prop({ required: true, unique: true, index: true })
  url: string;

  @Prop({ required: true, index: true })
  domain: string;

  @Prop({ required: true })
  screenshotGcsUrl: string;

  @Prop({ required: true })
  screenshotSize: number;

  @Prop({ type: Boolean, default: false })
  isProcessed?: boolean;

  @Prop({ index: true })
  createdAt?: Date;

  @Prop({ index: true })
  updatedAt?: Date;
}

export const PageScreenshotSchema = SchemaFactory.createForClass(PageScreenshot);

// Add indexes for better query performance
PageScreenshotSchema.index({ domain: 1 });
PageScreenshotSchema.index({ url: 1 });
PageScreenshotSchema.index({ createdAt: -1 });
PageScreenshotSchema.index({ screenshotSize: 1 });

// Optimize for MongoDB performance with high volume writes
PageScreenshotSchema.set('autoIndex', process.env.NODE_ENV !== 'production');
PageScreenshotSchema.set('bufferCommands', false);
