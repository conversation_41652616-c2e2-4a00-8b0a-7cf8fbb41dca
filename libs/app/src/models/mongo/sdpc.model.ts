import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { HydratedDocument } from 'mongoose';

export enum SDPCAgreementStatus {
  ACTIVE = 'Active',
  INACTIVE = 'Inactive',
  EXPIRED = 'Expired',
}

export enum SDPCAccountStatus {
  APPROVED = 'Approved',
  PENDING = 'Pending',
  DECLINED = 'Declined',
}

export enum SDPCPublicStatus {
  YES = 'Yes',
  NO = 'No',
}

export type SDPCAgreementDocument = HydratedDocument<SDPCAgreement>;

@Schema({
  collection: 'sdpc_agreements',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class SDPCAgreement {
  @ApiProperty({
    example: '195',
    description: 'Data ID',
    required: true,
  })
  @Prop({ required: true, index: true, type: String })
  dataid: string;

  @ApiProperty({
    example: '3',
    description: 'Agreement type ID',
  })
  @Prop({ required: true, type: String })
  agreement_typesid: string;

  @ApiProperty({
    example: 'https://sdpc.a4l.org/agreements/example.pdf',
    description: 'Signed agreement file URL',
  })
  @Prop({ type: String })
  signed_agreement_file: string;

  @ApiProperty({
    example: 1,
    description: 'Status ID',
  })
  @Prop({ type: String, default: '1' })
  statusid: string;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Date approved',
  })
  @Prop({ type: Date })
  date_approved: Date;

  @ApiProperty({
    example: '2025-01-01T00:00:00.000Z',
    description: 'Date expired',
  })
  @Prop({ type: Date, default: null })
  date_expired: Date;

  @ApiProperty({
    example: '2023 - 2024',
    description: 'Academic year',
  })
  @Prop({ type: String })
  year: string;

  @ApiProperty({
    example: '6, 7, 8',
    description: 'Grade levels',
  })
  @Prop({ type: String })
  grade_level: string;

  @ApiProperty({
    example: 'Math, Science',
    description: 'Content areas',
  })
  @Prop({ type: String })
  content_area: string;

  @ApiProperty({
    description: 'Reasoning for declined status',
  })
  @Prop({ type: String, default: null })
  declined_reasoning: string;

  @ApiProperty({
    description: 'Originator of the agreement',
  })
  @Prop({ type: String, default: null })
  originator: string;

  @ApiProperty({
    example: '757',
    description: 'District ID',
    required: true,
  })
  @Prop({ required: true, index: true, type: String })
  districtid: string;

  @ApiProperty({
    example: 'Example School District',
    description: 'District name',
    required: true,
  })
  @Prop({ required: true, type: String })
  district_name: string;

  @ApiProperty({
    example: 'New York',
    description: 'City',
  })
  @Prop({ type: String })
  city: string;

  @ApiProperty({
    example: 'NY',
    description: 'State',
  })
  @Prop({ type: String })
  state: string;

  @ApiProperty({
    example: 'United States',
    description: 'Country',
  })
  @Prop({ type: String, default: 'United States' })
  country: string;

  @ApiProperty({
    example: 'http://example.org',
    description: 'District website',
  })
  @Prop({ type: String })
  district_website: string;

  @ApiProperty({
    description: 'District logo URL',
  })
  @Prop({ type: String })
  district_logo: string;

  @ApiProperty({
    example: SDPCAccountStatus.APPROVED,
    description: 'Account status',
    enum: SDPCAccountStatus,
  })
  @Prop({
    type: String,
    enum: SDPCAccountStatus,
    default: SDPCAccountStatus.APPROVED,
  })
  account_status: SDPCAccountStatus;

  @ApiProperty({
    example: '2512000',
    description: 'LEA ID',
  })
  @Prop({ type: String })
  leaid: string;

  @ApiProperty({
    description: 'Selected addendum',
  })
  @Prop({ type: String, default: null })
  selected_addendum: string;

  @ApiProperty({
    description: 'Signed addendum file URL',
  })
  @Prop({ type: String, default: null })
  signed_addendum_file: string;

  @ApiProperty({
    description: 'Addendum ID',
  })
  @Prop({ type: String, default: null })
  addendumid: string;

  @ApiProperty({
    description: 'Addendum name',
  })
  @Prop({ type: String, default: null })
  addendum_name: string;

  @ApiProperty({
    example: 3,
    description: 'Public agreement number',
  })
  @Prop({ type: String })
  public_agreement: string;

  @ApiProperty({
    example: 'District Modified',
    description: 'Agreement name',
  })
  @Prop({ type: String })
  agreement_name: string;

  @ApiProperty({
    example: 'All',
    description: 'Agreement state',
  })
  @Prop({ type: String, default: 'All' })
  agreement_state: string;

  @ApiProperty({
    description: 'Agreement description',
  })
  @Prop({ type: String })
  description: string;

  @ApiProperty({
    example: SDPCAgreementStatus.ACTIVE,
    description: 'Agreement status',
    enum: SDPCAgreementStatus,
  })
  @Prop({
    type: String,
    enum: SDPCAgreementStatus,
    default: SDPCAgreementStatus.ACTIVE,
  })
  status: SDPCAgreementStatus;

  @ApiProperty({
    example: SDPCPublicStatus.YES,
    description: 'Public status',
    enum: SDPCPublicStatus,
  })
  @Prop({
    type: String,
    enum: SDPCPublicStatus,
    default: SDPCPublicStatus.YES,
  })
  public_status: SDPCPublicStatus;

  @ApiProperty({
    example: 'IP Addresses, Cookies',
    description: 'Data list',
  })
  @Prop({ type: String })
  data_list: string;

  @ApiProperty({
    example: '137',
    description: 'Software ID',
    required: true,
  })
  @Prop({ required: true, index: true, type: String })
  softwareid: string;

  @ApiProperty({
    example: 'Example Company Inc.',
    description: 'Company name',
    required: true,
  })
  @Prop({ required: true, type: String })
  company_name: string;

  @ApiProperty({
    example: 'Example Software',
    description: 'Software name',
    required: true,
  })
  @Prop({ required: true, type: String })
  software_name: string;

  @ApiProperty({
    description: 'Google name',
  })
  @Prop({ type: String })
  google_name: string;

  @ApiProperty({
    example: 'http://example.com',
    description: 'Website URL',
  })
  @Prop({ type: String })
  website: string;

  @ApiProperty({
    description: 'Icon URL',
  })
  @Prop({ type: String })
  icon: string;

  @ApiProperty({
    description: 'Terms of service URL',
  })
  @Prop({ type: String })
  terms_of_services: string;

  @ApiProperty({
    description: 'Privacy policy URL',
  })
  @Prop({ type: String })
  privacy_policy: string;

  @ApiProperty({
    description: 'Purpose of the software',
  })
  @Prop({ type: String })
  purpose: string;
}

export const SDPCAgreementSchema = SchemaFactory.createForClass(SDPCAgreement);

// Primary compound unique index
SDPCAgreementSchema.index(
  {
    agreement_typesid: 1,
    districtid: 1,
    softwareid: 1,
    statusid: 1,
    leaid: 1,
    selected_addendum: 1,
    addendumid: 1,
    public_agreement: 1,
  },
  { unique: true, name: 'agreement_district_software_unique' },
);

// District software company index
SDPCAgreementSchema.index(
  {
    districtid: 1,
    software_name: 1,
    company_name: 1,
  },
  { name: 'district_software_company' },
);

// Software company lookup index
SDPCAgreementSchema.index(
  {
    software_name: 1,
    company_name: 1,
  },
  { name: 'software_company_lookup' },
);

// District date approved index
SDPCAgreementSchema.index(
  {
    districtid: 1,
    date_approved: -1,
  },
  { name: 'district_date_approved' },
);

// Company district lookup index
SDPCAgreementSchema.index(
  {
    company_name: 1,
    districtid: 1,
  },
  { name: 'company_district_lookup' },
);

// Single field indexes
SDPCAgreementSchema.index({ districtid: 1 });
SDPCAgreementSchema.index({ softwareid: 1 });
SDPCAgreementSchema.index({ company_name: 1 });

SDPCAgreementSchema.index({
  company_name: 1,
  software_name: 1,
});

// Additional indexes for status and website
SDPCAgreementSchema.index({ status: 1 }, { name: 'idx_status' });
SDPCAgreementSchema.index({ website: 1 }, { name: 'idx_website' });
