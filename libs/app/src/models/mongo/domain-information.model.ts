import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type DomainInformationDocument = DomainInformation & Document;

@Schema({
  collection: 'domains_information',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class DomainInformation {
  @ApiProperty({
    example: 'clever.com',
    required: true,
    description: 'Domain name',
  })
  @Prop({ required: true, index: true, type: String })
  domain: string;

  @ApiProperty({
    example: 'domain',
    description: 'Status of the domain check',
  })
  @Prop({ type: String })
  status?: string;

  @ApiProperty({
    example: 'Clever',
    description: 'Name of the product or service',
  })
  @Prop({ type: String })
  product_name?: string;

  @ApiProperty({
    example: 'Clever Inc.',
    description: 'Name of the parent company',
  })
  @Prop({ type: String })
  parent_company_name?: string;

  @ApiProperty({
    example: 'clever.com',
    description: 'Domain of the parent company',
  })
  @Prop({ type: String })
  parent_company_domain?: string;

  @ApiProperty({
    example: 'Educational',
    description: 'Category of the domain',
  })
  @Prop({ type: String })
  category?: string;

  @ApiProperty({
    example: 'Clever provides a single sign-on platform for K-12 schools',
    description: 'Description of the product',
  })
  @Prop({ type: String })
  product_description?: string;

  @ApiProperty({
    example: 2012,
    description: 'Year the company was founded',
  })
  @Prop({ type: MongooseSchema.Types.Mixed })
  founded_year?: number | string;

  @ApiProperty({
    example: 'https://clever.com/privacy',
    description: 'URL to the privacy policy',
  })
  @Prop({ type: String })
  privacy_policy_url?: string;

  @ApiProperty({
    example: 'pass',
    description: 'Student data privacy compliance status',
  })
  @Prop({ type: String })
  student_data_privacy_compliance?: string;

  @ApiProperty({
    example: 'low',
    description: 'Risk level assessment',
  })
  @Prop({ type: String })
  risk_level?: string;

  @ApiProperty({
    example: 'Complies with COPPA, does not sell data',
    description: 'Reasoning for privacy assessment',
  })
  @Prop({ type: String })
  privacy_assessment_reasoning?: string;
}

export const DomainInformationSchema = SchemaFactory.createForClass(DomainInformation);

DomainInformationSchema.index({ domain: 1 });
DomainInformationSchema.index({ product_name: 1 });
DomainInformationSchema.index({ parent_company_name: 1 });
DomainInformationSchema.index({ category: 1 });
DomainInformationSchema.index({ risk_level: 1 });