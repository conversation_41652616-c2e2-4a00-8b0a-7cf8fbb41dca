import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { ApiProperty } from '@nestjs/swagger';

export type MetadataCategoryDocument = MetadataCategory & Document;

@Schema({
  collection: 'metadata_categories',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class MetadataCategory {
  @ApiProperty({
    example: 'EDUCATION',
    required: true,
    description: 'Category name',
  })
  @Prop({ required: true, index: true, type: String })
  name: string;
}

export const MetadataCategorySchema = SchemaFactory.createForClass(MetadataCategory);

MetadataCategorySchema.index({ name: 1 }, { unique: true });
