import { <PERSON><PERSON>, AuditSchema } from '@goteacher/app/models/mongo/audit.model';
import {
  Contract,
  ContractSchema,
} from '@goteacher/app/models/mongo/contract.model';
import {
  CustomMetadata,
  CustomMetadataSchema,
} from '@goteacher/app/models/mongo/custom.metadata.model';
import {
  DomainAnalysis,
  DomainAnalysisSchema,
} from '@goteacher/app/models/mongo/domain-analysis.model';
import {
  DomainInformation,
  DomainInformationSchema,
} from '@goteacher/app/models/mongo/domain-information.model';
import {
  DomainMetadata,
  DomainMetadataSchema,
} from '@goteacher/app/models/mongo/domain.metadata.model';
import {
  MetadataCategory,
  MetadataCategorySchema
} from '@goteacher/app/models/mongo/metadata.category.model';
import {
  OAuthApp,
  OAuthAppSchema,
} from '@goteacher/app/models/mongo/oauth-app.model';
import {
  OAuthSyncMetadata,
  OAuthSyncMetadataSchema,
} from '@goteacher/app/models/mongo/oauth-sync-metadata.model';
import {
  OrganizationDomainMetadata,
  OrganizationDomainMetadataSchema,
} from '@goteacher/app/models/mongo/org.metadata.model';
import { PageMetadata, PageMetadataSchema } from '@goteacher/app/models/mongo/page-metadata.model';
import { PageScreenshot, PageScreenshotSchema } from '@goteacher/app/models/mongo/page-screenshot.model';
import {
  Product,
  ProductSchema,
} from '@goteacher/app/models/mongo/product.model';
import {
  SDPCAgreement,
  SDPCAgreementSchema,
} from '@goteacher/app/models/mongo/sdpc.model';
import {
  TaggedYoutubeVideo,
  TaggedYoutubeVideoSchema,
} from '@goteacher/app/models/mongo/tagged_youtube_videos';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: OrganizationDomainMetadata.name,
        schema: OrganizationDomainMetadataSchema,
      },
      {
        name: PageMetadata.name,
        schema: PageMetadataSchema,
      },
      {
        name: PageScreenshot.name,
        schema: PageScreenshotSchema,
      },
      {
        name: Contract.name,
        schema: ContractSchema,
      },
      {
        name: Audit.name,
        schema: AuditSchema,
      },
      {
        name: Product.name,
        schema: ProductSchema,
      },
      {
        name: SDPCAgreement.name,
        schema: SDPCAgreementSchema,
      },
      {
        name: TaggedYoutubeVideo.name,
        schema: TaggedYoutubeVideoSchema,
      },
      {
        name: DomainMetadata.name,
        schema: DomainMetadataSchema,
      },
      {
        name: CustomMetadata.name,
        schema: CustomMetadataSchema,
      },
      {
        name: MetadataCategory.name,
        schema: MetadataCategorySchema,
      },
      {
        name: OAuthApp.name,
        schema: OAuthAppSchema,
      },
      {
        name: OAuthSyncMetadata.name,
        schema: OAuthSyncMetadataSchema,
      },
      {
        name: DomainAnalysis.name,
        schema: DomainAnalysisSchema,
      },
      {
        name: DomainInformation.name,
        schema: DomainInformationSchema,
      },
    ]),
  ],
  exports: [MongooseModule],
})
export class MongooseModelModule { }
