import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { ApiProperty } from '@nestjs/swagger';
import { validate as validateUUID } from 'uuid';

export type CustomMetadataDocument = CustomMetadata & Document;

@Schema({
  collection: 'custom_metadata',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class CustomMetadata {
  @ApiProperty({
    example: 'example.com',
    required: true,
    description: 'Base domain name',
  })
  @Prop({ required: true, index: true, type: String })
  domain: string;

  @ApiProperty({
    example: 'sub.example.com',
    required: false,
    description: 'Full domain name including subdomain',
  })
  @Prop({ required: false, index: true, type: String })
  fullDomain: string;

  @ApiProperty({
    example: 'LOW',
    required: false,
    description: 'Risk level assessment of the domain',
  })
  @Prop({ required: false, type: String })
  riskLevel: string;

  @ApiProperty({
    example: ['EDUCATION', 'PRODUCTIVITY'],
    required: false,
    description: 'Categories of the domain',
  })
  @Prop({ required: false, type: [String], default: [] })
  categories: string[];

  @ApiProperty({
    example: 'c2a1e5a0-3a45-4a10-8e5f-1e3d4f5e6b2a',
    description: 'Organization ID',
    required: true,
  })
  @Prop({
    required: true,
    index: true,
    type: String,
    validate: {
      validator: validateUUID,
      message: (props) => `${props.value} is not a valid UUID!`,
    },
  })
  organizationId: string;
}

export const CustomMetadataSchema =
  SchemaFactory.createForClass(CustomMetadata);

CustomMetadataSchema.index({ domain: 1, organizationId: 1 }, { unique: true });
CustomMetadataSchema.index({ fullDomain: 1 });
CustomMetadataSchema.index({ riskLevel: 1 });
CustomMetadataSchema.index({ categories: 1 });
