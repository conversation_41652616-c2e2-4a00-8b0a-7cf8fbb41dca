import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { HydratedDocument } from 'mongoose';
import { validate as validateUUID } from 'uuid';

export enum DataPrivacy {
  SIGNED = 'SIGNED',
  NO_AGREEMENT = 'NO_AGREEMENT',
  IN_REVIEW = 'IN_REVIEW',
}

export enum DistrictRecommended {
  APPROVED = 'APPROVED',
  BANNED = 'BANNED',
  IN_REVIEW = 'IN_REVIEW',
}

export enum ApprovalStatus {
  APPROVED = 'APPROVED',
  NOT_APPROVED = 'NOT_APPROVED',
  IN_REVIEW = 'IN_REVIEW',
}

export type OrganizationDomainMetadataDocument =
  HydratedDocument<OrganizationDomainMetadata>;

@Schema({
  collection: 'organization_domain_metadata',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class OrganizationDomainMetadata {
  @ApiProperty({
    example: 'org.example.com',
    required: true,
    description: 'Domain name',
  })
  @Prop({ required: true, index: true, type: String })
  domain: string;

  @ApiProperty({
    example: 'c2a1e5a0-3a45-4a10-8e5f-1e3d4f5e6b2a',
    description: 'Organization ID',
    required: true,
  })
  @Prop({
    required: true,
    index: true,
    type: String,
    validate: {
      validator: validateUUID,
      message: (props) => `${props.value} is not a valid UUID!`,
    },
  })
  organizationId: string;

  @ApiProperty({
    example: DataPrivacy.SIGNED,
    description: 'Data privacy status',
    enum: DataPrivacy,
  })
  @Prop({ type: String, enum: DataPrivacy })
  dataPrivacy: DataPrivacy;

  @ApiProperty({
    example: ApprovalStatus.APPROVED,
    description: 'Approval status',
    enum: ApprovalStatus,
  })
  @Prop({ type: String, enum: ApprovalStatus })
  approvalStatus: ApprovalStatus;

  @ApiProperty({
    example: DistrictRecommended.APPROVED,
    description: 'District recommended status',
    enum: DistrictRecommended,
  })
  @Prop({ type: String, enum: DistrictRecommended })
  districtRecommended: DistrictRecommended;
}

export const OrganizationDomainMetadataSchema = SchemaFactory.createForClass(
  OrganizationDomainMetadata,
);

OrganizationDomainMetadataSchema.index(
  { domain: 1, organizationId: 1 },
  { unique: true },
);
