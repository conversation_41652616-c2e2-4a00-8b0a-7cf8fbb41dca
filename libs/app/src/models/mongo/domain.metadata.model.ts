import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';

export type DomainMetadataDocument = DomainMetadata & Document;

@Schema({
  collection: 'domain_metadata',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class DomainMetadata {
  @ApiProperty({
    example: 'example.com',
    required: true,
    description: 'Base domain name',
  })
  @Prop({ required: true, index: true, type: String })
  domain: string;

  @ApiProperty({
    example: 'sub.example.com',
    required: true,
    description: 'Full domain name including subdomain',
  })
  @Prop({ required: true, index: true, type: String })
  fullDomain: string;

  @ApiProperty({
    example: 'LOW',
    required: true,
    description: 'Risk level assessment of the domain',
  })
  @Prop({ required: true, type: String })
  riskLevel: string;

  @ApiProperty({
    example: 'This domain is not safe for students',
    description: 'Reasoning for the risk level',
  })
  @Prop({ type: String })
  reasoning?: string;

  @ApiProperty({
    example: ['EDUCATION', 'PRODUCTIVITY'],
    required: true,
    description: 'Categories of the domain',
  })
  @Prop({ required: true, type: [String], default: [] })
  categories: string[];

  @ApiProperty({
    example: 'Google Classroom',
    required: true,
    description: 'Name of the product or service',
  })
  @Prop({ required: true, type: String })
  productName: string;

  @ApiProperty({
    example: 'Google LLC',
    required: true,
    description: 'Name of the parent company',
  })
  @Prop({ required: true, type: String })
  parentCompany: string;

  @ApiProperty({
    example: 'google.com',
    required: true,
    description: 'Domain of the parent company',
  })
  @Prop({ required: true, type: String })
  parentCompanyDomain: string;

  @ApiProperty({
    example: 1998,
    description: 'Year the company was founded',
  })
  @Prop({ type: Number })
  foundedYear?: number;

  @ApiProperty({
    example: 'https://policies.google.com/privacy',
    description: 'URL to the privacy policy',
  })
  @Prop({ type: String })
  privacyPolicyUrl?: string;

  @ApiProperty({
    example: ['COPPA', 'FERPA', 'GDPR'],
    description: 'List of student data privacy compliance standards met',
  })
  @Prop({ type: [String], default: [] })
  studentDataPrivacyCompliance?: string[];

  @ApiProperty({
    example: 'Educational platform for K-12 students',
    description: 'Additional description or notes about the domain',
  })
  @Prop({ type: String })
  description?: string;
}

export const DomainMetadataSchema =
  SchemaFactory.createForClass(DomainMetadata);

DomainMetadataSchema.index({ fullDomain: 1 }, { unique: true });
DomainMetadataSchema.index({ domain: 1 });
DomainMetadataSchema.index({ riskLevel: 1 });
DomainMetadataSchema.index({ categories: 1 });
DomainMetadataSchema.index({ productName: 1 });
DomainMetadataSchema.index({ parentCompany: 1 });
DomainMetadataSchema.index({ parentCompanyDomain: 1 });
DomainMetadataSchema.index({ foundedYear: 1 });
DomainMetadataSchema.index({ privacyPolicyUrl: 1 });
