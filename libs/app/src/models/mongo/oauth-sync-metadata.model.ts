import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { HydratedDocument } from 'mongoose';

export type OAuthSyncMetadataDocument = HydratedDocument<OAuthSyncMetadata>;

@Schema({
  collection: 'oauth_sync_metadata',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class OAuthSyncMetadata {
  @ApiProperty({
    description: 'Organisation ID from PostgreSQL',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: true,
  })
  @Prop({
    required: true,
    type: String,
    index: true,
    unique: true,
  })
  organisationId: string;

  @ApiProperty({
    description: 'Last successful sync timestamp',
    example: '2024-01-15T14:30:00Z',
    required: true,
  })
  @Prop({
    required: true,
    type: Date,
  })
  lastSyncTimestamp: Date;

  @ApiProperty({
    description: 'Sync status',
    example: 'success',
    required: true,
  })
  @Prop({
    required: true,
    type: String,
    enum: ['success', 'failed', 'in_progress'],
    default: 'in_progress',
  })
  syncStatus: string;

  @ApiProperty({
    description: 'Total number of OAuth apps found in last sync',
    example: 150,
    required: false,
  })
  @Prop({
    type: Number,
    default: 0,
  })
  totalAppsCount: number;

  @ApiProperty({
    description: 'Error message if sync failed',
    example: 'Insufficient permissions',
    required: false,
  })
  @Prop({
    type: String,
    default: null,
  })
  errorMessage: string;

  @ApiProperty({
    description: 'Is this the first sync for this organization',
    example: true,
    required: true,
  })
  @Prop({
    required: true,
    type: Boolean,
    default: true,
  })
  isFirstSync: boolean;
}

export const OAuthSyncMetadataSchema = SchemaFactory.createForClass(OAuthSyncMetadata);

// Create index for performance
OAuthSyncMetadataSchema.index({ lastSyncTimestamp: -1 }, { name: 'idx_last_sync_timestamp' });