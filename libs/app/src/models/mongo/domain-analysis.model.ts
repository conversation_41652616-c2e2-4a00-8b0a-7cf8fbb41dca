import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';

export type DomainAnalysisDocument = DomainAnalysis & Document;

@Schema({ timestamps: false })
export class Scorecard {
  @ApiProperty({
    example: 'Partially Compliant',
    description: 'Compliance status for data collection and minimization',
  })
  @Prop({ type: String })
  data_collection_minimization: string;

  @ApiProperty({
    example: 'Non-Compliant',
    description: 'Compliance status for data use and sharing',
  })
  @Prop({ type: String })
  data_use_sharing: string;

  @ApiProperty({
    example: 'Partially Compliant',
    description: 'Compliance status for transparency',
  })
  @Prop({ type: String })
  transparency: string;

  @ApiProperty({
    example: 'Partially Compliant',
    description: 'Compliance status for security measures',
  })
  @Prop({ type: String })
  security_measures: string;

  @ApiProperty({
    example: 'Partially Compliant',
    description: 'Compliance status for legal compliance',
  })
  @Prop({ type: String })
  legal_compliance: string;

  @ApiProperty({
    example: 'Compliant',
    description: 'Compliance status for user rights and controls',
  })
  @Prop({ type: String })
  user_rights_controls: string;

  @ApiProperty({
    example: 'Partially Compliant',
    description: 'Compliance status for data retention and deletion',
  })
  @Prop({ type: String })
  data_retention_deletion: string;
}

@Schema({ timestamps: false })
export class FailedCategory {
  @ApiProperty({
    example: 'Data Use & Sharing',
    description: 'Category that failed compliance',
  })
  @Prop({ required: true, type: String })
  category: string;

  @ApiProperty({
    example: 'The policy explicitly states that Paddock may share personally identifiable information...',
    description: 'Reason for the compliance failure',
  })
  @Prop({ required: true, type: String })
  reason: string;
}

@Schema({
  collection: 'domains_analysis',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class DomainAnalysis {
  @ApiProperty({
    example: 'dailyherald.com',
    required: true,
    description: 'Domain name being analyzed',
  })
  @Prop({ required: true, index: true, type: String })
  domain: string;

  @ApiProperty({
    example: 'Medium Risk',
    description: 'Risk level assessment',
    enum: ['Low Risk', 'Medium Risk', 'High Risk', 'NO_DATA'],
  })
  @Prop({ type: String, index: true })
  risk_level?: string;

  @ApiProperty({
    type: Scorecard,
    description: 'Compliance scorecard for various privacy categories',
  })
  @Prop({ type: Scorecard })
  scorecard?: Scorecard;

  @ApiProperty({
    type: [FailedCategory],
    description: 'List of categories that failed compliance',
  })
  @Prop({ type: [FailedCategory], default: [] })
  failed_categories: FailedCategory[];

  @ApiProperty({
    example: 'Do Not Procure',
    description: 'Procurement recommendation',
    enum: ['Procure', 'Do Not Procure', 'Review Required', null],
  })
  @Prop({ type: String })
  recommendation?: string | null;
}

export const DomainAnalysisSchema = SchemaFactory.createForClass(DomainAnalysis);

DomainAnalysisSchema.index({ domain: 1 }, { unique: true });
DomainAnalysisSchema.index({ risk_level: 1 });
DomainAnalysisSchema.index({ recommendation: 1 });