import { ApiProperty } from '@nestjs/swagger';
import {
  Column,
  CreatedAt,
  DataType,
  Default,
  Model,
  PrimaryKey,
  Table,
  UpdatedAt,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';

export enum OrganisationStatus {
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
}

@Table({ tableName: 'organisation', timestamps: true })
export class Organisation extends Model<Organisation> {
  @ApiProperty({ description: 'Organisation ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'Organisation name', type: String })
  @Column(DataType.STRING)
  name: string;

  @ApiProperty({ description: 'Organisation display name', type: String })
  @Column(DataType.STRING)
  displayName: string;

  @ApiProperty({ description: 'Organisation domains', type: String })
  @Column(DataType.TEXT)
  domains: string;

  @ApiProperty({ description: 'Organisation status', enum: OrganisationStatus })
  @Default(OrganisationStatus.PENDING)
  @Column(DataType.ENUM({ values: [...Object.values(OrganisationStatus)] }))
  status: OrganisationStatus;

  @ApiProperty({ description: 'Organisation SDPC district ID', type: String })
  @Column(DataType.TEXT)
  sdpcDistrictId: string;

  @ApiProperty({ description: 'Is organisation deleted', type: Boolean })
  @Default(false)
  @Column(DataType.BOOLEAN)
  deleted: boolean;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @CreatedAt
  @Default(DataType.NOW)
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of creation', type: Date })
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @UpdatedAt
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of last update', type: Date })
  updatedAt: Date;
}
