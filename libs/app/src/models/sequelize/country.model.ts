import { ApiProperty } from '@nestjs/swagger';
import {
  Column,
  DataType,
  <PERSON><PERSON>ult,
  Has<PERSON>any,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';
import { Address } from './address.model';
import { State } from './state.model';

@Table({ tableName: 'country', timestamps: true })
export class Country extends Model<Country> {
  @ApiProperty({ description: 'Country ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'Country name', type: String })
  @Column(DataType.STRING)
  countryName: string;

  @ApiProperty({ description: 'Country code', type: String })
  @Column(DataType.STRING)
  countryInitials: string;

  @HasMany(() => State)
  state: State[];

  @HasMany(() => Address)
  address: Address[];
}
