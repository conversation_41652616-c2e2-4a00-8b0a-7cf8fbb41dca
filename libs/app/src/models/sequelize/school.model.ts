import { Address } from '@goteacher/app/models/sequelize/address.model';
import { Organisation } from '@goteacher/app/models/sequelize/organisation.model';
import { ApiProperty } from '@nestjs/swagger';
import {
  BelongsTo,
  Column,
  CreatedAt,
  DataType,
  Default,
  ForeignKey,
  Model,
  PrimaryKey,
  Table,
  UpdatedAt,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';

export enum SchoolStatus {
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
}

@Table({ tableName: 'school', timestamps: true })
export class School extends Model<School> {
  @ApiProperty({ description: 'School ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'School name', type: String })
  @Column(DataType.STRING)
  name: string;

  @ApiProperty({ description: 'School display name', type: String })
  @Column(DataType.STRING)
  displayName: string;

  @ApiProperty({ description: 'School status', enum: SchoolStatus })
  @Default(SchoolStatus.PENDING)
  @Column(DataType.ENUM({ values: [...Object.values(SchoolStatus)] }))
  status: SchoolStatus;

  @ApiProperty({ description: 'Organisation ID', type: String })
  @ForeignKey(() => Organisation)
  @Column(DataType.UUID)
  organisationId: string;

  @BelongsTo(() => Organisation)
  organisation: Organisation;

  @ApiProperty({ description: 'Address ID', type: String })
  @ForeignKey(() => Address)
  @Column(DataType.UUID)
  addressId: string;

  @BelongsTo(() => Address)
  address: Address;

  @Default(false)
  @Column(DataType.BOOLEAN)
  deleted: boolean;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @CreatedAt
  @Default(DataType.NOW)
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of creation', type: Date })
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @UpdatedAt
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of last update', type: Date })
  updatedAt: Date;
}
