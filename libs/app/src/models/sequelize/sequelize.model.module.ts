import { Account } from '@goteacher/app/models/sequelize/account.model';
import { Address } from '@goteacher/app/models/sequelize/address.model';
import { Blacklist } from '@goteacher/app/models/sequelize/blacklist.model';
import { Country } from '@goteacher/app/models/sequelize/country.model';
import { CsvImportLog } from '@goteacher/app/models/sequelize/csv-import-log.model';
import { ExtensionConfigModel } from '@goteacher/app/models/sequelize/extension-config.model';
import { Grade } from '@goteacher/app/models/sequelize/grade.model';
import { Organisation } from '@goteacher/app/models/sequelize/organisation.model';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { ScrapeConfig } from '@goteacher/app/models/sequelize/scrape-config.model';
import { State } from '@goteacher/app/models/sequelize/state.model';
import { Subject } from '@goteacher/app/models/sequelize/subject.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { Global, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

@Global()
@Module({
  imports: [
    SequelizeModule.forFeature([
      Grade,
      Subject,
      Address,
      Country,
      State,
      Organisation,
      School,
      User,
      UserSchool,
      Account,
      Blacklist,
      ScrapeConfig,
      ExtensionConfigModel,
      CsvImportLog,
    ]),
  ],
  exports: [SequelizeModule],
})
export class SequelizeModelModule { }
