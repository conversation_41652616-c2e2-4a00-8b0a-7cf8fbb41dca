import { User } from '@goteacher/app/models/sequelize/user.model';
import { ApiProperty } from '@nestjs/swagger';
import {
  BelongsTo,
  Column,
  CreatedAt,
  DataType,
  Default,
  ForeignKey,
  Model,
  PrimaryKey,
  Table,
  UpdatedAt,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';

export enum AccountPlatform {
  GOOGLE_CLASSROOM = 'GOOGLE_CLASSROOM',
}

@Table({
  tableName: 'accounts',
  timestamps: true,
})
export class Account extends Model<Account> {
  @ApiProperty({ description: 'Account ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'Account platform', enum: AccountPlatform })
  @Column(DataType.STRING)
  platform: AccountPlatform;

  @Column(DataType.JSONB)
  credentials: any;

  @ApiProperty({
    description: 'External identifier. For example, Google user ID',
  })
  @Column(DataType.STRING)
  externalId: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  userId: string;

  @BelongsTo(() => User)
  user: User;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @CreatedAt
  @Default(DataType.NOW)
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of creation', type: Date })
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @UpdatedAt
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of last update', type: Date })
  updatedAt: Date;
}
