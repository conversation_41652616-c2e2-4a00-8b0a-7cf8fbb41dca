import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { ApiProperty } from '@nestjs/swagger';
import {
  BelongsTo,
  Column,
  CreatedAt,
  DataType,
  Default,
  ForeignKey,
  Index,
  Model,
  PrimaryKey,
  Table,
  UpdatedAt,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';

export enum UserSchoolStatus {
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
  DECOMMISSIONED = 'DECOMMISSIONED',
}

@Table({
  tableName: 'userSchool',
  timestamps: true,
})
export class UserSchool extends Model<UserSchool> {
  @ApiProperty({ description: 'User school ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'User school status', enum: UserSchoolStatus })
  @Default(UserSchoolStatus.PENDING)
  @Column(DataType.ENUM({ values: [...Object.values(UserSchoolStatus)] }))
  status: UserSchoolStatus;

  @ApiProperty({ description: 'School ID', type: String })
  @ForeignKey(() => School)
  @Column(DataType.UUID)
  schoolId: string;

  @BelongsTo(() => School)
  school: School;

  @ApiProperty({ description: 'User ID', type: String })
  @ForeignKey(() => User)
  @Column(DataType.UUID)
  userId: string;

  @BelongsTo(() => User)
  user: User;

  @ApiProperty({ description: 'School year', type: String })
  @Index
  @Column(DataType.STRING)
  schoolYear: string;

  @ApiProperty({ description: 'Grade', type: String })
  @Index
  @Column(DataType.STRING)
  grade: string;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @CreatedAt
  @Default(DataType.NOW)
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of creation', type: Date })
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @UpdatedAt
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of last update', type: Date })
  updatedAt: Date;
}
