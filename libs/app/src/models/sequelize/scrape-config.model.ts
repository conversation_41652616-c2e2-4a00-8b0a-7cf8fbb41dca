import { ApiProperty } from '@nestjs/swagger';
import {
  AllowNull,
  Column,
  DataType,
  Default,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';

@Table({ tableName: 'scrape_config', timestamps: true })
export class ScrapeConfig extends Model<ScrapeConfig> {
  @ApiProperty({ description: 'Blacklist ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @AllowNull(false)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'Domain', type: String })
  @AllowNull(false)
  @Column(DataType.STRING)
  domain: string;

  @ApiProperty({ description: 'URL pattern', type: String })
  @Column({
    type: DataType.STRING,
  })
  urlPattern: string;

  @ApiProperty({ description: 'Contents', type: Object })
  @Column({
    type: DataType.JSONB,
  })
  contents: object;

  @ApiProperty({ description: 'Metadata', type: Object })
  @Column({
    type: DataType.JSONB,
  })
  metadata: object;

  @ApiProperty({ description: 'Requested tags', type: Object })
  @Column({
    type: DataType.JSONB,
  })
  requested_tags: object;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @AllowNull(false)
  @Column(DataType.DATE)
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @AllowNull(false)
  @Column(DataType.DATE)
  updatedAt: Date;
}
