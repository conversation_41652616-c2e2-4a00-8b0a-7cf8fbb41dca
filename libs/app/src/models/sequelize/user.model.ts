import { Account } from '@goteacher/app/models/sequelize/account.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { ApiProperty } from '@nestjs/swagger';
import {
  Column,
  CreatedAt,
  DataType,
  Default,
  HasMany,
  Model,
  PrimaryKey,
  Table,
  Unique,
  UpdatedAt,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';

export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  ADMINISTRATOR = 'administrator',
}

@Table({
  tableName: 'users',
  timestamps: true,
})
export class User extends Model<User> {
  @ApiProperty({ description: 'User ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'User email', type: String })
  @Unique
  @Column(DataType.TEXT)
  email: string;

  @ApiProperty({ description: 'User first name', type: String })
  @Column(DataType.STRING)
  firstName?: string;

  @ApiProperty({ description: 'User last name', type: String })
  @Column(DataType.STRING)
  lastName?: string;

  @ApiProperty({ description: 'User picture', type: String })
  @Column(DataType.STRING)
  picture?: string;

  @ApiProperty({ description: 'Is user registration completed', type: Boolean })
  @Default(false)
  @Column(DataType.BOOLEAN)
  isRegistrationCompleted: boolean;

  @ApiProperty({ description: 'Is user onboarding completed', type: Boolean })
  @Default(false)
  @Column(DataType.BOOLEAN)
  isOnboardingCompleted: boolean;

  @ApiProperty({ description: 'Is user deleted', type: Boolean })
  @Default(false)
  @Column(DataType.BOOLEAN)
  deleted: boolean;

  @ApiProperty({ description: 'User role', enum: UserRole })
  @Column(DataType.STRING)
  role?: string;

  @ApiProperty({ description: 'User metadata', type: Object })
  @Column(DataType.JSONB)
  Metadata?: any;

  @ApiProperty({ description: 'Is user synced', type: Boolean })
  @Default(false)
  @Column(DataType.BOOLEAN)
  Synced: boolean;

  @ApiProperty({ description: 'Date and time when user was decommissioned', type: Date, required: false })
  @Column({ type: 'timestamp without time zone', allowNull: true })
  decommissionedAt?: Date;

  @ApiProperty({ description: 'User schools', type: UserSchool })
  @HasMany(() => UserSchool)
  UserSchool: UserSchool[];

  @ApiProperty({ description: 'User accounts', type: Account })
  @HasMany(() => Account)
  Account: Account[];

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @CreatedAt
  @Default(DataType.NOW)
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of creation', type: Date })
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @UpdatedAt
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of last update', type: Date })
  updatedAt: Date;
}
