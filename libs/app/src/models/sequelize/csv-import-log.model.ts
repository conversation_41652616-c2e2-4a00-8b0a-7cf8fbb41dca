import { BelongsTo, Column, DataType, ForeignKey, Index, Model, Table } from 'sequelize-typescript';
import { Organisation } from './organisation.model';

export enum ImportType {
  STUDENT = 'student',
  STAFF = 'staff',
}

@Table({
  tableName: 'csv_import_logs',
  timestamps: true,
  createdAt: 'imported_at',
  updatedAt: false,
})
export class CsvImportLog extends Model<CsvImportLog> {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
  })
  id: string;

  @ForeignKey(() => Organisation)
  @Index('idx_csv_import_logs_org_year_type')
  @Column({
    type: DataType.STRING,
    allowNull: false,
    field: 'organization_id',
  })
  organizationId: string;

  @Index('idx_csv_import_logs_org_year_type')
  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    field: 'school_year',
  })
  schoolYear: string;

  @Index('idx_csv_import_logs_org_year_type')
  @Column({
    type: DataType.ENUM(...Object.values(ImportType)),
    allowNull: false,
    field: 'import_type',
  })
  importType: ImportType;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    field: 'imported_at',
  })
  importedAt: Date;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    field: 'file_name',
  })
  fileName: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  metadata: Record<string, any>;

  @BelongsTo(() => Organisation)
  organization: Organisation;
}