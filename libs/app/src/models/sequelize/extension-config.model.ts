import { ApiProperty } from '@nestjs/swagger';
import {
  AllowNull,
  Column,
  DataType,
  Default,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';
import { ExtensionConfig } from '@goteacher/app/extension/extension.types';

@Table({ tableName: 'extension_config', timestamps: true })
export class ExtensionConfigModel extends Model<ExtensionConfigModel> {
  @ApiProperty({ description: 'Extension config ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @AllowNull(false)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ 
    description: 'Organization ID (* for global default)', 
    type: String 
  })
  @AllowNull(false)
  @Column(DataType.STRING)
  orgId: string;

  @ApiProperty({ 
    description: 'Extension configuration JSON', 
    type: Object 
  })
  @AllowNull(false)
  @Column(DataType.JSONB)
  config: ExtensionConfig;

  @ApiProperty({ 
    description: 'Configuration description/name', 
    type: String 
  })
  @AllowNull(true)
  @Column(DataType.STRING)
  description?: string;

  @ApiProperty({ 
    description: 'Whether this configuration is active', 
    type: Boolean 
  })
  @AllowNull(false)
  @Default(true)
  @Column(DataType.BOOLEAN)
  isActive: boolean;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @AllowNull(false)
  @Column(DataType.DATE)
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @AllowNull(false)
  @Column(DataType.DATE)
  updatedAt: Date;
}
