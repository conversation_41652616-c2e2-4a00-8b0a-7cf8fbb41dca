import { BelongsTo, Column, DataType, Default, ForeignKey, HasMany, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';
import { Address } from './address.model';
import { Country } from './country.model';
import { ApiProperty } from '@nestjs/swagger';

@Table({ tableName: 'state', timestamps: true })
export class State extends Model<State> {
  @ApiProperty({ description: 'State ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'State name', type: String })
  @Column(DataType.STRING)
  stateName: string;

  @ApiProperty({ description: 'State initials', type: String })
  @Column(DataType.STRING)
  stateInitials: string;

  @ApiProperty({ description: 'Country ID', type: String })
  @ForeignKey(() => Country)
  @Column(DataType.UUID)
  countryId: string;

  @BelongsTo(() => Country)
  country: Country;

  @HasMany(() => Address)
  address: Address[];
}