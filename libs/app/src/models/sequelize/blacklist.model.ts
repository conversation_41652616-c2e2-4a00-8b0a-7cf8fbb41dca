import { ApiProperty } from '@nestjs/swagger';
import {
  AllowNull,
  Column,
  DataType,
  Default,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';

@Table({ tableName: 'blacklist', timestamps: true })
export class Blacklist extends Model<Blacklist> {
  @ApiProperty({ description: 'Blacklist ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @AllowNull(false)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'Organization', type: String })
  @AllowNull(false)
  @Column(DataType.STRING)
  orgId: string;

  @ApiProperty({ description: 'Domain', type: String })
  @AllowNull(false)
  @Column(DataType.STRING)
  domain: string;

  @ApiProperty({ description: 'URL', type: String })
  @Column(DataType.STRING)
  url: string;

  @ApiProperty({ description: 'URL pattern', type: String })
  @Column(DataType.STRING)
  urlPattern: string;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @AllowNull(false)
  @Column(DataType.DATE)
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @AllowNull(false)
  @Column(DataType.DATE)
  updatedAt: Date;
}
