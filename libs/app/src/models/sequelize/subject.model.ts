import { ApiProperty } from '@nestjs/swagger';
import {
  Column,
  CreatedAt,
  DataType,
  Default,
  Model,
  PrimaryKey,
  Table,
  UpdatedAt,
} from 'sequelize-typescript';

@Table({
  tableName: 'subject',
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
})
export class Subject extends Model<Subject> {
  @ApiProperty({ description: 'Subject ID', type: String })
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  @ApiProperty({
    description: 'Unique identifier for the subject',
    type: String,
  })
  id: string;

  @ApiProperty({ description: 'Subject name', type: String })
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  @ApiProperty({ description: 'Name of the subject', type: String })
  name: string;

  @ApiProperty({ description: 'Subject value', type: String })
  @Column(DataType.STRING)
  @ApiProperty({ description: 'Value of the subject', type: String })
  value: string;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @CreatedAt
  @Default(DataType.NOW)
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of creation', type: Date })
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @UpdatedAt
  @Column({ type: 'timestamp without time zone' })
  @ApiProperty({ description: 'Date and time of last update', type: Date })
  updatedAt: Date;
}
