import { ApiProperty } from '@nestjs/swagger';
import {
  Column,
  CreatedAt,
  DataType,
  Default,
  Model,
  PrimaryKey,
  Table,
  UpdatedAt,
} from 'sequelize-typescript';

@Table({
  tableName: 'grade',
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
})
export class Grade extends Model<Grade> {
  @ApiProperty({ description: 'Grade ID', type: String })
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.STRING)
  id: string;

  @ApiProperty({ description: 'Grade name', type: String })
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @ApiProperty({ description: 'Grade value', type: String })
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  value: string;

  @ApiProperty({ description: 'Grade order', type: Number })
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  order: number;

  @ApiProperty({
    description: 'Whether the grade is filterable',
    type: Boolean,
  })
  @Column(DataType.BOOLEAN)
  filterable: boolean;

  @ApiProperty({ description: 'Whether the grade is trackable', type: Boolean })
  @Column(DataType.BOOLEAN)
  trackable: boolean;

  @ApiProperty({
    description: 'Whether the grade is licenseable',
    type: Boolean,
  })
  @Column(DataType.BOOLEAN)
  licenseable: boolean;

  @ApiProperty({ description: 'Date and time of creation', type: Date })
  @CreatedAt
  @Default(DataType.NOW)
  @Column({ type: 'timestamp without time zone' })
  createdAt: Date;

  @ApiProperty({ description: 'Date and time of last update', type: Date })
  @UpdatedAt
  @Column({ type: 'timestamp without time zone' })
  updatedAt: Date;
}
