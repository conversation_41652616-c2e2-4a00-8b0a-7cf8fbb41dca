import { ApiProperty } from '@nestjs/swagger';
import {
  BelongsTo,
  Column,
  DataType,
  Default,
  ForeignKey,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { v4 as uuidv4 } from 'uuid';
import { Country } from './country.model';
import { State } from './state.model';

@Table({ tableName: 'address', timestamps: true })
export class Address extends Model<Address> {
  @ApiProperty({ description: 'Address ID', type: String })
  @PrimaryKey
  @Default(uuidv4)
  @Column(DataType.UUID)
  id: string;

  @ApiProperty({ description: 'Address line 1', type: String })
  @Column(DataType.STRING)
  addressLine1: string;

  @ApiProperty({ description: 'Address line 2', type: String })
  @Column(DataType.STRING)
  addressLine2: string;

  @ApiProperty({ description: 'Postal code', type: String })
  @Column(DataType.STRING)
  postalCode: string;

  @ApiProperty({ description: 'City', type: String })
  @Column(DataType.STRING)
  city: string;

  @ApiProperty({ description: 'Country ID', type: String })
  @ForeignKey(() => Country)
  @Column(DataType.UUID)
  countryId: string;

  @ApiProperty({ description: 'State ID', type: String })
  @ForeignKey(() => State)
  @Column(DataType.UUID)
  stateId: string;

  @BelongsTo(() => Country)
  country: Country;

  @BelongsTo(() => State)
  state: State;
}
