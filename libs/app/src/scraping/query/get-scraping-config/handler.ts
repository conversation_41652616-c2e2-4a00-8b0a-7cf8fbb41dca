import { ScrapeConfig } from '@goteacher/app/models/sequelize/scrape-config.model';
import { GetScrapingConfigQuery } from '@goteacher/app/scraping/query/get-scraping-config/query';
import { ICacheService } from '@goteacher/infra/cache';
import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON>uery<PERSON><PERSON>ler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@QueryHandler(GetScrapingConfigQuery)
export class GetScrapingConfigHandler
  implements IQueryHandler<GetScrapingConfigQuery>
{
  constructor(
    private cacheService: ICacheService,
    @InjectModel(ScrapeConfig) private scrapeConfigModel: typeof ScrapeConfig,
  ) {}

  async execute(query: GetScrapingConfigQuery): Promise<any> {
    const cache =
      await this.cacheService.get<ScrapeConfig[]>(`scraping-config`);

    const domains = ['*', query.domain];
    if (cache) {
      const urlMatches = cache.filter((config) => {
        const domainMatches = domains.includes(config.domain);
        const urlMatches = new RegExp(config.urlPattern).test(query.url);
        return domainMatches && urlMatches;
      });
      const domainMatch = urlMatches.find(
        (config) => config.domain === query.domain,
      );

      const defaultMatch = urlMatches.find((config) => {
        const domainMatches = config.domain === '*';
        return domainMatches;
      });

      if (!domainMatch && !defaultMatch)
        throw new NotFoundException('No applicable configuration found!');

      return domainMatch ?? defaultMatch;
    }

    const scrapeConfigs = await this.scrapeConfigModel.findAll({});
    this.cacheService.set(`scraping-config`, scrapeConfigs, 24 * 60 * 60);

    const urlMatches = scrapeConfigs.filter((config) => {
      const domainMatches = domains.includes(config.domain);
      const urlMatches = new RegExp(config.urlPattern).test(query.url);
      return domainMatches && urlMatches;
    });

    const domainMatch = urlMatches.find(
      (config) => config.domain === query.domain,
    );

    const defaultMatch = urlMatches.find((config) => {
      const domainMatches = config.domain === '*';
      return domainMatches;
    });

    if (!domainMatch && !defaultMatch)
      throw new NotFoundException('No applicable configuration found!');

    return domainMatch ?? defaultMatch;
  }
}
