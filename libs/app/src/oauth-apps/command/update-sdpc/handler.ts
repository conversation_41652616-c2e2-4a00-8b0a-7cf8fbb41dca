import { OAuthApp, OAuthAppDocument, SDPCAgreement, SDPCAgreementDocument } from '@goteacher/app/models/mongo';
import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON>and<PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UpdateOAuthAppSdpcCommand } from './command';

@CommandHandler(UpdateOAuthAppSdpcCommand)
export class UpdateOAuthAppSdpcHandler implements ICommandHandler<UpdateOAuthAppSdpcCommand> {
  constructor(
    @InjectModel(OAuthApp.name)
    private readonly oauthAppModel: Model<OAuthAppDocument>,
    @InjectModel(SDPCAgreement.name)
    private readonly sdpcAgreementModel: Model<SDPCAgreementDocument>,
  ) {}

  async execute(command: UpdateOAuthAppSdpcCommand): Promise<OAuthApp> {
    const { organisationId, clientId, sdpcAgreementId, districtId } = command;

    // Find the OAuth app
    const oauthApp = await this.oauthAppModel.findOne({
      clientId,
      organisationId,
    });

    if (!oauthApp) {
      throw new NotFoundException('OAuth app not found');
    }

    // Verify ownership
    if (oauthApp.organisationId !== organisationId) {
      throw new ForbiddenException('You do not have permission to update this OAuth app');
    }

    // If sdpcAgreementId is provided, validate it exists and belongs to the district
    if (sdpcAgreementId) {
      const sdpcAgreement = await this.sdpcAgreementModel.findOne({
        _id: sdpcAgreementId,
        districtid: districtId,
        status: 'Active',
      });

      if (!sdpcAgreement) {
        throw new BadRequestException(
          `SDPC Agreement with ID ${sdpcAgreementId} not found or not active for district ${districtId}`
        );
      }
    }

    // Update the overridden SDPC agreement ID
    const updatedApp = await this.oauthAppModel.findOneAndUpdate(
      { clientId, organisationId },
      { 
        $set: { 
          overriddenSdpcAgreementId: sdpcAgreementId,
          updatedAt: new Date(),
        },
      },
      { new: true },
    ).lean();
    
    return updatedApp;
  }
}