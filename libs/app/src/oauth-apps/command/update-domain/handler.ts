import { OAuthApp, OAuthAppDocument } from '@goteacher/app/models/mongo';
import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UpdateOAuthAppDomainCommand } from './command';

@CommandHandler(UpdateOAuthAppDomainCommand)
export class UpdateOAuthAppDomainHandler implements ICommandHandler<UpdateOAuthAppDomainCommand> {
  constructor(
    @InjectModel(OAuthApp.name)
    private readonly oauthAppModel: Model<OAuthAppDocument>,
  ) {}

  async execute(command: UpdateOAuthAppDomainCommand): Promise<OAuthApp> {
    const { organisationId, clientId, domain } = command;

    // Find the OAuth app
    const oauthApp = await this.oauthAppModel.findOne({
      clientId,
      organisationId,
    });

    if (!oauthApp) {
      throw new NotFoundException('OAuth app not found');
    }

    // Verify ownership
    if (oauthApp.organisationId !== organisationId) {
      throw new ForbiddenException('You do not have permission to update this OAuth app');
    }

    // Update the domain
    const updatedApp = await this.oauthAppModel.findOneAndUpdate(
      { clientId, organisationId },
      { 
        $set: { 
          domain,
          updatedAt: new Date(),
        },
      },
      { new: true },
    ).lean();
    
    return updatedApp;
  }
}