import { OA<PERSON>A<PERSON>, OAuthAppDocument } from '@goteacher/app/models/mongo';
import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { NotFoundException } from '@nestjs/common';
import { Model } from 'mongoose';
import { GetOAuthAppByIdQuery } from './query';

@QueryHandler(GetOAuthAppByIdQuery)
export class GetOAuthAppByIdHandler implements IQueryHandler<GetOAuthAppByIdQuery> {
  constructor(
    @InjectModel(OAuthApp.name)
    private readonly oauthAppModel: Model<OAuthAppDocument>,
  ) {}

  async execute(query: GetOAuthAppByIdQuery): Promise<OAuthApp> {
    const { organisationId, clientId, districtId } = query;

    const matchStage: any = { 
      organisationId,
      clientId 
    };

    // Build aggregation pipeline
    const pipeline: any[] = [
      // Match stage
      { $match: matchStage },
      
      // Lookup privacy policy analysis using the oauth app's domain directly
      {
        $lookup: {
          from: "domains_analysis",
          let: { domain: "$domain" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $ne: ["$$domain", null] },
                    { $eq: [{ $toLower: "$domain" }, { $toLower: "$$domain" }] }
                  ]
                }
              }
            },
            { $limit: 1 }
          ],
          as: "privacyPolicyAnalysis"
        }
      },
      
      // Unwind privacy policy
      {
        $unwind: {
          path: "$privacyPolicyAnalysis",
          preserveNullAndEmptyArrays: true
        }
      },
      
      // Lookup overridden SDPC agreement if exists
      {
        $lookup: {
          from: "sdpc_agreements",
          let: { 
            overriddenId: {
              $cond: {
                if: { 
                  $and: [
                    { $ne: ["$overriddenSdpcAgreementId", null] },
                    { $ne: ["$overriddenSdpcAgreementId", ""] }
                  ]
                },
                then: { $toObjectId: "$overriddenSdpcAgreementId" },
                else: null
              }
            }
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $ne: ["$$overriddenId", null] },
                    { $eq: ["$_id", "$$overriddenId"] },
                    { $eq: ["$districtid", districtId] }
                  ]
                }
              }
            },
            { $limit: 1 }
          ],
          as: "overriddenSdpcAgreement"
        }
      },
      
      // Unwind overridden SDPC agreement (preserving null)
      {
        $unwind: {
          path: "$overriddenSdpcAgreement",
          preserveNullAndEmptyArrays: true
        }
      },
      
      // Lookup SDPC agreements with domain matching (always performed)
      {
        $lookup: {
          from: "sdpc_agreements",
          let: { 
            domain: "$domain"
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    // Filter by district first - this uses an index!
                    { $eq: ["$districtid", districtId] },
                    // Only active agreements
                    { $eq: ["$status", "Active"] },
                    // Match by domain extracted from website URL
                    { $ne: ["$$domain", null] },
                    { $ne: ["$website", null] },
                    {
                      $eq: [
                        {
                          $toLower: {
                            $replaceAll: {
                              input: {
                                // Extract domain from URL by removing protocol and path
                                $arrayElemAt: [
                                  {
                                    $split: [
                                      {
                                        $replaceAll: {
                                          input: {
                                            $replaceAll: {
                                              input: "$website",
                                              find: "https://",
                                              replacement: ""
                                            }
                                          },
                                          find: "http://",
                                          replacement: ""
                                        }
                                      },
                                      "/"
                                    ]
                                  },
                                  0
                                ]
                              },
                              find: "www.",
                              replacement: ""
                            }
                          }
                        },
                        { $toLower: "$$domain" }
                      ]
                    }
                  ]
                }
              }
            },
            // Sort by date_expired descending to get latest expiration first
            { $sort: { date_expired: -1 } },
            { $limit: 5 } // Get top 5 matching agreements
          ],
          as: "domainMatchedSdpcAgreements"
        }
      },
      
      // Add both types of SDPC agreements
      {
        $addFields: {
          // New field for automatic domain matching results
          automaticSdpcAgreements: { $ifNull: ["$domainMatchedSdpcAgreements", []] },
          // Keep the overridden agreement in response
          manualSdpcAgreement: "$overriddenSdpcAgreement",
          isManuallyOverridden: { 
            $and: [
              { $ne: [{ $type: "$overriddenSdpcAgreementId" }, "missing"] },
              { $ne: ["$overriddenSdpcAgreementId", null] }
            ]
          }
        }
      },
      
      // Remove only temporary internal fields
      {
        $project: {
          overriddenSdpcAgreement: 0,
          domainMatchedSdpcAgreements: 0
        }
      },
      
      // Lookup organization domain metadata using domain field
      {
        $lookup: {
          from: "organization_domain_metadata",
          let: { domain: "$domain" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $ne: ["$$domain", null] },
                    { $eq: [{ $toLower: "$domain" }, { $toLower: "$$domain" }] },
                    { $eq: ["$organizationId", organisationId] }
                  ]
                }
              }
            },
            { $limit: 1 }
          ],
          as: "organizationDomainMetadata"
        }
      },
      
      // Unwind organization domain metadata
      {
        $unwind: {
          path: "$organizationDomainMetadata",
          preserveNullAndEmptyArrays: true
        }
      },
      
      // Add privacy field calculation
      {
        $addFields: {
          privacy: {
            $cond: {
              if: {
                $and: [
                  // Check if we have at least one SDPC agreement
                  { $gt: [{ $size: { $ifNull: ["$sdpcAgreements", []] } }, 0] },
                  // Check if the first agreement has a date_expired field
                  { $ne: [{ $ifNull: [{ $arrayElemAt: ["$sdpcAgreements", 0] }, null] }, null] },
                  // Check if date_expired exists and is not null
                  { $ne: [{ $ifNull: [{ $arrayElemAt: ["$sdpcAgreements.date_expired", 0] }, null] }, null] },
                  // Check if date_expired is greater than current date
                  {
                    $gt: [
                      { $toDate: { $arrayElemAt: ["$sdpcAgreements.date_expired", 0] } },
                      new Date()
                    ]
                  }
                ]
              },
              then: "PASS",
              else: "FAIL"
            }
          }
        }
      },
      
      {
        $project: {
          __v: 0,
          _id: 0
        }
      }
    ];

    const results = await this.oauthAppModel.aggregate(pipeline).exec();
    
    if (!results || results.length === 0) {
      throw new NotFoundException(`OAuth app with clientId ${clientId} not found`);
    }

    let result = results[0];
    
    // Post-process the data to clean up sdpcAgreements based on isManuallyOverridden
    if (result.isManuallyOverridden) {
      // Use manual agreement and clean up fields
      result.sdpcAgreements = result.manualSdpcAgreement ? [result.manualSdpcAgreement] : [];
      delete result.manualSdpcAgreement;
      delete result.automaticSdpcAgreements;
    } else {
      // Use automatic agreements and clean up fields
      result.sdpcAgreements = result.automaticSdpcAgreements || [];
      delete result.manualSdpcAgreement;
      delete result.automaticSdpcAgreements;
    }

    return result;
  }
}