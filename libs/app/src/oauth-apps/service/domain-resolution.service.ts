import { OAuthApp, OAuthAppDocument } from '@goteacher/app/models/mongo/oauth-app.model';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel as InjectModelMongoose } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import OpenAI from 'openai';

@Injectable()
export class DomainResolutionService {
  private openai: OpenAI;

  constructor(
    @InjectModelMongoose(OAuthApp.name) private oauthAppModel: Model<OAuthAppDocument>,
    private readonly configService: ConfigService,
  ) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('GEMINI_API_KEY'),
      maxRetries: 5,
      baseURL: "https://generativelanguage.googleapis.com/v1beta/openai/"
    });
  }

  async resolveMissingDomains(organisationId: string): Promise<void> {
    const timestamp = new Date().toISOString();
    console.log(`[DOMAIN_RESOLUTION] [${timestamp}] Starting domain resolution for organisation ${organisationId}`);

    try {
      const appsWithoutDomains = await this.oauthAppModel.find({
        organisationId,
        $or: [{ domain: null }, { domain: { $exists: false } }],
      }).lean();

      if (appsWithoutDomains.length === 0) {
        console.log(`[DOMAIN_RESOLUTION] [${timestamp}] No apps without domains found for organisation ${organisationId}`);
        return;
      }

      console.log(`[DOMAIN_RESOLUTION] [${timestamp}] Found ${appsWithoutDomains.length} apps without domains`);

      const resolvedDomains = await this.getResolvedDomainMappings();
      
      const appsToUpdate = [];
      const unresolvedAppNames = new Set<string>();

      for (const app of appsWithoutDomains) {
        if (resolvedDomains.has(app.appName)) {
          appsToUpdate.push({
            clientId: app.clientId,
            organisationId: app.organisationId,
            domain: resolvedDomains.get(app.appName),
          });
        } else {
          unresolvedAppNames.add(app.appName);
        }
      }

      if (appsToUpdate.length > 0) {
        await this.updateAppDomains(appsToUpdate);
        console.log(`[DOMAIN_RESOLUTION] [${timestamp}] Updated ${appsToUpdate.length} apps with existing domain mappings`);
      }

      const unresolvedArray = Array.from(unresolvedAppNames);
      if (unresolvedArray.length === 0) {
        console.log(`[DOMAIN_RESOLUTION] [${timestamp}] All apps resolved from existing mappings`);
        return;
      }

      console.log(`[DOMAIN_RESOLUTION] [${timestamp}] Processing ${unresolvedArray.length} unresolved app names`);
      
      const batchSize = 50;
      for (let i = 0; i < unresolvedArray.length; i += batchSize) {
        const batch = unresolvedArray.slice(i, i + batchSize);
        console.log(`[DOMAIN_RESOLUTION] [${timestamp}] Processing batch ${Math.floor(i / batchSize) + 1} with ${batch.length} apps`);
        
        const resolvedBatch = await this.resolveDomainBatch(batch);
        
        const batchUpdates = [];
        for (const app of appsWithoutDomains) {
          if (resolvedBatch.has(app.appName)) {
            batchUpdates.push({
              clientId: app.clientId,
              organisationId: app.organisationId,
              domain: resolvedBatch.get(app.appName),
            });
          }
        }

        if (batchUpdates.length > 0) {
          await this.updateAppDomains(batchUpdates);
          console.log(`[DOMAIN_RESOLUTION] [${timestamp}] Updated ${batchUpdates.length} apps from batch`);
        }
      }

      console.log(`[DOMAIN_RESOLUTION] [${timestamp}] Domain resolution completed for organisation ${organisationId}`);
    } catch (error) {
      console.error(`[DOMAIN_RESOLUTION] [${timestamp}] Error during domain resolution:`, error.message);
    }
  }

  private async getResolvedDomainMappings(): Promise<Map<string, string>> {
    const appsWithDomains = await this.oauthAppModel.find({
      domain: { $ne: null, $exists: true },
    }).select('appName domain').lean();

    const domainMap = new Map<string, string>();
    for (const app of appsWithDomains) {
      if (app.domain && app.domain !== 'NOT_FOUND') {
        domainMap.set(app.appName, app.domain);
      }
    }

    return domainMap;
  }

  private async resolveDomainBatch(appNames: string[]): Promise<Map<string, string>> {
    const timestamp = new Date().toISOString();
    const resolvedDomains = new Map<string, string>();

    try {
      const prompt = `Identify the website name for the following applications. Return a JSON object where each key is the app name and the value is either the primary website domain. and not the app domain (e.g., "google.com") or "NOT_FOUND" if you cannot determine the domain.

      Apps to resolve:
${appNames.join(', ')}

Example response format:
{
  "Google Drive": "drive.google.com",
  "Facebook": "facebook.com",
  "Unknown App XYZ": "NOT_FOUND"
}`;

      const response = await this.openai.chat.completions.create({
        model: "gemini-2.5-flash",
        messages: [
          {
            role: 'system',
            content: 'You are a domain identification assistant. Given app names, identify their primary website domains. Return only valid JSON.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.1,
        response_format: { type: 'json_object' },
      });

      const content = response.choices[0]?.message?.content;
      if (content) {
        const parsedResponse = JSON.parse(content);
        
        for (const appName of appNames) {
          if (parsedResponse[appName]) {
            resolvedDomains.set(appName, parsedResponse[appName]);
          } else {
            const matchedKey = Object.keys(parsedResponse).find(
              key => key.toLowerCase() === appName.toLowerCase()
            );
            if (matchedKey) {
              resolvedDomains.set(appName, parsedResponse[matchedKey]);
            } else {
              resolvedDomains.set(appName, 'NOT_FOUND');
            }
          }
        }
      }

      console.log(`[DOMAIN_RESOLUTION] [${timestamp}] Successfully resolved ${resolvedDomains.size} domains from batch`);
    } catch (error) {
      console.error(`[DOMAIN_RESOLUTION] [${timestamp}] Error calling OpenAI API:`, error.message);
      for (const appName of appNames) {
        resolvedDomains.set(appName, 'NOT_FOUND');
      }
    }

    return resolvedDomains;
  }

  private async updateAppDomains(updates: { clientId: string; organisationId: string; domain: string }[]): Promise<void> {
    const bulkOperations = updates.map(update => ({
      updateOne: {
        filter: { 
          organisationId: update.organisationId, 
          clientId: update.clientId 
        },
        update: {
          $set: { 
            domain: update.domain,
            updatedAt: new Date(),
          },
        },
      },
    }));

    if (bulkOperations.length > 0) {
      await this.oauthAppModel.bulkWrite(bulkOperations);
    }
  }
}