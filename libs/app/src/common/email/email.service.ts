import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MailDataRequired } from '@sendgrid/mail';
import { SendGridClient } from './sendgrid-client';
@Injectable()
export class EmailService {
  constructor(
    private readonly sendGridClient: SendGridClient,
    private readonly configService: ConfigService,
  ) { }

  async sendEmail(recipient: string, subject = 'Test email', body = 'This is a test mail'): Promise<void> {
    const mail: MailDataRequired = {
      to: recipient,
      from: this.configService.get('SENDGRID_FROM_EMAIL'),
      subject,
      content: [{ type: 'text/plain', value: body }],
    };
    await this.sendGridClient.send(mail);
  }

  async sendEmailWithTemplate(recipient: string, body: string): Promise<void> {
    const mail: MailDataRequired = {
      to: recipient,
      cc: '<EMAIL>', //Assuming you want to send a copy to this email
      from: this.configService.get('SENDGRID_FROM_EMAIL'), //Approved sender ID in Sendgrid
      templateId: 'Sendgrid_template_ID', //Retrieve from config service or environment variable
      dynamicTemplateData: { body, subject: 'Send Email with template' }, //The data to be used in the template
    };
    await this.sendGridClient.send(mail);
  }
}