import {
  CallHandler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
  Scope,
} from '@nestjs/common';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { FastifyReply, FastifyRequest } from 'fastify';
import { ClsService } from 'nestjs-cls';
import { catchError, tap } from 'rxjs';
import { v4 as uuid } from 'uuid';

@Injectable({ scope: Scope.REQUEST })
export class HttpContextInterceptor implements NestInterceptor {
  private readonly logger = new Logger(HttpContextInterceptor.name);
  private readonly logHealthy: boolean;

  constructor(private readonly cls: ClsService) {
    this.logHealthy = process.env.LOG_HEALTHY_REQUESTS === 'true';
  }

  intercept(context: ExecutionContext, next: CallHandler) {
    if (context.getType() === 'http') {
      return this.requestLog(context.switchToHttp(), next);
    }
    return next.handle();
  }

  requestLog(context: HttpArgumentsHost, next: Call<PERSON>and<PERSON>) {
    const startAt = process.hrtime();
    const req: FastifyRequest = context.getRequest();

    const requestId = uuid();
    const correlationId = req.headers['X-Correlation-Id'] || uuid();

    const { ip, method, originalUrl } = req;
    const userAgent = req.headers['user-agent'] || '';
    const { authorization: _authorization, ...headers } = req.headers;

    this.cls.set('requestId', requestId);
    this.cls.set('correlationId', correlationId);

    // Only log the request details if detailed logging is enabled
    if (this.logHealthy) {
      this.logger.log(
        `Request: ${correlationId} ${requestId} | ${method} ${originalUrl} - ${userAgent} ${ip} ${JSON.stringify(
          {
            body: req.body,
            headers: headers,
          },
        )}`,
      );
    }

    return next.handle().pipe(
      catchError((err) => {
        // Always log errors so you catch problematic situations.
        this.logger.error(err);
        const res: FastifyReply = context.getResponse();
        res.header('X-Request-ID', requestId);
        res.header('X-Correlation-ID', correlationId);
        const { statusCode } = res;
        const contentLength = res.raw.getHeader('content-length');
        const diff = process.hrtime(startAt);
        const responseTime = diff[0] * 1e3 + diff[1] * 1e-6;
        this.logger.error(
          `Response: ${correlationId} ${requestId} | ${method} ${originalUrl} ${statusCode} ${responseTime}ms ${contentLength} - ${userAgent} ${ip}`,
        );
        throw err;
      }),
      tap(() => {
        const res: FastifyReply = context.getResponse();
        res.header('X-Request-ID', requestId);
        res.header('X-Correlation-ID', correlationId);
        const { statusCode } = res;
        const contentLength = res.raw.getHeader('content-length');
        const diff = process.hrtime(startAt);
        const responseTime = diff[0] * 1e3 + diff[1] * 1e-6;
        // Log response details only if healthy logging is enabled
        if (this.logHealthy) {
          this.logger.log(
            `Response: ${correlationId} ${requestId} | ${method} ${originalUrl} ${statusCode} ${responseTime}ms ${contentLength} - ${userAgent} ${ip}`,
          );
        }
      }),
    );
  }
}
