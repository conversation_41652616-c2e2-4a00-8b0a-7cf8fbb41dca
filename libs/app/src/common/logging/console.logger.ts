import { ConsoleLogger, Injectable, LogLevel } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

const PROD_LOG_LEVEL: LogLevel[] = ['log', 'warn', 'error'];
const DEV_LOG_LEVEL: LogLevel[] = ['verbose', 'debug', 'log', 'warn', 'error'];

@Injectable()
export class CommonConsoleLogger extends ConsoleLogger {
  constructor(
    private readonly cls: ClsService,
    context: string,
  ) {
    super(context);
    super.setLogLevels(
      process.env.NODE_ENV == 'production' ? PROD_LOG_LEVEL : DEV_LOG_LEVEL,
    );
  }

  log(...args) {
    args[0] = this.__formatMessage(args[0]);
    super.log.apply(this, args);
  }

  error(...args) {
    args[0] = this.__formatMessage(args[0]);
    super.error.apply(this, args);
  }

  warn(...args) {
    args[0] = this.__formatMessage(args[0]);
    super.warn.apply(this, args);
  }

  debug(...args) {
    args[0] = this.__formatMessage(args[0]);
    super.debug.apply(this, args);
  }

  verbose(...args) {
    args[0] = this.__formatMessage(args[0]);
    super.verbose.apply(this, args);
  }

  private __formatMessage(message: string) {
    if (this.cls.get('correlationId')) {
      return `${this.cls.get('correlationId')} ${this.cls.get('requestId')} ${message}`;
    }
    return message;
  }
}
