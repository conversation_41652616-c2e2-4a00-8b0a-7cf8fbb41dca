import { Global, Module } from '@nestjs/common';
import { ClsModule } from 'nestjs-cls';
import { CommonConsoleLogger } from './console.logger';
import { HttpContextInterceptor } from './interceptors/http.interceptor';

@Global()
@Module({
  imports: [
    ClsModule.forRoot({
      global: true,
      interceptor: {
        mount: true,
      },
    }),
  ],
  providers: [CommonConsoleLogger, HttpContextInterceptor],
  exports: [ClsModule, CommonConsoleLogger, HttpContextInterceptor],
})
export class LoggingModule {}
