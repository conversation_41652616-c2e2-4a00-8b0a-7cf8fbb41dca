import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';
import { EmailService } from './email/email.service';
import { SendGridClient } from './email/sendgrid-client';
import { HealthModule } from './health/health.module';
import { LoggingModule } from './logging/logging.module';
import { SeedModule } from './seed/seed.module';
@Global()
@Module({
  imports: [LoggingModule, SeedModule, HealthModule, CqrsModule],
  exports: [LoggingModule, SeedModule, HealthModule, CqrsModule, EmailService],
  providers: [EmailService, SendGridClient],
})
export class CommonModule { }
