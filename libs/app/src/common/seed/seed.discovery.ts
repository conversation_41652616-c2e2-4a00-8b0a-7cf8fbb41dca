import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModulesContainer } from '@nestjs/core';
import { SEED_METADATA, SEED_OPTIONS } from './seed.decorator';
import { ISeedReference } from './seed.interface';

@Injectable()
export class SeedDiscovery {
  private readonly logger = new Logger(SeedDiscovery.name);
  constructor(
    private readonly modulesContainer: ModulesContainer,
    private readonly configService: ConfigService,
  ) {}

  async runSeeds(): Promise<void> {
    const startAt = process.hrtime();
    const seeds = this.getSeeds();
    const orderedSeeds = this.orderSeedsForExecution(seeds);

    for (const seed of orderedSeeds) {
      const contextShouldRun = seed.context.includes(
        this.configService.get('NODE_ENV'),
      );
      const instanceRunnable = seed.instance && seed.instance.up;

      if (instanceRunnable && contextShouldRun) {
        this.logger.log(`[UP] Executing seed: ${seed.name}`);
        await seed.instance.up();
      } else {
        this.logger.log(`[UP] Skipping seed: ${seed.name}`);
      }
    }
    this.logger.log(
      `Seeds execution finished in ${process.hrtime(startAt)}ms!`,
    );
  }

  async rollbackSeeds(): Promise<void> {
    const seeds = this.getSeeds();
    const orderedSeeds = this.orderSeedsForExecution(seeds).reverse();

    for (const seed of orderedSeeds) {
      const contextShouldRun = seed.context.includes(
        this.configService.get('NODE_ENV'),
      );
      const instanceRunnable = seed.instance && seed.instance.up;

      if (instanceRunnable && contextShouldRun) {
        this.logger.log(`[DOWN] Executing seed: ${seed.name}`);
        await seed.instance.down();
      } else {
        this.logger.log(`[DOWN] Skipping seed: ${seed.name}`);
      }
    }
  }

  private getSeeds(): ISeedReference[] {
    const modules = [...this.modulesContainer.values()];
    const providers = modules
      .map((module) => [...module.providers.values()])
      .reduce((a, b) => a.concat(b), []);
    const seeds = providers
      .map((provider) => {
        const { instance, metatype } = provider;
        if (!instance || !metatype) return [];

        const metadata: SEED_OPTIONS = Reflect.getMetadata(
          SEED_METADATA,
          metatype,
        );
        if (metadata)
          return [
            {
              ...metadata,
              instance,
            },
          ];
      })
      .reduce((a, b) => a.concat(b), []);

    return seeds.filter((seed) => !!seed) as ISeedReference[];
  }

  private orderSeedsForExecution(seeds: ISeedReference[]): ISeedReference[] {
    const visited: { [key: string]: boolean } = {};
    const stack = [];

    const visit = (seed: ISeedReference, ancestors: string[] = []) => {
      if (!Array.isArray(ancestors)) ancestors = [];
      if (!seed) throw Error(`Cannot find seed: ${seed.name}!`);

      ancestors.push(seed.name);
      visited[seed.name] = true;

      const dependencies = seed.depends_on || [];

      for (const dependency of dependencies) {
        if (ancestors.indexOf(dependency) >= 0) {
          throw new Error(
            `Circular dependency "${dependency}" is required by "${seed.name}"`,
          );
        }

        if (!visited[dependency]) {
          visit(
            seeds.find((s) => s.name === dependency),
            ancestors.slice(),
          );
        }
      }

      if (stack.indexOf(seed.name) < 0) {
        stack.push(seed);
      }
    };

    seeds.forEach((seed) => {
      if (!visited[seed.name]) visit(seed);
    });

    return stack;
  }
}
