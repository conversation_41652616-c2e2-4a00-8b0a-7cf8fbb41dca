import { normalizeDateRange } from '@goteacher/app/utility';

export function NormalizeDateRange() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const query = args[0];
      if (query && query.fromDate && query.toDate) {
        const { fromDate, toDate } = normalizeDateRange(
          query.fromDate,
          query.toDate,
        );
        query.fromDate = fromDate;
        query.toDate = toDate;
      }
      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}
