import { formatSecondsToHms } from '@goteacher/app/utility/time';
import Handlebars, { compile } from 'handlebars';
import puppeteer, { PDFOptions, PuppeteerLaunchOptions } from 'puppeteer';

export type handlebarsHelperFunction = (value: any, options: any) => any;

export type handlebarsHelperType = {
  name: string;
  func: handlebarsHelperFunction;
};

export const generateHtml = async (
  template: string,
  options: { data: any },
  customFunction: handlebarsHelperType[] = [],
) => {
  const { data } = options;

  customFunction?.forEach(({ func, name }) => {
    Handlebars.registerHelper(name, func);
  });

  const compiledTemplate = compile(template);
  const renderedHtml = compiledTemplate(data ?? {});
  return renderedHtml;
};

export const generatePdf = async (
  html: string,
  options: { launchOptions: PuppeteerLaunchOptions; pdfOptions: PDFOptions },
) => {
  const { launchOptions, pdfOptions } = options;
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-dev-shm-usage'],
    ...(launchOptions ?? {}),
  });

  let pdfBuffer: Buffer;

  try {
    const page = await browser.newPage();
    await page.setContent(html, {
      waitUntil: 'networkidle0',
    });

    await page.emulateMediaType('print');

    pdfBuffer = await page.pdf({
      format: 'A4',
      displayHeaderFooter: false,
      printBackground: true,
      margin: {
        top: '10px',
      },
      ...(pdfOptions ?? {}),
    });
  } catch (e) {
    console.log(e);
  } finally {
    await browser.close();
  }
  return pdfBuffer;
};

export const columnTransformMap = {
  '#': (value) => value,
  domain: (value) => value,
  page_views: (value) => Number(value),
  active_users: (value) => Number(value),
  active_users_student: (value) => Number(value),
  active_users_teacher: (value) => Number(value),
  count_sessions: (value) => Number(value),
  count_sessions_student: (value) => Number(value),
  count_sessions_teacher: (value) => Number(value),
  sum_time_spent: (value) => formatSecondsToHms(value),
  sum_time_spent_student: (value) => formatSecondsToHms(value),
  sum_time_spent_teacher: (value) => formatSecondsToHms(value),
  avg_time_spent: (value) => formatSecondsToHms(value),
  avg_time_spent_student: (value) => formatSecondsToHms(value),
  avg_time_spent_teacher: (value) => formatSecondsToHms(value),
};

export const columnMap = {
  '#': '#',
  domain: 'Platform',
  page_views: 'Page Views',
  page_views_student: 'Page Views (Student)',
  page_views_teacher: 'Page Views (Teacher)',
  active_users: 'Active Users',
  active_users_student: 'Active Students',
  active_users_teacher: 'Active Teachers',
  count_sessions: 'Sessions',
  count_sessions_student: 'Sessions (Student)',
  count_sessions_teacher: 'Sessions (Teacher)',
  sum_time_spent: 'Total Time',
  sum_time_spent_student: 'Total Time (Student)',
  sum_time_spent_teacher: 'Total Time (Teacher)',
  avg_time_spent: 'Average Time',
  avg_time_spent_student: 'Average Time (Student)',
  avg_time_spent_teacher: 'Average Time (Teacher)',
};
