import { Transform } from 'class-transformer';
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';

export function TransformToBoolean() {
  return Transform(({ value }) => !!JSON.parse(String(value).toLowerCase()));
}

export const sqlSanitizer = (str: string) => str.replace(/[\\$'"]/g, '\\$&');

export const IsRegexMatch = (
  regex: RegExp,
  validationOptions?: ValidationOptions,
) => {
  return (object: object, propertyName: string) => {
    registerDecorator({
      validator: {
        validate(
          value: any,
          validationArguments?: ValidationArguments,
        ): Promise<boolean> | boolean {
          const [validationRegex] = validationArguments.constraints;
          const relatedValue = (validationArguments.object as unknown)[
            propertyName
          ];
          return new RegExp(validationRegex).test(relatedValue);
        },
      },
      name: 'IsRegexMatch',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [regex],
      options: validationOptions,
    });
  };
};

export const IsComparable = (
  property: string,
  compareFunction: (v1: unknown, v2: unknown) => boolean,
  validationOptions?: ValidationOptions,
) => {
  return (object: object, propertyName: string) => {
    registerDecorator({
      validator: {
        validate(
          value: unknown,
          validationArguments?: ValidationArguments,
        ): Promise<boolean> | boolean {
          const relatedValue = (validationArguments.object as unknown)[
            property
          ];
          return compareFunction(value, relatedValue);
        },
      },
      name: 'IsComparable',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
    });
  };
};
