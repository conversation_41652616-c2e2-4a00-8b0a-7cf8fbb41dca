import { Duration } from 'luxon';

export const formatSecondsToHms = (seconds: number | string): string => {
  const duration = Duration.fromObject({ seconds: Number(seconds) });
  if (duration.as('hours') >= 1) {
    // Format as hours and minutes
    return duration.toFormat("h 'hr' m 'min'");
  } else {
    // Format as minutes and seconds
    return duration.toFormat("m 'min' s 'sec");
  }
};

export const timezoneOffset = (timeZone: string): number => {
  try {
    // Current date-time in UTC
    const nowUTC = new Date();

    // Formatting the current UTC date-time to the specific timezone's local time
    const formattedTimezoneTime = new Intl.DateTimeFormat('en-US', {
      timeZone: timeZone,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    }).format(nowUTC);

    const timezoneDate = new Date(
      `${nowUTC.toISOString().split('T')[0]}T${formattedTimezoneTime}`,
    );

    // Calculating the difference in milliseconds
    const difference = timezoneDate.getTime() - nowUTC.getTime();

    // Convert the difference to hours, minutes, and seconds
    const hours = difference / (1000 * 60 * 60);
    const absoluteHours = Math.floor(hours);

    // Wrap into -12 to 12
    const wrappedHours =
      absoluteHours > 12
        ? absoluteHours - 24
        : absoluteHours < -12
          ? absoluteHours + 24
          : absoluteHours;

    return wrappedHours;
  } catch (error) {
    return 0;
  }
};

export function isMoreThanTwoWeeks(date1, date2) {
  const msPerDay = 24 * 60 * 60 * 1000;
  const twoWeeksInMs = 14 * msPerDay;

  const diffInMs = Math.abs(date1 - date2);

  return diffInMs > twoWeeksInMs;
}

export function getAcademicYear(date: Date) {
  const year = date.getFullYear();
  const month = date.getMonth(); // Months are zero-indexed: 0 = January, 11 = December

  if (month >= 6 && month <= 11) {
    return `${year}_${year + 1}`;
  } else if (month >= 0 && month <= 5) {
    return `${year - 1}_${year}`;
  } else if (month === 6) {
    // Obsolete - To be refactored
    return `${year - 1}_${year}_summer`;
  }
}

export function normalizeDateRange(fromDate?: Date, toDate?: Date) {
  if (fromDate) {
    fromDate.setHours(0, 0, 0, 0);
  }
  if (toDate) {
    toDate.setHours(23, 59, 59, 0);
  }
  return { fromDate, toDate };
}
