import { LicensableGrades } from "@goteacher/app/models/mongo";
import { GradeGroup } from "@goteacher/app/models/mongo/contract.model";

export const convertGradeGroupsToGrades = (gradeGroups: GradeGroup[]): LicensableGrades[] => {
  const gradesMap = {
    [GradeGroup.KINDERGARDEN]: [LicensableGrades.KG],
    [GradeGroup.ELEMENTARY]: [
      LicensableGrades.FIRST,
      LicensableGrades.SECOND,
      LicensableGrades.THIRD,
      LicensableGrades.FOURTH,
      LicensableGrades.FIFTH,
    ],
    [GradeGroup.MIDDLE]: [
      LicensableGrades.SIXTH,
      LicensableGrades.SEVENTH,
      LicensableGrades.EIGHTH,
    ],
    [GradeGroup.HIGH_SCHOOL]: [
      LicensableGrades.NINTH,
      LicensableGrades.TENTH,
      LicensableGrades.ELEVENTH,
      LicensableGrades.TWELFTH,
    ],
  };

  return [...new Set(gradeGroups.flatMap(group => gradesMap[group]))];
}