import { Type } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type as TransformType } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsEnum,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { toLower, toNumber, toUpper } from 'lodash';
import { SortOrder } from 'mongoose';
import { OrderItem } from 'sequelize';

export class OrderByRequest {
  @ApiProperty()
  @IsEnum(['asc', 'desc'])
  orderDirection: 'asc' | 'desc' = 'asc';

  @ApiProperty()
  @IsString()
  orderBy: string;
}

export const parseOrderBy = (orderBy: Array<OrderByRequest>): OrderItem[] => {
  return orderBy.map((item) => {
    return [item.orderBy, toUpper(item.orderDirection)];
  });
};

export const parseOrderByMongoose = (
  orderBy: Array<OrderByRequest>,
): [string, SortOrder][] => {
  return orderBy.map((item) => {
    return [item.orderBy, toLower(item.orderDirection)];
  }) as [string, SortOrder][];
};

export class PaginationRequest {
  constructor(obj: PaginationRequest = { offset: 0, limit: 100 }) {
    if (obj) Object.assign(this, obj);
  }

  @ApiProperty({
    required: false,
    default: 0,
    description: 'Pagination offset',
  })
  @Transform(({ value }) => (value ? toNumber(value) : 0))
  @IsOptional()
  @Min(0)
  offset: number;

  @ApiProperty({
    required: false,
    default: 100,
    description: 'Pagination limit',
  })
  @Transform(({ value }) => (value ? toNumber(value) : 100))
  @IsOptional()
  @Max(1000)
  limit: number;

  @ApiProperty({
    required: false,
    description: 'Order by',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMaxSize(5)
  @TransformType(() => OrderByRequest)
  order?: OrderByRequest[];
}

export class PaginationResponse<T> {
  @ApiProperty({ description: 'Array of items' })
  data: T[];

  @ApiProperty({ description: 'Total number of items', type: Number })
  total: number;

  @ApiProperty({ description: 'Pagination limit', type: Number })
  limit: number;

  @ApiProperty({ description: 'Pagination offset', type: Number })
  offset: number;
}

export function createPaginationResponseDto<T>(itemType: Type<T>): any {
  class PaginationResponseDto {
    @ApiProperty({ type: [itemType], description: 'Array of items' })
    @IsArray()
    @TransformType(() => itemType)
    data: T[];

    @ApiProperty({
      description: 'Total number of items',
    })
    total: number;

    @ApiProperty({
      description: 'Pagination limit',
    })
    limit: number;

    @ApiProperty({
      description: 'Pagination offset',
    })
    offset: number;
  }

  // Assign a unique name to the class
  Object.defineProperty(PaginationResponseDto, 'name', {
    value: `PaginationResponseDto_${itemType.name}`,
  });

  return PaginationResponseDto;
}
