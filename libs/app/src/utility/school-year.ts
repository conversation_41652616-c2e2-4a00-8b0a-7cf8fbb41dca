/**
 * School Year Utility Functions
 * 
 * Handles automatic calculation of school years based on the academic calendar.
 * School year transition date: June 20th
 * - Before/on June 20th: Previous year to current year (e.g., 2024_2025)
 * - After June 20th: Current year to next year (e.g., 2025_2026)
 */

/**
 * Calculate the current school year based on the current date
 * @param date Optional date to calculate from (defaults to current date)
 * @returns School year in format YYYY_YYYY (e.g., "2024_2025")
 */
export function calculateCurrentSchoolYear(date?: Date): string {
  const currentDate = date || new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth(); // 0-based (0 = January)
  const currentDay = currentDate.getDate();
  
  // June 20th cutoff (month 5 = June in 0-based indexing)
  const isAfterJune20 = currentMonth > 5 || (currentMonth === 5 && currentDay > 20);
  
  if (isAfterJune20) {
    // After June 20th: current year to next year
    return `${currentYear}_${currentYear + 1}`;
  } else {
    // Before/on June 20th: previous year to current year
    return `${currentYear - 1}_${currentYear}`;
  }
}

/**
 * Parse a school year string into start and end years
 * @param schoolYear School year string (e.g., "2024_2025")
 * @returns Object with startYear and endYear as numbers
 */
export function parseSchoolYear(schoolYear: string): { startYear: number; endYear: number } {
  const parts = schoolYear.split('_');
  if (parts.length !== 2) {
    throw new Error(`Invalid school year format: ${schoolYear}. Expected format: YYYY_YYYY`);
  }
  
  const startYear = parseInt(parts[0], 10);
  const endYear = parseInt(parts[1], 10);
  
  if (isNaN(startYear) || isNaN(endYear)) {
    throw new Error(`Invalid school year format: ${schoolYear}. Years must be numeric.`);
  }
  
  if (endYear - startYear !== 1) {
    throw new Error(`Invalid school year format: ${schoolYear}. End year must be exactly one year after start year.`);
  }
  
  return { startYear, endYear };
}

/**
 * Validate if a school year string is in the correct format
 * @param schoolYear School year string to validate
 * @returns true if valid, false otherwise
 */
export function isValidSchoolYear(schoolYear: string): boolean {
  try {
    parseSchoolYear(schoolYear);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get the next school year after the given one
 * @param schoolYear Current school year (e.g., "2024_2025")
 * @returns Next school year (e.g., "2025_2026")
 */
export function getNextSchoolYear(schoolYear: string): string {
  const { startYear, endYear } = parseSchoolYear(schoolYear);
  return `${endYear}_${endYear + 1}`;
}

/**
 * Get the previous school year before the given one
 * @param schoolYear Current school year (e.g., "2024_2025")
 * @returns Previous school year (e.g., "2023_2024")
 */
export function getPreviousSchoolYear(schoolYear: string): string {
  const { startYear, endYear } = parseSchoolYear(schoolYear);
  return `${startYear - 1}_${startYear}`;
}

/**
 * Check if a given date falls within a specific school year
 * @param date Date to check
 * @param schoolYear School year to check against
 * @returns true if the date falls within the school year period
 */
export function isDateInSchoolYear(date: Date, schoolYear: string): boolean {
  const { startYear, endYear } = parseSchoolYear(schoolYear);
  
  // School year starts after June 20th of start year
  const schoolYearStart = new Date(startYear, 5, 21); // June 21st
  // School year ends on June 20th of end year
  const schoolYearEnd = new Date(endYear, 5, 20); // June 20th
  
  return date >= schoolYearStart && date <= schoolYearEnd;
}