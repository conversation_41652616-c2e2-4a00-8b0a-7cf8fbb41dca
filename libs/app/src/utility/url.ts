export const cleanUrl = (url: string): string => {
  try {
    const urlObject = new URL(url);
    urlObject.hash = '';
    return urlObject.toString();
  } catch (error) {
    return url;
  }
};

/**
 * Extracts the domain from a URL, removing protocol, www, and path
 * Examples:
 * - https://www.google.com/path -> google.com
 * - http://subdomain.example.com -> subdomain.example.com
 * - https://example.co.uk -> example.co.uk
 * - invalid-url -> null
 */
export const extractDomainFromUrl = (url: string): string | null => {
  if (!url) return null;
  
  try {
    // Add protocol if missing
    let processedUrl = url;
    if (!url.match(/^https?:\/\//)) {
      processedUrl = `https://${url}`;
    }
    
    const urlObject = new URL(processedUrl);
    let hostname = urlObject.hostname;
    
    // Remove www. prefix if present
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    return hostname;
  } catch (error) {
    // If URL parsing fails, try basic string manipulation
    let domain = url;
    
    // Remove protocol
    domain = domain.replace(/^https?:\/\//, '');
    
    // Remove www.
    domain = domain.replace(/^www\./, '');
    
    // Remove path, query, and hash
    domain = domain.split('/')[0];
    domain = domain.split('?')[0];
    domain = domain.split('#')[0];
    
    // Basic validation - must contain at least one dot
    if (domain && domain.includes('.')) {
      return domain;
    }
    
    return null;
  }
};
