export type CalculatedTerm = {
  costBreakdown: {
    userTotal: number;
    costPerHour: number;
    subscriptionCost: number;
    totalCost: number;
    licenses?: number;
    activeLicenses?: number;
    inactiveLicenses?: number;
    extraLicenses?: number;
    licensesDetails?: {
      licenses: number;
      licenses_20: number;
      licenses_20_min_plus: number;
      licenses_no_login: number;
      licenses_untrackable: number;
      licenses_unaccounted: number;
    };
    costPerLicense?: number;
    costPerUser?: number;
    costPerBucket_20_min?: number;
    costPerBucket_20_min_plus?: number;
    costPerBucket_no_login?: number;
    costSavings?: number;
    trackableCost?: number;
    trackableDetails?: {
      trackableStudent_20_min: number;
      trackableStudent_20_min_plus: number;
      trackableStudent_no_login: number;
      trackableTeacher_20_min: number;
      trackableTeacher_20_min_plus: number;
      trackableTeacher_no_login: number;
      trackableUser_20_min: number;
      trackableUser_20_min_plus: number;
      trackableUser_no_login: number;
    };
    trackableLicenses?: number;
    costSavingsPercentage?: number;
    unTrackableLicenses?: number;
    roi?: number;
  };
};
