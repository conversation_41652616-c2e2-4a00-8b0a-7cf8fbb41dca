import { ListContractQuery } from '@goteacher/app/financial/query/list-contract/query';
import { FinancialService } from '@goteacher/app/financial/service/financial.service';
import { Contract } from '@goteacher/app/models/mongo';
import {
  PaginationResponse,
  parseOrderByMongoose,
} from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import crypto from 'crypto';
import { Model } from 'mongoose';

@QueryHandler(ListContractQuery)
export class ListContractQueryHandler
  implements IQueryHandler<ListContractQuery> {
  constructor(
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    private readonly financialService: FinancialService,
    private readonly cacheService: ICacheService,
  ) { }

  async execute(query: ListContractQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const cachingKey = `financial_${userOrganizationIds[0]}_list_contracts_${this.getCachingId(query)}`;
    const cacheResult =
      await this.cacheService.get<PaginationResponse<Contract>>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const orderBy = parseOrderByMongoose(
      query.order ?? [{ orderBy: 'number', orderDirection: 'desc' }],
    );

    let whereClause: any = {
      organizationId: { $in: userOrganizationIds },
      deleted: false,
    };

    if (query.search) {
      whereClause = {
        ...whereClause,
        $or: [
          { title: { $regex: query.search, $options: 'i' } },
          { domain: { $regex: query.search, $options: 'i' } },
          { productName: { $regex: query.search, $options: 'i' } },
        ],
      };
    }

    if (query.domains) {
      whereClause = {
        ...whereClause,
        domain: { $in: query.domains },
      };
    }

    if (query.fromDate || query.toDate) {
      // Changed filter logic to intersect with subscriptionStartDate and subscriptionEndDate
      whereClause.$and = [
        {
          $and: [
            { subscriptionStartDate: { $lt: new Date(query.toDate) || new Date('9999-12-31') } },
            { subscriptionEndDate: { $gt: new Date(query.fromDate) || new Date('1970-01-01') } }
          ]
        }
      ];
    }

    if (query.active !== undefined) {
      whereClause = {
        ...whereClause,
        subscriptionStartDate: { $lte: new Date() },
        subscriptionEndDate: { $gte: new Date() },
        active: query.active,
      };
    }

    const contractCount = await this.contractModel.countDocuments(whereClause);
    const contracts = await this.contractModel
      .find(whereClause)
      .limit(query.limit)
      .skip(query.offset)
      .sort(orderBy);

    if (!query.withCostBreakdown)
      return {
        data: contracts,
        total: contractCount,
        limit: query.limit,
        offset: query.offset,
      };

    const financialContracts = await Promise.all(
      contracts.map((contract) =>
        this.financialService.calculateContract(contract),
      ),
    );

    const response = {
      data: financialContracts,
      total: contractCount,
      limit: query.limit,
      offset: query.offset,
    };

    this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  getCachingId(query: ListContractQuery) {
    const content = JSON.stringify({
      search: query.search,
      domains: query.domains,
      limit: query.limit,
      offset: query.offset,
      fromDate: query.fromDate ? query.fromDate.toISOString() : null,
      toDate: query.toDate ? query.toDate.toISOString() : null,
      active: query.active,
      withCostBreakdown: query.withCostBreakdown,
      order: query.order,
    });

    return crypto.createHash('md5').update(content).digest('hex');
  }
}
