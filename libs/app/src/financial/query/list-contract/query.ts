import { ReqContext } from '@goteacher/app/auth';
import { PaginationRequest } from '@goteacher/app/utility';

export class ListContractQuery extends PaginationRequest {
  constructor(obj: ListContractQuery) {
    super(obj);
    Object.assign(this, obj);
  }

  search?: string;
  domains?: string[];
  fromDate?: Date;
  toDate?: Date;
  active?: boolean;
  withCostBreakdown?: boolean = false;
  forceRefresh?: boolean = false;

  ctx: ReqContext;
}
