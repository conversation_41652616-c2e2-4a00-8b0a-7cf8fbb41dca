import { UserGroup } from '@goteacher/app/analytics/types';
import { ReqContext } from '@goteacher/app/auth';
import { PaginationRequest } from '@goteacher/app/utility';

export class GetContractUsersQuery extends PaginationRequest {
  constructor(obj: GetContractUsersQuery) {
    super(obj);
    Object.assign(this, obj);
  }

  contractId: string;
  ctx: ReqContext;

  search?: string;
  userGroup?: UserGroup = UserGroup.BOTH;
  forceRefresh?: boolean = false;
}
