import { EnrichmentColumn, EnrichmentService } from '@goteacher/app/analytics';
import { GetContractUsersQuery } from '@goteacher/app/financial/query/get-contract-users/query';
import { FinancialService } from '@goteacher/app/financial/service/financial.service';
import { AudienceType, Contract } from '@goteacher/app/models/mongo';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { PaginationResponse, parseOrderBy } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger, NotFoundException } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel as InjectModelMongoose } from '@nestjs/mongoose';
import { InjectModel as InjectModelSequelize } from '@nestjs/sequelize';
import { TopUsersResponse } from 'apps/api/src/api/analytic/data/res.dto';
import { Model } from 'mongoose';
import { Op } from 'sequelize';

@QueryHandler(GetContractUsersQuery)
export class GetContractUsersQueryHandler
  implements IQueryHandler<GetContractUsersQuery> {
  private readonly logger = new Logger(GetContractUsersQueryHandler.name);

  constructor(
    @InjectModelMongoose(Contract.name) private readonly contractModel: Model<Contract>,
    private cacheService: ICacheService,
    private enrichmentService: EnrichmentService,
    private financialService: FinancialService,
    @InjectModelSequelize(User) private userModel: typeof User,
    @InjectModelSequelize(School) private readonly schoolModel: typeof School,
  ) { }

  async execute(query: GetContractUsersQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const contract = await this.contractModel.findOne({
      _id: query.contractId,
      organizationId: { $in: userOrganizationIds },
      deleted: false,
    });

    if (!contract) {
      throw new NotFoundException('Contract not found!');
    }


    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    const cachingKey = `financial_${query.contractId}_users_${query.search}_${query.limit}_${query.offset}_${JSON.stringify(orderBy)}`;
    const cacheResult =
      await this.cacheService.get<PaginationResponse<TopUsersResponse>>(
        cachingKey,
      );

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const { data, rows_before_limit_at_least } = await this.financialService.getContractUsers(
      contract,
      userOrganizationIds,
      orderBy,
      query.search,
      query.limit,
      query.offset,
    );

    const enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.USER
    ]);

    const response: PaginationResponse<TopUsersResponse> = {
      data: enrichedData as unknown as TopUsersResponse[],
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  async getSearchableUserIds(
    orgIds: string[],
    query: GetContractUsersQuery,
  ): Promise<string[]> {
    if (!query.search) return [];

    const schoolIds = await this.schoolModel.findAll({
      where: {
        organisationId: {
          [Op.in]: orgIds,
        },
      },
      attributes: ['id'],
    });

    const userIds = await this.userModel.findAll({
      where: {
        [Op.or]: [
          {
            email: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            firstName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            lastName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
        ],
      },
      attributes: ['id'],
      include: [
        {
          model: UserSchool,
          where: {
            schoolId: {
              [Op.in]: schoolIds.map((s) => s.id),
            },
          },
          include: [
            {
              model: School,
              where: {
                organisationId: {
                  [Op.in]: orgIds,
                },
              },
            },
          ],
        },
      ],
    });

    return userIds.map((u) => u.id);
  }

  getVisitsSQL(
    contract: Contract,
    orgIds: string[],
    searchableUserIds: string[],
  ): string {
    const orgId = orgIds[0];
    const { domain, productId, subscriptionStartDate, subscriptionEndDate, terms: [term] } = contract
    const { audience } = term
    return `
      SELECT 
        st.sessionId AS sessionId,
        st.tabId AS tabId,
        st.userId AS userId,
        COALESCE(u.orgId, st.orgId) AS orgId,
        COALESCE(u.schoolId, st.schoolId) AS schoolId,
        COALESCE(u.role, st.role) AS role,
        COALESCE(u.grade, st.grade) AS grade,
        ${productId ? 'st.productId AS productId,' : 'st.domain AS domain,'}
        st.url AS url,
        min(st.visit_start) AS visit_start,
        max(st.visit_end) AS visit_end,
        any(st.visit_timezone) AS visit_timezone,
        dateDiff('s', visit_start, visit_end) AS time_spent        
      FROM goteacher.visits_sc as st
      LEFT JOIN goteacher.users u on u.userId = st.userId
      WHERE 
        orgId = '${orgId}'
        ${productId ? `AND st.productId = '${productId}'` : `AND st.domain = '${domain}'`}            
        ${audience?.schools.length ? `AND schoolId IN (${audience?.schools.map((s) => `'${s}'`).join(',')})` : ''}
        ${audience?.grades.length ? `AND grade IN (${audience?.grades.map((s) => `'${s}'`).join(',')})` : ''}
        ${audience.type === AudienceType.TEACHER ? `AND role = 'teacher'` : ''}
        ${audience.type === AudienceType.STUDENT ? `AND role = 'student'` : ''}
        AND toDateTime(toStartOfWeek(st.visit_start)) >= ${Math.round(new Date(subscriptionStartDate).getTime() / 1000)}
        AND toDateTime(toStartOfWeek(st.visit_start)) <= ${Math.round(new Date(subscriptionEndDate).getTime() / 1000)}            
        ${searchableUserIds.length ? `AND st.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''} 
      GROUP BY
          sessionId,
          tabId,
          userId,
          orgId,
          schoolId,
          role,
          grade,
          ${productId ? 'productId,' : 'domain,'}
          url             
     `;
  }

  getDailySessionsSQL(contract: Contract): string {
    const { domain, productId, subscriptionStartDate, subscriptionEndDate, terms: [term] } = contract
    const { audience } = term
    return `
      SELECT 
        userId,
        schoolId,
        orgId,
        grade,        
        ${productId ? 'productId,' : 'domain,'}        
        avgOrNull(time_spent) AS avg_time_spent,
        sumOrDefault(time_spent) AS sum_time_spent,
        uniqState(sessionId) AS count_sessions
    FROM session_metrics
    GROUP BY
        userId,
        ${productId ? 'productId,' : 'domain,'}
        schoolId,
        orgId,
        grade        
    `;
  }
}
