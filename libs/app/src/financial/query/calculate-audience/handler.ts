import { GetAudienceQuery } from '@goteacher/app/financial/query/calculate-audience/query';
import { FinancialService } from '@goteacher/app/financial/service/financial.service';
import { LicensableGrades } from '@goteacher/app/models/mongo';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

@QueryHandler(GetAudienceQuery)
export class GetAudienceQueryHandler
  implements IQueryHandler<GetAudienceQuery>
{
  constructor(private readonly financialService: FinancialService) {}

  async execute(query: GetAudienceQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    return await this.financialService.calculateAudience({
      orgId: userOrganizationIds[0],
      schoolIds: query.schoolIds,
      grades: query.grades as LicensableGrades[],
      userGroup: query.userGroup,
    });
  }
}
