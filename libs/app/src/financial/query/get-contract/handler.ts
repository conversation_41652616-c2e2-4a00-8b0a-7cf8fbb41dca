import { GetContractQuery } from '@goteacher/app/financial/query/get-contract/query';
import { FinancialService } from '@goteacher/app/financial/service/financial.service';
import { Contract } from '@goteacher/app/models/mongo';
import { NotFoundException } from '@nestjs/common';
import { I<PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(GetContractQuery)
export class GetContractQueryHandler
  implements IQueryHandler<GetContractQuery>
{
  constructor(
    @InjectModel(Contract.name)
    private readonly contractModel: Model<Contract>,
    private readonly financialService: FinancialService,
  ) {}

  async execute(query: GetContractQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const contract = await this.contractModel.findOne({
      _id: query.contractId,
      organizationId: { $in: userOrganizationIds },
      deleted: false,
    });

    if (!contract) {
      throw new NotFoundException('Contract not found!');
    }

    if (!query.withCostBreakdown) return contract;

    const financialContract =
      await this.financialService.calculateContract(contract);

    return financialContract;
  }
}
