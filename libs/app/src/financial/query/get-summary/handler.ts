import { GetSummaryQuery } from '@goteacher/app/financial/query/get-summary/query';
import { FinancialService } from '@goteacher/app/financial/service/financial.service';
import { Contract } from '@goteacher/app/models/mongo';
import { ICacheService } from '@goteacher/infra/cache';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import crypto from 'crypto';
import { Model } from 'mongoose';

@QueryHandler(GetSummaryQuery)
export class GetSummaryQueryHandler implements IQueryHandler<GetSummaryQuery> {
  constructor(
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    private readonly cacheService: ICacheService,
    private readonly financialService: FinancialService,
  ) { }

  async execute(query: GetSummaryQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const cachingKey = `financial_${userOrganizationIds[0]}_get-summary_${this.getCachingId(query)}`;
    const cacheResult = await this.cacheService.get<{
      totalCost: number;
      NumberOfContracts: number;
      totalSavings: number;
      userCount: number;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const whereClause: any = {
      organizationId: { $in: userOrganizationIds },
      // active: true,
      deleted: false,
    };
    if (query.fromDate || query.toDate) {
      // Changed filter logic to intersect with subscriptionStartDate and subscriptionEndDate
      whereClause.$and = [
        {
          $and: [
            { subscriptionStartDate: { $lte: new Date(query.toDate) || new Date('9999-12-31') } },
            { subscriptionEndDate: { $gte: new Date(query.fromDate) || new Date('1970-01-01') } }
          ]
        }
      ];
    }

    const allContracts = await this.contractModel.find(whereClause);

    const allContractsWithLicenses = await Promise.all(
      allContracts.map((contract) =>
        this.financialService.calculateContract(contract),
      ),
    );

    const totalCost = allContractsWithLicenses.reduce(
      (acc, curr) =>
        acc +
        curr.terms.reduce(
          (acc2, curr2) => acc2 + curr2.costBreakdown.subscriptionCost || 0,
          0,
        ),
      0,
    );

    const totalSavings = allContractsWithLicenses.reduce(
      (acc, curr) =>
        acc +
        curr.terms.reduce(
          (acc2, curr2) => acc2 + (curr2.costBreakdown.costSavings || 0),
          0,
        ),
      0,
    );

    const userPerSchoolPerGrade =
      await this.financialService.countTotalUsersPerSchoolPerGrade(
        userOrganizationIds[0],
      );
    const userCount = userPerSchoolPerGrade.reduce(
      (acc, curr) => acc + curr.total,
      0,
    );

    const response = {
      numberOfContracts: allContracts.length,
      totalCost,
      totalSavings,
      userCount,
    };

    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);
    return response;
  }

  getCachingId(query: GetSummaryQuery) {
    const content = JSON.stringify({
      fromDate: query.fromDate ? query.fromDate.toISOString() : null,
      toDate: query.toDate ? query.toDate.toISOString() : null,
    });

    return crypto.createHash('md5').update(content).digest('hex');
  }
}
