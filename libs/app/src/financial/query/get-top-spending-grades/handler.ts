import { GetGradesTopSpendQuery } from '@goteacher/app/financial/query/get-top-spending-grades/query';
import { FinancialService } from '@goteacher/app/financial/service/financial.service';
import { ICacheService } from '@goteacher/infra/cache';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import crypto from 'crypto';

@QueryHandler(GetGradesTopSpendQuery)
export class GetGradesTopSpendQueryHandler
  implements IQueryHandler<GetGradesTopSpendQuery>
{
  constructor(
    private readonly financialService: FinancialService,
    private readonly cacheService: ICacheService,
  ) {}

  async execute(query: GetGradesTopSpendQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const cachingKey = `financial_${userOrganizationIds[0]}_get-top-spending-grades_${this.getCachingId(query)}`;
    const cacheResult = await this.cacheService.get<
      {
        grade: string;
        totalCost: number;
        percentage: number;
      }[]
    >(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const response = await this.financialService.getTopSpendingGrades(
      userOrganizationIds[0],
      query.fromDate,
      query.toDate,
    );

    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  getCachingId(query: GetGradesTopSpendQuery) {
    const content = JSON.stringify({
      fromDate: query.fromDate ? query.fromDate.toISOString() : null,
      toDate: query.toDate ? query.toDate.toISOString() : null,
    });

    return crypto.createHash('md5').update(content).digest('hex');
  }
}
