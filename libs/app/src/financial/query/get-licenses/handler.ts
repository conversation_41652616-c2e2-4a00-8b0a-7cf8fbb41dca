import { GetLicensesQuery } from '@goteacher/app/financial/query/get-licenses/query';
import { FinancialService } from '@goteacher/app/financial/service/financial.service';
import { Contract } from '@goteacher/app/models/mongo';
import { ICacheService } from '@goteacher/infra/cache';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import crypto from 'crypto';
import { Model } from 'mongoose';

@QueryHandler(GetLicensesQuery)
export class GetLicensesQueryHandler
  implements IQueryHandler<GetLicensesQuery> {
  constructor(
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    private readonly cacheService: ICacheService,
    private readonly financialService: FinancialService,
  ) { }

  async execute(query: GetLicensesQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const cachingKey = `financial_${userOrganizationIds[0]}_get-licenses_${this.getCachingId(query)}`;
    const cacheResult = await this.cacheService.get<{
      licenses: number;
      activeLicenses: number;
      inactiveLicenses: number;
      extraLicenses: number;
      licenses_untrackable: number;
      licenses_unaccounted: number;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const whereClause: any = {
      organizationId: { $in: userOrganizationIds },
      // active: true,
      deleted: false,
    };
    if (query.fromDate || query.toDate) {
      // Changed filter logic to intersect with subscriptionStartDate and subscriptionEndDate
      whereClause.$and = [
        {
          $and: [
            { subscriptionStartDate: { $lt: new Date(query.toDate) || new Date('9999-12-31') } },
            { subscriptionEndDate: { $gt: new Date(query.fromDate) || new Date('1970-01-01') } }
          ]
        }
      ];
    }
    const allContracts = await this.contractModel.find(whereClause);

    const allContractsWithLicenses = await Promise.all(
      allContracts.map((contract) =>
        this.financialService.calculateContract(contract),
      ),
    );

    const licenses = allContractsWithLicenses.reduce(
      (acc, curr) => {
        return {
          licenses:
            acc.licenses +
            curr.terms.reduce((acc2, curr2) => {
              acc2 += curr2.costBreakdown.licenses || 0;
              return acc2;
            }, 0),
          activeLicenses:
            acc.activeLicenses +
            curr.terms.reduce((acc2, curr2) => {
              acc2 += curr2.costBreakdown.activeLicenses || 0;
              return acc2;
            }, 0),
          inactiveLicenses:
            acc.inactiveLicenses +
            curr.terms.reduce((acc2, curr2) => {
              acc2 +=
                curr2.costBreakdown.inactiveLicenses || 0;
              return acc2;
            }, 0),
          extraLicenses:
            acc.extraLicenses +
            curr.terms.reduce((acc2, curr2) => {
              acc2 +=
                curr2.costBreakdown.extraLicenses || 0;
              return acc2;
            }, 0),
          licenses_untrackable:
            acc.licenses_untrackable +
            curr.terms.reduce((acc2, curr2) => {
              acc2 +=
                curr2.costBreakdown.licensesDetails?.licenses_untrackable || 0;
              return acc2;
            }, 0),
          licenses_unaccounted:
            acc.licenses_unaccounted +
            curr.terms.reduce((acc2, curr2) => {
              acc2 +=
                curr2.costBreakdown.licensesDetails?.licenses_unaccounted || 0;
              return acc2;
            }, 0),
        };
      },
      {
        licenses: 0,
        activeLicenses: 0,
        inactiveLicenses: 0,
        extraLicenses: 0,
        // licenses_20: 0,
        // licenses_20_min_plus: 0,
        // licenses_no_login: 0,
        licenses_untrackable: 0,
        licenses_unaccounted: 0,
      },
    );

    await this.cacheService.set(cachingKey, licenses, 6 * 60 * 60);

    return licenses;
  }

  getCachingId(query: GetLicensesQuery) {
    const content = JSON.stringify({
      fromDate: query.fromDate ? query.fromDate.toISOString() : null,
      toDate: query.toDate ? query.toDate.toISOString() : null,
    });

    return crypto.createHash('md5').update(content).digest('hex');
  }
}
