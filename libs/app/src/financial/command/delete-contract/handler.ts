import { DeleteContractCommand } from '@goteacher/app/financial/command/delete-contract/command';
import { Contract } from '@goteacher/app/models/mongo';
import { Audit, AuditAction } from '@goteacher/app/models/mongo/audit.model';
import { ICacheService } from '@goteacher/infra/cache';
import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@CommandHandler(DeleteContractCommand)
export class DeleteContractHandler
  implements ICommandHandler<DeleteContractCommand>
{
  constructor(
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    @InjectModel(Audit.name) private readonly auditModel: Model<Audit>,
    private readonly cacheService: ICacheService,
  ) {}

  async execute(command: DeleteContractCommand) {
    const orgIds = command.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const toDeleteContract = await this.contractModel.findOne({
      _id: command.contractId,
      organizationId: { $in: orgIds },
    });

    if (!toDeleteContract) {
      throw new NotFoundException('Contract not found!');
    }

    if (
      toDeleteContract.organizationId !==
      command.ctx.user.UserSchool[0].school.organisationId
    ) {
      throw new ForbiddenException(
        'You do not have permission to delete this contract!',
      );
    }

    await this.contractModel.updateOne(
      {
        _id: command.contractId,
        organizationId: toDeleteContract.organizationId,
      },
      { $set: { deleted: true } },
    );

    this.auditModel.create({
      actorId: command.ctx.user.id,
      action: AuditAction.DELETE,
      contractId: toDeleteContract._id.toString(),
    });

    this.cacheService.invalidate(
      `financial_${toDeleteContract.organizationId}_*`,
    );
  }
}
