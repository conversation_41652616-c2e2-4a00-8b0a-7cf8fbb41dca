import { CreateContractCommand } from '@goteacher/app/financial/command/create-contract/command';
import { Contract } from '@goteacher/app/models/mongo';
import { Audit, AuditAction } from '@goteacher/app/models/mongo/audit.model';
import { convertGradeGroupsToGrades } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@CommandHandler(CreateContractCommand)
export class CreateContractHandler
  implements ICommandHandler<CreateContractCommand> {
  constructor(
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    @InjectModel(Audit.name) private readonly auditModel: Model<Audit>,
    private readonly cacheService: ICacheService,
  ) { }

  async execute(command: CreateContractCommand) {
    const orgId = command.ctx.user.UserSchool[0].school.organisationId;

    const latestContract = await this.contractModel
      .findOne({
        organizationId: orgId,
      })
      .sort({ number: -1 });

    const contractNumber = (latestContract?.number || 0) + 1;
    const contractTitle =
      command.contract.title || `Contract ${contractNumber}`;

    for (const term of command.contract.terms) {
      if (term.audience && term.audience.gradeGroups) {
        term.audience.grades = convertGradeGroupsToGrades(
          term.audience.gradeGroups,
        );
      }
    }

    const contract = await this.contractModel.create({
      ...command.contract,
      organizationId: orgId,
      number: contractNumber,
      title: contractTitle,
    });

    this.auditModel.create({
      actorId: command.ctx.user.id,
      action: AuditAction.CREATE,
      contractId: contract._id.toString(),
    });

    this.cacheService.invalidate(`financial_${orgId}_*`);

    return contract;
  }
}
