import { UpdateContractCommand } from '@goteacher/app/financial/command/update-contract/command';
import { Contract } from '@goteacher/app/models/mongo';
import { Audit, AuditAction } from '@goteacher/app/models/mongo/audit.model';
import { ICacheService } from '@goteacher/infra/cache';
import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@CommandHandler(UpdateContractCommand)
export class UpdateContractCommandHandler
  implements ICommandHandler<UpdateContractCommand> {
  constructor(
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    @InjectModel(Audit.name) private readonly auditModel: Model<Audit>,
    private readonly cacheService: ICacheService,
  ) { }

  async execute(command: UpdateContractCommand) {
    const orgId = command.ctx.user.UserSchool[0].school.organisationId;
    const contract = await this.contractModel.findById(command.contractId);

    if (!contract) {
      throw new NotFoundException('Contract not found');
    }

    if (contract.organizationId !== orgId) {
      throw new ForbiddenException("You don't have access to this contract");
    }

    const contractData = { ...command.contract };

    const updatedContract = await this.contractModel.findByIdAndUpdate(
      { _id: command.contractId },
      contractData,
    );

    this.auditModel.create({
      actorId: command.ctx.user.id,
      action: AuditAction.UPDATE,
      contractId: updatedContract._id.toString(),
    });

    this.cacheService.invalidate(`financial_${orgId}_*`);

    return updatedContract;
  }
}
