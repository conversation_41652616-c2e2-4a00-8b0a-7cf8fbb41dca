import { ReqContext } from '@goteacher/app/auth';
import { Contract } from '@goteacher/app/models/mongo';

export class UpdateContractCommand {
  constructor(obj: UpdateContractCommand) {
    Object.assign(this, obj);
  }

  contractId: string;
  contract: Partial<
    Omit<
      Contract,
      | '_id'
      | 'organizationId'
      | 'domain'
      | 'number'
      | 'createdAt'
      | 'updatedAt'
      | 'deleted'
    >
  >;
  ctx: ReqContext;
}
