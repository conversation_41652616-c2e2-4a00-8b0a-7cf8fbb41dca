import { ExportAnalyticsPdfQuery } from '@goteacher/app/export/query/export-analytics-pdf/query';
import {
  columnMap,
  columnTransformMap,
  generateHtml,
  generatePdf,
} from '@goteacher/app/utility/pdf';
import { IStorageService } from '@goteacher/infra/storage/storage.service';
import { ConfigService } from '@nestjs/config';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { uniq } from 'lodash';

@QueryHandler(ExportAnalyticsPdfQuery)
export class ExportAnalyticsPdfQueryHandler
  implements IQueryHandler<ExportAnalyticsPdfQuery>
{
  constructor(
    private storageService: IStorageService,
    private configService: ConfigService,
  ) {}

  async execute(query: ExportAnalyticsPdfQuery) {
    const [headerHtmlTemplate, tableBodyHtmlTemplate] = await Promise.all([
      this.storageService.getObjectAsString(
        this.configService.get<string>('BLOCKS_BUCKET'),
        'pdf-templates/header.html',
      ),
      this.storageService.getObjectAsString(
        this.configService.get<string>('BLOCKS_BUCKET'),
        'pdf-templates/table-body.html',
      ),
    ]);

    const generateTableHtml = await generateHtml(
      tableBodyHtmlTemplate,
      {
        data: {
          columnNames: this.labelColumns(query.columns),
          rows: this.transformData(query.rows, query.columns),
        },
      },
      [{ name: 'increment', func: (value) => value + 1 }],
    );

    const generatedHeaderHtml = await generateHtml(headerHtmlTemplate, {
      data: {
        title: `Exported Data: ${query.fromDate.toDateString()} - ${query.toDate.toDateString()}`,
        content: generateTableHtml,
      },
    });

    const pdf = await generatePdf(generatedHeaderHtml, {
      launchOptions: {},
      pdfOptions: {},
    });
    return {
      extension: '.pdf',
      fileName: 'ReportExportData',
      fullFileName: 'ReportExportData.pdf',
      file: pdf,
    };
  }

  labelColumns(columns: string[]) {
    const uniqueColumns = uniq(columns);

    return uniqueColumns.map((col) => {
      return columnMap[col] ?? col;
    });
  }

  transformData(data: any[], columns: string[]) {
    const uniqueColumns = uniq(columns);

    return data.map((row) => {
      const transformedRow = {};
      uniqueColumns.forEach((column) => {
        const transform = columnTransformMap[column] || ((value) => value);
        if(!row[column]) return;
        transformedRow[column] = transform(row[column]);
      });
      return transformedRow;
    });
  }
}
