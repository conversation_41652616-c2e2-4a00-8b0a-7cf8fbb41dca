import { ApiProperty } from '@nestjs/swagger';
import { SDPCAgreementStatus, SDPCPublicStatus } from '@goteacher/app/models/mongo/sdpc.model';

export class GetAgreementResponseDto {
  @ApiProperty({
    example: '195',
    description: 'Data ID',
  })
  dataid: string;

  @ApiProperty({
    example: 'Example Company Inc.',
    description: 'Company name',
  })
  company_name: string;

  @ApiProperty({
    example: 'Example Software',
    description: 'Software name',
  })
  software_name: string;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Date approved',
  })
  date_approved: Date;

  @ApiProperty({
    example: '2025-01-01T00:00:00.000Z',
    description: 'Date expired',
  })
  date_expired: Date;

  @ApiProperty({
    example: SDPCAgreementStatus.ACTIVE,
    description: 'Agreement status',
    enum: SDPCAgreementStatus,
  })
  status: SDPCAgreementStatus;

  @ApiProperty({
    example: '3',
    description: 'Agreement type ID',
  })
  agreement_typesid: string;

  @ApiProperty({
    example: 'https://example.com/agreement.pdf',
    description: 'Signed agreement file URL',
  })
  signed_agreement_file: string;

  @ApiProperty({
    example: '2023-2024',
    description: 'Academic year',
  })
  year: string;

  @ApiProperty({
    example: '6, 7, 8',
    description: 'Grade levels',
  })
  grade_level: string;

  @ApiProperty({
    example: 'Math, Science',
    description: 'Content areas',
  })
  content_area: string;

  @ApiProperty({
    example: 'Example School District',
    description: 'District name',
  })
  district_name: string;

  @ApiProperty({
    example: 'New York',
    description: 'City',
  })
  city: string;

  @ApiProperty({
    example: 'NY',
    description: 'State',
  })
  state: string;

  @ApiProperty({
    example: 'United States',
    description: 'Country',
  })
  country: string;

  @ApiProperty({
    description: 'Agreement description',
  })
  description: string;

  @ApiProperty({
    example: 'IP Addresses, Cookies',
    description: 'Data list',
  })
  data_list: string;

  @ApiProperty({
    description: 'Purpose of the software',
  })
  purpose: string;

  @ApiProperty({
    description: 'Privacy policy URL',
  })
  privacy_policy: string;

  @ApiProperty({
    description: 'Terms of service URL',
  })
  terms_of_services: string;
} 