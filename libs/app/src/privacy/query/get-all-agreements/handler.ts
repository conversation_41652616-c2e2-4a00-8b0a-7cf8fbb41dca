import {
  SDPCAgreement,
  SDPCPublicStatus,
} from '@goteacher/app/models/mongo/sdpc.model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GetAllAgreementsQuery } from './query';

@QueryHandler(GetAllAgreementsQuery)
export class GetAllAgreementsHandler
  implements IQueryHandler<GetAllAgreementsQuery>
{
  constructor(
    @InjectModel(SDPCAgreement.name)
    private readonly sdpcAgreementModel: Model<SDPCAgreement>,
  ) {}

  async execute(query: GetAllAgreementsQuery) {
    const { organizationId, search, offset = 0, limit = 10 } = query;

    const filter: any = {
      districtid: organizationId,
      public_status: SDPCPublicStatus.YES,
    };

    // Add search condition if search term is provided
    if (search) {
      filter.$or = [
        { company_name: { $regex: search, $options: 'i' } },
        { software_name: { $regex: search, $options: 'i' } },
        { agreement_name: { $regex: search, $options: 'i' } },
      ];
    }

    // Get total count for pagination
    const total = await this.sdpcAgreementModel.countDocuments(filter);

    // Calculate skip value for pagination
    const skip = offset;

    // Get paginated agreements
    const agreements = await this.sdpcAgreementModel
      .find(filter)
      .sort({ company_name: 1, software_name: 1 })
      .skip(skip)
      .limit(limit)
      .exec();

    return {
      data: agreements,
      total: total,
      limit: limit,
      offset: offset,
    };
  }
}
