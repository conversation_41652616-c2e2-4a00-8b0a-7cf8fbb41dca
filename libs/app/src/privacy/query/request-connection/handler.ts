import { EmailService } from '@goteacher/app/common/email/email.service';
import { Organisation, OrganisationStatus } from '@goteacher/app/models/sequelize/organisation.model';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { RequestConnectionQuery } from './query';

@QueryHandler(RequestConnectionQuery)
export class RequestConnectionHandler implements IQueryHandler<RequestConnectionQuery> {
  constructor(
    @InjectModel(Organisation) private organizationModel: typeof Organisation,
    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
  ) { }

  async execute(query: RequestConnectionQuery) {
    const orgId = query.ctx.user.UserSchool.map((s) => s.school.organisationId)[0];

    const org = await this.organizationModel.findOne({ where: { id: orgId } });

    if (!org) {
      throw new NotFoundException('Organization not found');
    }

    if (org.status !== OrganisationStatus.APPROVED) {
      throw new BadRequestException('Organization is not active');
    }

    if (org.sdpcDistrictId) {
      throw new BadRequestException('Organization already has a connection to SDPC');
    }

    // Send email to SDPC Admin
    await this.emailService.sendEmail(
      this.configService.get('SDPC_ADMIN_EMAIL'),
      `${org.name} - SDPC request`,
      `Org ${org.name} - ID: ${org.id} has requested to connect to SDPC`
    );

    return {
      message: 'Connection request sent',
    };
  }
}
