import {
  SDPCAgreement,
  SDPCPublicStatus,
} from '@goteacher/app/models/mongo/sdpc.model';
import { I<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GetAgreementQuery } from './query';
import { NotFoundException } from '@nestjs/common';

@QueryHandler(GetAgreementQuery)
export class GetAgreementHandler implements IQueryHandler<GetAgreementQuery> {
  constructor(
    @InjectModel(SDPCAgreement.name)
    private readonly sdpcAgreementModel: Model<SDPCAgreement>,
  ) {}

  async execute(query: GetAgreementQuery) {
    const agreement = await this.sdpcAgreementModel
      .findOne({
        districtid: query.organizationId,
        dataid: query.dataId,
        public_status: SDPCPublicStatus.YES,
      })
      .exec();

    if (!agreement) {
      throw new NotFoundException('Agreement not found');
    }

    return agreement;
  }
}
