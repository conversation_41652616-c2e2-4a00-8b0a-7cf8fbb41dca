import {
  SDPCAgreement,
  SDPCPublicStatus,
} from '@goteacher/app/models/mongo/sdpc.model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GetAgreementsQuery } from './query';

@QueryHandler(GetAgreementsQuery)
export class GetAgreementsHandler implements IQueryHandler<GetAgreementsQuery> {
  constructor(
    @InjectModel(SDPCAgreement.name)
    private readonly sdpcAgreementModel: Model<SDPCAgreement>,
  ) {}

  async execute(query: GetAgreementsQuery) {
    const agreements = await this.sdpcAgreementModel
      .find({
        districtid: query.organizationId,
        softwareid: query.softwareId,
        public_status: SDPCPublicStatus.YES,
      })
      .exec();

    return { agreements };
  }
}
