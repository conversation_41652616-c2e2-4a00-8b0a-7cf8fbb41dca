import { SDPCAgreement, SDPCPublicStatus } from '@goteacher/app/models/mongo/sdpc.model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GetCompaniesQuery } from './query';

@QueryHandler(GetCompaniesQuery)
export class GetCompaniesHandler implements IQueryHandler<GetCompaniesQuery> {
  constructor(
    @InjectModel(SDPCAgreement.name)
    private readonly sdpcAgreementModel: Model<SDPCAgreement>,
  ) {}

  async execute(query: GetCompaniesQuery) {
    const companies = await this.sdpcAgreementModel
      .aggregate([
        {
          $match: {
            districtid: query.organizationId,
            public_status: SDPCPublicStatus.YES,
          },
        },
        {
          $group: {
            _id: '$company_name',
            count: { $sum: 1 },
          },
        },
        {
          $project: {
            _id: 0,
            company_name: '$_id',
            agreements_count: '$count',
          },
        },
        {
          $sort: { company_name: 1 },
        },
      ])
      .exec();

    return { companies };
  }
} 