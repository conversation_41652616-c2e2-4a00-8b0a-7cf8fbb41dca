import {
  SDPCAgreement,
  SDPCPublicStatus,
} from '@goteacher/app/models/mongo/sdpc.model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GetSoftwareQuery } from './query';

@QueryHandler(GetSoftwareQuery)
export class GetSoftwareHandler implements IQueryHandler<GetSoftwareQuery> {
  constructor(
    @InjectModel(SDPCAgreement.name)
    private readonly sdpcAgreementModel: Model<SDPCAgreement>,
  ) {}

  async execute(query: GetSoftwareQuery) {
    const matchStage: any = {
      districtid: query.organizationId,
      public_status: SDPCPublicStatus.YES,
    };

    if (query.companyName) {
      matchStage.company_name = query.companyName;
    }
    const software = await this.sdpcAgreementModel
      .aggregate([
        {
          $match: matchStage,
        },
        {
          $group: {
            _id: {
              softwareid: '$softwareid',
              software_name: '$software_name',
              company_name: '$company_name',
            },
            count: { $sum: 1 },
          },
        },
        {
          $project: {
            _id: 0,
            softwareid: '$_id.softwareid',
            software_name: '$_id.software_name',
            company_name: '$_id.company_name',
            agreements_count: '$count',
          },
        },
        {
          $sort: { software_name: 1 },
        },
      ])
      .exec();

    return { software };
  }
}
