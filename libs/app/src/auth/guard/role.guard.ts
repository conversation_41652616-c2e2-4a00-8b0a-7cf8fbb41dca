import { UserRole } from '@goteacher/app/models/sequelize/user.model';
import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

export const RoleProtected = (roles: UserRole[]) => {
  return SetMetadata('roles', roles);
};

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    if (!request.user) {
      throw new Error(
        `Can't find user in request object. Most likely you forgot to add @UseGuards(AuthGuard)`,
      );
    }
 
    const acceptedRoles = this.reflector.getAllAndOverride<string>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    const userRole = request.user.role;
    if (!acceptedRoles.includes(userRole))
      throw new ForbiddenException(
        'You are not authorized to access this route',
      );

    return true;
  }
}
