import { IJwtPayload, JWTService } from '@goteacher/app/auth/service';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { ICacheService } from '@goteacher/infra/cache';
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

export enum JWTProvider {
  GOOGLE = 'GOOGLE',
  GOTEACHER = 'GOTEACHER',
  APPLE = 'APPLE',
  CLEVER = 'CLEVER',
}

@Injectable()
export class JWTGuard implements CanActivate {
  private readonly logger = new Logger(JWTGuard.name);
  constructor(
    @InjectModel(User) private userModel: typeof User,
    private cacheService: ICacheService,
    private jwtService: JWTService,
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest();
      const endpoint = `${request.method} ${request.url}`;
      const token = this.extractTokenFromHeader(request);

      if (!token) {
        this.logger.warn(`No token provided in request to ${endpoint}`);
        throw new UnauthorizedException('No authorization token provided');
      }

      try {
        const payload = await this.jwtService.decode(token);

        if (!payload.role) {
          throw new UnauthorizedException('Old token, please login again');
        }

        const cache = await this.cacheService.get<{
          user: User;
          provider: JWTProvider;
          payload?: IJwtPayload;
        }>(`user-${payload.sub}`);
        if (cache && cache.user) {
          request['user'] = cache.user;
          request['provider'] = cache.provider;
          request['token'] = token;

          request['tokenPayload'] = cache.payload;
          if (cache.payload?.sdpcDistrictId) {
            request['sdpcDistrictId'] = cache.payload.sdpcDistrictId;
          }

          return true;
        }

        const user = await this.userModel.findOne({
          where: { email: payload.email, deleted: false, decommissionedAt: null },
          include: [
            {
              model: UserSchool,
              include: [
                {
                  model: School,
                  attributes: ['id', 'organisationId'],
                },
              ],
            },
          ],
        });

        if (!user) throw new UnauthorizedException();

        request['user'] = user;
        request['provider'] = payload.provider;
        request['token'] = token;

        request['tokenPayload'] = payload;
        if (payload.sdpcDistrictId) {
          request['sdpcDistrictId'] = payload.sdpcDistrictId;
        }

        this.cacheService.set(
          `user-${user.id}`,
          {
            user,
            provider: payload.provider,
            payload,
          },
          60 * 60,
        );

        return true;
      } catch (jwtError) {
        this.logger.error(`JWT validation failed for endpoint: ${endpoint}`);
        this.logger.error(`Token preview: ${token}`);
        this.logger.error(`JWT Error type: ${jwtError.name}`);
        this.logger.error(`JWT Error message: ${jwtError.message}`);
        throw new UnauthorizedException('Invalid token');
      }
    } catch (err) {
      if (!(err instanceof UnauthorizedException)) {
        this.logger.error('JWT guard failed:');
        this.logger.error(`Error type: ${err.name}`);
        this.logger.error(`Error message: ${err.message}`);
      }
      throw new UnauthorizedException();
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    try {
      const authHeader = request.headers['authorization'];
      if (!authHeader) {
        this.logger.warn('No authorization header present');
        return undefined;
      }

      const [type, token] = authHeader.split(' ') ?? [];
      if (type !== 'Bearer') {
        this.logger.warn(`Invalid authorization type: ${type}`);
        return undefined;
      }

      return token;
    } catch (error) {
      this.logger.error('Error extracting token from header:', error);
      return undefined;
    }
  }
}
