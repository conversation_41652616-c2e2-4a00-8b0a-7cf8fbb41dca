import { GOOGLE_SCOPES } from '@goteacher/app/auth/oauth/google/google.oauth.scopes';
import { ModuleMetadata } from '@nestjs/common';

export enum IPrompt {
  NONE = 'none',
  CONSENT = 'consent',
  SELECT_ACCOUNT = 'select_account',
}

export enum IAccessType {
  ONLINE = 'online',
  OFFLINE = 'offline',
}

export type IGoogleAuthorizeParameters = {
  client_id?: string;
  redirect_uri?: string;
  response_type?: string;
  scope: GOOGLE_SCOPES[];
  state?: string;
  access_type?: IAccessType;
  include_granted_scopes?: boolean;
  login_hint?: string;
  prompt?: IPrompt;
};

export type IGoogleTokenParameters = {
  client_id?: string;
  client_secret?: string;
  code?: string;
  grant_type?: string;
  redirect_uri?: string;
};

export type IGoogleRefreshParameters = {
  client_id?: string;
  client_secret?: string;
  refresh_token: string;
};

export type IGoogleInvalidateParameters = {
  client_id?: string;
  client_secret?: string;
  token: string;
};

export type IGoogleMeParameters = {
  client_id?: string;
  client_secret?: string;
  redirect_uri?: string;
  token: string;
};

export interface Credentials {
  refresh_token?: string | null;
  expiry_date?: number | null;
  access_token?: string | null;
  token_type?: string | null;
  id_token?: string | null;
  scope?: string;
}

export interface IGoogleModuleConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface IGoogleModuleOptions extends Pick<ModuleMetadata, 'imports'> {
  useFactory?: (...args: any[]) => IGoogleModuleConfig;
  inject?: any[];
}
