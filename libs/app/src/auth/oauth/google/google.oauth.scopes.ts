export enum GOOGLE_SCOPES {
  EMAIL = 'https://www.googleapis.com/auth/userinfo.email',
  PROFILE = 'https://www.googleapis.com/auth/userinfo.profile',
  OPENID = 'openid',

  CLASSROOM_ANNOUNCEMENTS = 'https://www.googleapis.com/auth/classroom.announcements',
  CLASSROOM_COURSES = 'https://www.googleapis.com/auth/classroom.courses',
  CLASSROOM_COURSEWORK_ME = 'https://www.googleapis.com/auth/classroom.coursework.me',
  CLASSROOM_COURSEWORK_STUDENTS = 'https://www.googleapis.com/auth/classroom.coursework.students',
  CLA<PERSON><PERSON><PERSON>_COURSEWORK_MATERIALS = 'https://www.googleapis.com/auth/classroom.courseworkmaterials',
  CLASSROOM_GUARDIANLINKS_STUDENTS_READONLY = 'https://www.googleapis.com/auth/classroom.guardianlinks.students.readonly',
  CLASSROOM_PROFILE_EMAILS = 'https://www.googleapis.com/auth/classroom.profile.emails',
  CLASSROOM_PROFILE_PHOTOS = 'https://www.googleapis.com/auth/classroom.profile.photos',
  CLASSROOM_PUSH_NOTIFICATIONS = 'https://www.googleapis.com/auth/classroom.push-notifications',
  CLASSROOM_ROSTERS_READONLY = 'https://www.googleapis.com/auth/classroom.rosters.readonly',
  CLASSROOM_TOPICS = 'https://www.googleapis.com/auth/classroom.topics',
  
  // Admin SDK scopes
  ADMIN_DIRECTORY_USER_SECURITY = 'https://www.googleapis.com/auth/admin.directory.user.security',
  ADMIN_DIRECTORY_USER_READONLY = 'https://www.googleapis.com/auth/admin.directory.user.readonly',
  ADMIN_REPORTS_AUDIT_READONLY = 'https://www.googleapis.com/auth/admin.reports.audit.readonly',
}
