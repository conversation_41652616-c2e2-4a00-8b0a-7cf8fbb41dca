import { GoogleOAuthStrategy } from '@goteacher/app/auth/oauth/google/google.oauth.strategy';
import { IGoogleModuleOptions } from '@goteacher/app/auth/oauth/google/google.oauth.types';
import { DynamicModule, Module, Provider } from '@nestjs/common';

@Module({})
export class GoogleOAuthModule {
  public static forRoot(options: IGoogleModuleOptions): DynamicModule {
    const providers = GoogleOAuthModule.buildProviders(options);
    return {
      module: GoogleOAuthModule,
      imports: options.imports,
      exports: providers,
      providers,
    };
  }

  private static buildProviders(options: IGoogleModuleOptions): Provider[] {
    return [
      {
        provide: GoogleOAuthStrategy,
        inject: options.inject,
        useFactory: async (...args: any[]) => {
          const serviceOptions = options.useFactory(...args);
          return new GoogleOAuthStrategy(serviceOptions);
        },
      },
    ];
  }
}
