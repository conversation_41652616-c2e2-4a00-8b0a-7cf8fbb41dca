import {
  IAccessType,
  IGoogleAuthorizeParameters,
  IGoogleInvalidateParameters,
  IGoogleMeParameters,
  IGoogleModuleConfig,
  IGoogleRefreshParameters,
  IGoogleTokenParameters,
} from '@goteacher/app/auth/oauth/google/google.oauth.types';
import { IOAuthStrategy } from '@goteacher/app/auth/oauth/oauth.strategy';
import { Credentials, OAuth2Client, TokenPayload } from 'google-auth-library';

export class GoogleOAuthStrategy
  implements
    IOAuthStrategy<
      IGoogleAuthorizeParameters,
      IGoogleTokenParameters,
      IGoogleRefreshParameters,
      IGoogleInvalidateParameters,
      IGoogleMeParameters
    >
{
  private readonly CLIENT_ID: string;
  private readonly CLIENT_SECRET: string;
  private readonly REDIRECT_URI: string;
  constructor(config: IGoogleModuleConfig) {
    this.CLIENT_ID = config.clientId;
    this.CLIENT_SECRET = config.clientSecret;
    this.REDIRECT_URI = config.redirectUri;
  }

  authorize(params: IGoogleAuthorizeParameters): string {
    return new OAuth2Client().generateAuthUrl({
      client_id: params.client_id || this.CLIENT_ID,
      redirect_uri: params.redirect_uri || this.REDIRECT_URI,
      scope: params.scope,
      state: params.state,
      access_type: params.access_type || IAccessType.OFFLINE,
      include_granted_scopes: params.include_granted_scopes || true,
      login_hint: params.login_hint,
      prompt: params.prompt,
    });
  }

  async token(params: IGoogleTokenParameters): Promise<Credentials> {
    const OAuthClient = new OAuth2Client({
      clientId: params.client_id || this.CLIENT_ID,
      clientSecret: params.client_secret || this.CLIENT_SECRET,
      redirectUri: params.redirect_uri || this.REDIRECT_URI,
    });
    const { tokens } = await OAuthClient.getToken(params.code);

    return tokens;
  }

  async refresh(params: IGoogleRefreshParameters): Promise<Credentials> {
    const OAuthClient = new OAuth2Client({
      clientId: params.client_id || this.CLIENT_ID,
      clientSecret: params.client_secret || this.CLIENT_SECRET,
    });
    OAuthClient.setCredentials({
      refresh_token: params.refresh_token,
    });

    const response = await OAuthClient.refreshAccessToken();

    return response.credentials;
  }

  async invalidate(params: IGoogleInvalidateParameters): Promise<void> {
    const OAuthClient = new OAuth2Client({
      clientId: params.client_id || this.CLIENT_ID,
      clientSecret: params.client_secret || this.CLIENT_SECRET,
    });
    await OAuthClient.revokeToken(params.token);
  }

  async me(params: IGoogleMeParameters): Promise<TokenPayload> {
    const OAuthClient = new OAuth2Client({
      clientId: params.client_id || this.CLIENT_ID,
      clientSecret: params.client_secret || this.CLIENT_SECRET,
      redirectUri: params.redirect_uri || this.REDIRECT_URI,
    });
    const response = await OAuthClient.verifyIdToken({
      idToken: params.token,
      audience: params.client_id || this.CLIENT_ID,
    });

    return response.getPayload() as TokenPayload;
  }
}
