import { Strategy } from '@arendajaelu/nestjs-passport-apple';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';

@Injectable()
export class AppleStrategy extends PassportStrategy(Strategy, 'apple') {
  constructor(config: ConfigService) {
    super({
      clientID: config.get<string>('APPLE_OAUTH_CLIENT_ID'),
      teamID: config.get<string>('APPLE_OAUTH_TEAM_ID'),
      keyID: config.get<string>('APPLE_OAUTH_KEY_ID'),
      key: config.get<string>('APPLE_OAUTH_PRIVATE_KEY'),
      callbackURL: config.get<string>('APPLE_OAUTH_CALLBACK_URL'),
      passReqToCallback: false,
      scope: ['email', 'name'],
    });
  }
}
