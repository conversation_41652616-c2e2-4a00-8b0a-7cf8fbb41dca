import { JW<PERSON>rovider } from '@goteacher/app/auth/guard';
import { IJwtPayload } from '@goteacher/app/auth/service/jwt.service';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export interface ReqContext {
  userId: string;
  user: Partial<User>;
  provider: JWTProvider;
  token: string;
  sdpcDistrictId?: string;
  tokenPayload?: IJwtPayload;
}

export const ReqContext = createParamDecorator(
  (data, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    if (!request.user) {
      throw new Error(
        `Can't find user in request object. Most likely you forgot to add @UseGuards(AuthGuard)`,
      );
    }
    return {
      userId: request.user.id,
      user: request.user,
      provider: request.provider,
      token: request.token,
      sdpcDistrictId: request.sdpcDistrictId,
      tokenPayload: request.tokenPayload,
    } as ReqContext;
  },
);
