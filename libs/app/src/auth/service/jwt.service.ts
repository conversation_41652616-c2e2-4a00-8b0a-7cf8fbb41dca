import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

export type IJwtPayload = {
  email: string;
  sub: string;
  role?: string;
  provider: string;
  schoolName?: string;
  schoolIds: string[];
  orgIds: string[];
  sdpcDistrictId?: string;
  grade?: string;
};

@Injectable()
export class JWTService {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) { }

  signPayload(
    payload: IJwtPayload,
    expiresIn?: string,
  ): {
    accessToken: string;
    refreshToken: string;
  } {
    return {
      accessToken: this.jwtService.sign(payload, {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn:
          expiresIn || this.configService.get<string>('JWT_EXPIRES_IN'),
        issuer: 'https://api.goteacher.com',
      }),
      refreshToken: this.jwtService.sign(payload, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
        expiresIn:
          expiresIn || this.configService.get<string>('JWT_REFRESH_EXPIRES_IN'),
        issuer: 'https://api.goteacher.com',
      }),
    };
  }

  verifyAccessToken(token: string): IJwtPayload {
    return this.jwtService.verify(token, {
      secret: this.configService.get('JWT_SECRET'),
    });
  }

  verifyRefreshToken(token: string): IJwtPayload {
    return this.jwtService.verify(token, {
      secret: this.configService.get('JWT_REFRESH_SECRET'),
    });
  }

  async decode(token: string): Promise<IJwtPayload> {
    return (await this.jwtService.decode(token)) as IJwtPayload;
  }
}
