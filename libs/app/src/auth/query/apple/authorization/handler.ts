import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';

import { AppleAuthorizationQuery } from '@goteacher/app/auth/query/apple/authorization/query';
import { AppleAuthorizationQueryResponse } from '@goteacher/app/auth/query/apple/authorization/response';

@QueryHandler(AppleAuthorizationQuery)
export class AppleAuthorizationHandler
  implements IQueryHandler<AppleAuthorizationQuery>
{
  constructor() {}

  async execute(
    query: AppleAuthorizationQuery,
  ): Promise<AppleAuthorizationQueryResponse> {
    console.log(query);
    return new AppleAuthorizationQueryResponse({
      url: 'apple',
    });
  }
}
