import { GOOGLE_SCOPES, IAccessType, IPrompt } from '@goteacher/app/auth/oauth';

export class GoogleAuthorizationQuery {
  constructor(obj?: GoogleAuthorizationQuery) {
    if (obj) Object.assign(this, obj);
  }

  client_id?: string;
  redirect_uri?: string;
  response_type?: string;
  access_type?: IAccessType;
  include_granted_scopes?: boolean;
  login_hint?: string;
  prompt?: IPrompt;
  scope?: GOOGLE_SCOPES[];
  state?: string;
}
