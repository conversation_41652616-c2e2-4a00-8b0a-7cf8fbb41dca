import { GOOGLE_SCOPES, GoogleOAuthStrategy } from '@goteacher/app/auth/oauth';
import { GoogleAuthorizationQuery } from '@goteacher/app/auth/query/google/authorization/query';
import { GoogleAuthorizationQueryResponse } from '@goteacher/app/auth/query/google/authorization/response';
import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

@QueryHandler(GoogleAuthorizationQuery)
export class GoogleA<PERSON>orizationHandler
  implements IQueryHandler<GoogleAuthorizationQuery>
{
  constructor(private readonly googleOAuthStrategy: GoogleOAuthStrategy) {}

  async execute(
    query: GoogleAuthorizationQuery,
  ): Promise<GoogleAuthorizationQueryResponse> {
    return new GoogleAuthorizationQueryResponse({
      uri: this.googleOAuthStrategy.authorize({
        ...query,
        scope: query.scope || [
          GOOGLE_SCOPES.PROFILE,
          GOOGLE_SCOPES.EMAIL,
          GOOGLE_SCOPES.OPENID,
        ],
      }),
    });
  }
}
