import { AuthMeQuery } from '@goteacher/app/auth/query/goteacher/me/query';
import { Account } from '@goteacher/app/models/sequelize/account.model';
import { Organisation } from '@goteacher/app/models/sequelize/organisation.model';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@QueryHandler(AuthMeQuery)
export class AuthMeHandler implements IQueryHandler<AuthMeQuery> {
  constructor(
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Organisation) private organizationModel: typeof Organisation,
  ) {}

  async execute(query: AuthMeQuery): Promise<any> {
    const user = await this.userModel.findOne({
      where: { id: query.userId },
      include: [
        {
          model: Account,
          attributes: ['platform', 'externalId'],
        },
        {
          model: UserSchool,
          include: [
            {
              model: School,
              attributes: ['id', 'organisationId', 'name'],
              include: [
                {
                  model: Organisation,
                  attributes: ['id', 'name'],
                },
              ],
            },
          ],
        },
      ],
    });
    const orgIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.school.organisationId)
      : [];

    const orgId = orgIds.length ? orgIds[0] : null;

    const organisation = orgId
      ? await this.organizationModel.findByPk(orgId)
      : null;

    // if (organisation && organisation.sdpcDistrictId) {
    //   (user.dataValues as any).sdpcDistrictId = organisation.sdpcDistrictId;
    // }

    return { ...user.dataValues, sdpcDistrictId: organisation?.sdpcDistrictId };
  }
}
