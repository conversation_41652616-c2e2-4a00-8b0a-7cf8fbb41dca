import { RefreshTokenCommand } from '@goteacher/app/auth/command/goteacher/refresh-token/command';
import { JW<PERSON>rovider } from '@goteacher/app/auth/guard';
import { JWTService } from '@goteacher/app/auth/service';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { ICacheService } from '@goteacher/infra/cache';
import { UnauthorizedException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@CommandHandler(RefreshTokenCommand)
export class RefreshTokenHandler
  implements ICommandHandler<RefreshTokenCommand>
{
  constructor(
    private readonly jwtService: JWTService,
    private cacheService: ICacheService,
    @InjectModel(User) private userModel: typeof User,
  ) {}

  async execute(
    command: RefreshTokenCommand,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const payload = this.jwtService.verifyRefreshToken(command.refreshToken);

    const user = await this.userModel.findOne({
      where: { email: payload.email, decommissionedAt: null },
      include: [
        {
          model: UserSchool,
          include: [
            {
              model: School,
              attributes: ['id', 'organisationId'],
            },
          ],
        },
      ],
    });

    if (!user) throw new UnauthorizedException('User not found!');

    const schoolIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.schoolId)
      : [];
    const orgIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.school.organisationId)
      : [];

    this.cacheService.del(`user-${user.id}`);

    return this.jwtService.signPayload({
      email: user.email,
      sub: user.id,
      role: user.role,
      provider: JWTProvider.GOTEACHER,
      schoolIds,
      orgIds,
    });
  }
}
