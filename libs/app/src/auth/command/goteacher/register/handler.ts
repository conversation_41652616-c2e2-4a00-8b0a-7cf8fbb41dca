import { RegisterUserCommand } from '@goteacher/app/auth/command/goteacher/register/command';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { ICacheService } from '@goteacher/infra/cache';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@CommandHandler(RegisterUserCommand)
export class RegisterUserCommandHandler
  implements ICommandHandler<RegisterUserCommand> {
  constructor(
    @InjectModel(User) private readonly userModel: typeof User,
    private cacheService: ICacheService,
  ) { }

  async execute(command: RegisterUserCommand): Promise<User> {
    const user = await this.userModel.findOne({
      where: {
        email: command.ctx.user.email,
        decommissionedAt: null,
      },
      include: [
        {
          model: UserSchool,
          include: [
            {
              model: School,
              attributes: ['id', 'organisationId'],
            },
          ],
        },
      ],
    });

    const userHasRole = user.role;
    const userHasSchool = user.UserSchool.length;
    const userIsRegistered = user.isRegistrationCompleted;

    if (userHasRole && userHasSchool && userIsRegistered) return user;

    let toUpdate: any = {};
    if (command.role && !userHasRole) {
      toUpdate.role = command.role;
      user.changed('role', true);
    }

    if ((userHasRole || command.role) && userHasSchool) {
      toUpdate.isRegistrationCompleted = true;
      user.changed('isRegistrationCompleted', true);
    }

    user.set(toUpdate);
    await user.save();

    this.cacheService.del(`user-${user.id}`);

    return user;
  }
}
