import { GoogleExchangeCodeCommand } from '@goteacher/app/auth/command/google/exchange-code/command';
import { J<PERSON><PERSON>rovider } from '@goteacher/app/auth/guard';
import { GoogleOAuthStrategy } from '@goteacher/app/auth/oauth';
import { JWTService } from '@goteacher/app/auth/service';
import {
  Account,
  AccountPlatform,
} from '@goteacher/app/models/sequelize/account.model';
import {
  Organisation,
  OrganisationStatus,
} from '@goteacher/app/models/sequelize/organisation.model';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { ICacheService } from '@goteacher/infra/cache';
import { UnauthorizedException } from '@nestjs/common';
import { CommandHand<PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { toLower } from 'lodash';
import { Op } from 'sequelize';

@CommandHandler(GoogleExchangeCodeCommand)
export class GoogleExchangeCodeHandler
  implements ICommandHandler<GoogleExchangeCodeCommand>
{
  constructor(
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Organisation) private organizationModel: typeof Organisation,
    @InjectModel(Account) private accountModel: typeof Account,
    private cacheService: ICacheService,
    private readonly googleOAuthStrategy: GoogleOAuthStrategy,
    private readonly jwtService: JWTService,
  ) {}

  async execute(
    command: GoogleExchangeCodeCommand,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const state = new URLSearchParams(`?${command.state}`);

    const googleTokens = await this.googleOAuthStrategy.token({
      code: command.code,
      redirect_uri: state.get('redirect_uri'),
    });

    const userInfo = await this.googleOAuthStrategy.me({
      redirect_uri: state.get('redirect_uri'),
      token: googleTokens.id_token,
    });

    const emailDomain = userInfo.email.split('@')[1];
    const organization = await this.organizationModel.findOne({
      where: { domains: { [Op.like]: `%${emailDomain}%` } },
    });

    if (!organization || organization.status !== OrganisationStatus.APPROVED) {
      throw new UnauthorizedException({
        action: 'ORGANISATION_NOT_APPROVED',
      });
    }

    // Create user if not exists
    let user = await this.userModel.findOne({
      where: { email: toLower(userInfo.email), decommissionedAt: null },
      include: [
        {
          model: UserSchool,
          include: [
            {
              model: School,
              attributes: ['id', 'organisationId'],
            },
          ],
        },
      ],
    });

    if (!user) {
      user = await this.userModel.create({
        email: toLower(userInfo.email),
        firstName: userInfo.given_name,
        lastName: userInfo.family_name,
        picture: userInfo.picture,
        role: state.has('role') ? state.get('role') : undefined,
        isRegistrationCompleted: false,
      });
    }

    // Upsert Google credentials
    let account = await this.accountModel.findOne({
      where: {
        platform: AccountPlatform.GOOGLE_CLASSROOM,
        userId: user.id,
      },
    });

    if (account) {
      account = await account.update(
        {
          credentials: { ...account.credentials, ...googleTokens },
        },
        {
          where: {
            platform: AccountPlatform.GOOGLE_CLASSROOM,
            userId: user.id,
          },
        },
      );
    } else {
      account = await this.accountModel.create({
        platform: AccountPlatform.GOOGLE_CLASSROOM,
        credentials: googleTokens,
        externalId: userInfo.sub,
        userId: user.id,
      });
    }

    if (!account.credentials.refresh_token) {
      throw new UnauthorizedException({
        action: 'GOOGLE_RELOGIN',
      });
    }

    const schoolIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.schoolId)
      : [];
    const orgIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.school.organisationId)
      : [];

    this.cacheService.set(`user-${user.id}`, user, 60 * 60);

    const tokens = this.jwtService.signPayload({
      email: user.email,
      provider: JWTProvider.GOOGLE,
      role: user.role,
      sub: user.id,
      schoolIds,
      orgIds,
      sdpcDistrictId: organization.sdpcDistrictId,
    });

    return tokens;
  }
}
