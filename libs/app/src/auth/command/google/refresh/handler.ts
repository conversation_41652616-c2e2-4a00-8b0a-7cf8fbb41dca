import { GoogleRefreshCommand } from '@goteacher/app/auth/command/google/refresh/command';
import { Credentials, GoogleOAuthStrategy } from '@goteacher/app/auth/oauth';
import { Account } from '@goteacher/app/models/sequelize/account.model';
import { NotFoundException } from '@nestjs/common';
import { CommandHandler, IQueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@CommandHandler(GoogleRefreshCommand)
export class GoogleRefresh<PERSON>ommandHandler
  implements IQueryHandler<GoogleRefreshCommand>
{
  constructor(
    @InjectModel(Account) private readonly accountModel: typeof Account,
    private readonly googleOAuthStrategy: GoogleOAuthStrategy,
  ) {}

  async execute(command: GoogleRefreshCommand): Promise<Credentials> {
    const account = await this.accountModel.findOne({
      where: { userId: command.userId },
    });

    if (!account) {
      throw new NotFoundException('No credentials found for this user!');
    }

    const nowWithOffset = new Date();
    nowWithOffset.setMinutes(nowWithOffset.getMinutes() + 5);

    if (new Date(account.credentials.expiry_date) > nowWithOffset)
      return account.credentials;

    const newCredentials = await this.googleOAuthStrategy.refresh({
      refresh_token: account.credentials.refresh_token,
    });
    await account.update({ credentials: newCredentials });

    return newCredentials;
  }
}
