import { Test, TestingModule } from '@nestjs/testing';
import { UnauthorizedException } from '@nestjs/common';
import { GoogleExtensionTokenHandler } from './handler';
import { GoogleExtensionTokenCommand } from './command';
import { JWTService } from '@goteacher/app/auth/service';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { Account } from '@goteacher/app/models/sequelize/account.model';
import { ICacheService } from '@goteacher/infra/cache';
import { getModelToken } from '@nestjs/sequelize';
import { google } from 'googleapis';

// Mock googleapis
jest.mock('googleapis', () => ({
  google: {
    oauth2: jest.fn(),
  },
}));

// Mock google-auth-library
jest.mock('google-auth-library', () => ({
  OAuth2Client: jest.fn().mockImplementation(() => ({
    setCredentials: jest.fn(),
  })),
}));

describe('GoogleExtensionTokenHandler', () => {
  let handler: GoogleExtensionTokenHandler;
  let mockUserModel: any;
  let mockAccountModel: any;
  let mockCacheService: any;
  let mockJwtService: any;
  let mockOauth2: any;

  beforeEach(async () => {
    mockUserModel = {
      findOne: jest.fn(),
      create: jest.fn(),
    };

    mockAccountModel = {
      create: jest.fn(),
    };

    mockCacheService = {
      set: jest.fn(),
    };

    mockJwtService = {
      signPayload: jest.fn().mockReturnValue({
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
      }),
    };

    mockOauth2 = {
      userinfo: {
        get: jest.fn(),
      },
    };

    (google.oauth2 as jest.Mock).mockReturnValue(mockOauth2);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GoogleExtensionTokenHandler,
        {
          provide: getModelToken(User),
          useValue: mockUserModel,
        },
        {
          provide: getModelToken(Account),
          useValue: mockAccountModel,
        },
        {
          provide: ICacheService,
          useValue: mockCacheService,
        },
        {
          provide: JWTService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    handler = module.get<GoogleExtensionTokenHandler>(GoogleExtensionTokenHandler);
  });

  describe('execute', () => {
    it('should throw UnauthorizedException when Google API returns 401', async () => {
      const command = new GoogleExtensionTokenCommand({ token: 'expired-token' });
      
      const googleError = new Error('Token expired');
      (googleError as any).status = 401;
      mockOauth2.userinfo.get.mockRejectedValue(googleError);

      await expect(handler.execute(command)).rejects.toThrow(
        new UnauthorizedException('Google access token is invalid or expired')
      );
    });

    it('should throw UnauthorizedException when Google API returns 403', async () => {
      const command = new GoogleExtensionTokenCommand({ token: 'insufficient-permissions-token' });
      
      const googleError = new Error('Insufficient permissions');
      (googleError as any).status = 403;
      mockOauth2.userinfo.get.mockRejectedValue(googleError);

      await expect(handler.execute(command)).rejects.toThrow(
        new UnauthorizedException('Google access token does not have required permissions')
      );
    });

    it('should throw UnauthorizedException for other Google API errors', async () => {
      const command = new GoogleExtensionTokenCommand({ token: 'invalid-token' });
      
      const googleError = new Error('Some other error');
      (googleError as any).status = 500;
      mockOauth2.userinfo.get.mockRejectedValue(googleError);

      await expect(handler.execute(command)).rejects.toThrow(
        new UnauthorizedException('Failed to validate Google access token')
      );
    });

    it('should successfully process valid token and create new user', async () => {
      const command = new GoogleExtensionTokenCommand({ token: 'valid-token' });
      
      const mockUserInfo = {
        data: {
          email: '<EMAIL>',
          given_name: 'Test',
          family_name: 'User',
          picture: 'https://example.com/picture.jpg',
          id: 'google-user-id',
        },
      };

      mockOauth2.userinfo.get.mockResolvedValue(mockUserInfo);
      mockUserModel.findOne.mockResolvedValue(null); // User doesn't exist
      
      const mockCreatedUser = {
        id: 'user-id',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'unknown',
        UserSchool: [],
      };
      
      mockUserModel.create.mockResolvedValue(mockCreatedUser);

      const result = await handler.execute(command);

      expect(result).toEqual({
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
      });

      expect(mockUserModel.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        picture: 'https://example.com/picture.jpg',
        isRegistrationCompleted: false,
      });

      expect(mockAccountModel.create).toHaveBeenCalled();
      expect(mockCacheService.set).toHaveBeenCalledWith('user-user-id', mockCreatedUser, 60 * 60);
    });
  });
});
