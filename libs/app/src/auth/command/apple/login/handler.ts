import { AppleLoginCommand } from '@goteacher/app/auth/command/apple/login/command';
import { J<PERSON><PERSON>rovider } from '@goteacher/app/auth/guard';
import { JWTService } from '@goteacher/app/auth/service';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { ICacheService } from '@goteacher/infra/cache';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { toLower } from 'lodash';

@CommandHandler(AppleLoginCommand)
export class AppleLoginHandler implements ICommandHandler<AppleLoginCommand> {
  constructor(
    @InjectModel(User) private userModel: typeof User,
    private cacheService: ICacheService,
    private readonly jwtService: JWTService,
  ) {}

  async execute(command: AppleLoginCommand): Promise<any> {
    if (command.hasOwnProperty('id_token')) {
      let email,
        firstName,
        lastName = '';

      const decodedObj = await this.jwtService.decode(command.id_token);
      const accountId = decodedObj.sub || '';      

      if (decodedObj.hasOwnProperty('email')) {
        email = decodedObj['email'];
      }

      if (command.hasOwnProperty('user')) {
        const userData = JSON.parse(command.user);
        const { firstName: _firstName, lastName: _lastName } =
          userData.name || {};

        firstName = _firstName || '';
        lastName = _lastName || '';
      }

      let user = await this.userModel.findOne({
        where: { email: toLower(email), decommissionedAt: null },
        include: [
          {
            model: UserSchool,
            include: [
              {
                model: School,
                attributes: ['id', 'organisationId'],
              },
            ],
          },
        ],
      });

      if (!user) {
        user = await this.userModel.create({
          email: toLower(email),
          firstName: firstName,
          lastName: lastName,
          picture: '',
          role: 'student',
          isRegistrationCompleted: false,
        });

        // TODO: ?
        // await this.accountModel.create({
        //   platform: 'apple',
        //   credentials: {},
        //   externalId: accountId,
        //   userId: user.id,
        // });
      }

      const schoolIds = user.UserSchool
        ? user.UserSchool.map((userSchool) => userSchool.schoolId)
        : [];
      const orgIds = user.UserSchool
        ? user.UserSchool.map((userSchool) => userSchool.school.organisationId)
        : [];

      this.cacheService.set(`user-${user.id}`, user, 60 * 60);

      const tokens = this.jwtService.signPayload({
        email: user.email,
        provider: JWTProvider.APPLE,
        role: user.role,
        sub: user.id,
        schoolIds,
        orgIds,
      });

      return tokens;
    }
  }
}
