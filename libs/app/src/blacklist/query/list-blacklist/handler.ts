import { ListBlacklistQuery } from '@goteacher/app/blacklist/query/list-blacklist/query';
import { Blacklist } from '@goteacher/app/models/sequelize/blacklist.model';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(ListBlacklistQuery)
export class ListBlacklistQueryHandler
  implements IQueryHandler<ListBlacklistQuery>
{
  constructor(
    @InjectModel(Blacklist) private readonly blacklistModel: typeof Blacklist,
  ) {}

  async execute(query: ListBlacklistQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    let whereClause: any = {
      orgId: { [Op.in]: userOrganizationIds },
    };

    if (query.domain) {
      whereClause = {
        ...whereClause,
        domain: query.domain,
      };
    }

    return await this.blacklistModel.findAll({
      where: where<PERSON>lause,
      order: [['createdAt', 'DESC']],
    });
  }
}
