import { CheckBlacklistQuery } from '@goteacher/app/blacklist/query/check-blacklist/query';
import { Product } from '@goteacher/app/models/mongo/product.model';
import { Blacklist } from '@goteacher/app/models/sequelize/blacklist.model';
import { ICacheService } from '@goteacher/infra/cache';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel as InjectModelMongoose } from '@nestjs/mongoose';
import { InjectModel } from '@nestjs/sequelize';
import { Model } from 'mongoose';

@QueryHandler(CheckBlacklistQuery)
export class CheckBlacklistQueryHandler
  implements IQueryHandler<CheckBlacklistQuery> {
  constructor(
    private readonly cacheService: ICacheService,
    @InjectModel(Blacklist) private blacklistModel: typeof Blacklist,
    @InjectModelMongoose(Product.name)
    private readonly productModel: Model<Product>,
  ) { }

  async execute(query: CheckBlacklistQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );
    const orgIds = ['*', ...userOrganizationIds];

    const cachingKeys = orgIds.map(
      (orgId) => `blacklist_${orgId}_${query.domain}`,
    );
    const cacheResults = await Promise.all(
      cachingKeys.map((key) =>
        this.cacheService.get<{
          orgId: string;
          domain: string;
          data: Blacklist[];
        }>(key),
      ),
    );

    let isBlacklisted = false;
    for (const orgId of orgIds) {
      const cache = cacheResults.find((c) => c?.orgId === orgId);

      if (cache) {
        isBlacklisted =
          isBlacklisted || (await this.checkBlacklist(query.url, cache.data));
        continue;
      }

      const blacklists = await this.blacklistModel.findAll({
        where: {
          domain: query.domain,
          orgId: orgId,
        },
      });

      this.cacheService.set(
        `blacklist_${orgId}_${query.domain}`,
        {
          orgId,
          domain: query.domain,
          data: blacklists,
        },
        24 * 60 * 60,
      );

      isBlacklisted =
        isBlacklisted || (await this.checkBlacklist(query.url, blacklists));
    }

    const response: any = {
      trackingAllowed: !isBlacklisted,
    };

    // If not blacklisted, enrich with product information
    if (!isBlacklisted) {
      const fullDomain = new URL(query.url).hostname;
      const productId = await this.getProductInfo(fullDomain);
      if (productId) {
        response.productId = productId;
      }
    }

    return response;
  }

  async checkBlacklist(url: string, blacklists: Blacklist[]) {
    return blacklists.some((b) => {
      const urlBlacklisted = b.url === url;
      const urlMatchesPattern = new RegExp(b.urlPattern).test(url);

      return urlBlacklisted || urlMatchesPattern;
    });
  }

  private async getProductInfo(fullDomain: string): Promise<any | null> {        
    const productCacheKey = `product-match:${fullDomain}`;    
    // Check cache first
    const cachedProduct = await this.cacheService.get<any>(productCacheKey);
    if (cachedProduct) {
      return cachedProduct;
    }

    const match = await this.productModel
      .findOne(
        {
          active: true,
          domainRules: {
            $elemMatch: {
              pattern: fullDomain,
              // TODO: add date validation once we have validFrom and validTo
              // validFrom: { $lte: new Date() },
              // $or: [{ validTo: null }, { validTo: { $gt: new Date() } }],
            },
          },
        },
        { _id: 1, name: 1, vendor: 1, type: 1, domainRules: 1 },
      )
      .sort({ 'domainRules.priority': -1 })
      .lean();

    const productId = match?._id.toString();

    if (productId) {
      await this.cacheService.set(productCacheKey, productId, 24 * 60 * 60);
    }

    return productId;
  }
}
