import { UpsertBlacklistCommand } from '@goteacher/app/blacklist/command/upsert-blacklist/command';
import { Blacklist } from '@goteacher/app/models/sequelize/blacklist.model';
import { ICacheService } from '@goteacher/infra/cache';
import { BadRequestException } from '@nestjs/common';
import { Command<PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@CommandHandler(UpsertBlacklistCommand)
export class UpsertBlacklistCommandHandler
  implements ICommandHandler<UpsertBlacklistCommand>
{
  constructor(
    @InjectModel(Blacklist) private readonly blacklistModel: typeof Blacklist,
    private cacheService: ICacheService,
  ) {}

  async execute(command: UpsertBlacklistCommand) {
    if (!command.url && !command.urlPattern)
      throw new BadRequestException('No url or urlPattern provided!');

    const userOrganizationIds = command.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const blacklists = await this.blacklistModel.bulkCreate(
      userOrganizationIds.map(
        (orgId) => ({
          orgId,
          domain: command.domain,
          url: command.url,
          urlPattern: command.urlPattern,
        }),
        { updateOnDuplicate: true },
      ),
    );

    for (const orgId of userOrganizationIds) {
      this.cacheService.invalidate(`blacklist_${orgId}_${command.domain}`);
    }

    return blacklists;
  }
}
