import { DeleteBlacklistCommand } from '@goteacher/app/blacklist/command/delete-blacklist/command';
import { Blacklist } from '@goteacher/app/models/sequelize/blacklist.model';
import { ICacheService } from '@goteacher/infra/cache';
import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@CommandHandler(DeleteBlacklistCommand)
export class DeleteBlacklistCommandHandler
  implements ICommandHandler<DeleteBlacklistCommand>
{
  constructor(
    @InjectModel(Blacklist) private readonly blacklistModel: typeof Blacklist,
    private cacheService: ICacheService,
  ) {}

  async execute(command: DeleteBlacklistCommand) {
    const userOrganizationIds = command.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const toDeleteBlacklist = await this.blacklistModel.findOne({
      where: {
        id: command.id,
      },
    });

    if (!toDeleteBlacklist) {
      throw new NotFoundException('Blacklist not found!');
    }
    if (!userOrganizationIds.includes(toDeleteBlacklist.orgId)) {
      throw new ForbiddenException(
        'You do not have permission to delete this blacklist!',
      );
    }

    await this.blacklistModel.destroy({ where: { id: command.id } });

    this.cacheService.invalidate(
      `blacklist_${toDeleteBlacklist.orgId}_${toDeleteBlacklist.domain}`,
    );

    return toDeleteBlacklist;
  }
}
