import { ScrapeEventCommand } from '@goteacher/app/event/command/scrape-event/command';
import { transformEvent } from '@goteacher/app/event/event.utility';
// import { IStorageService } from '@goteacher/infra/storage/storage.service';
// import { ConfigService } from '@nestjs/config';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';

@CommandHandler(ScrapeEventCommand)
export class ScrapeEventHandler implements ICommandHandler<ScrapeEventCommand> {
  constructor(
    // private storageService: IStorageService,
    // private configService: ConfigService,
  ) { }

  async execute(command: ScrapeEventCommand): Promise<any> {
    let transformedEvent = transformEvent({
      event: command.event,
      ip: command.ip,
      userId: command.ctx.user.id,
      tokenPayload: command.ctx.tokenPayload,
    });

    // TODO: Adapt for GCP
    // const eventKey = `event/${uuidv4()}`;
    // const ingestionS3Sink = this.configService.get('INGESTION_SINK');

    // this.storageService.writeObject(
    //   ingestionS3Sink,
    //   eventKey,
    //   JSON.stringify(transformedEvent),
    // );
    // delete transformedEvent.contents;
    // delete transformedEvent.metadata;

    // transformedEvent = {
    //   ...transformedEvent,
    //   target_event_path: `s3://${ingestionS3Sink}/${eventKey}`,
    //   target_event_key: `${eventKey}`,
    //   requested_tags: command.config.requested_tags,
    // };

    return transformedEvent;
  }
}
