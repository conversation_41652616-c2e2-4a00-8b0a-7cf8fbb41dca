import { CreateBatchEventCommand, CreateEventCommand } from '@goteacher/app/event/command/create-event/command';
import { EventType, NoActivityEvent } from '@goteacher/app/event/event.types';
import { transformEvent } from '@goteacher/app/event/event.utility';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';

type TransformedEvent = {
  sessionId: string;
  tabId: number;
  userId: string;
  domain: string;
  url: string;
}

@CommandHandler(CreateEventCommand)
export class CreateEventHandler implements ICommandHandler<CreateEventCommand> {
  constructor() { }

  async execute(command: CreateEventCommand): Promise<any> {
    const transformedEvent = transformEvent({
      event: command.event,
      ip: command.ip,
      userId: command.ctx.user.id,
      tokenPayload: command.ctx.tokenPayload
    });

    return transformedEvent;
  }
}

@CommandHandler(CreateBatchEventCommand)
export class CreateBatchEventHandler implements ICommandHandler<CreateBatchEventCommand> {
  constructor() { }

  async execute(command: CreateBatchEventCommand): Promise<{ events: any[], activitySessions: any[] }> {
    // Process all events in the batch with the same domain and userId - except for PAGE_SCRAPING events    
    const transformedEvents = command.events.filter(event => event.type !== EventType.PAGE_SCRAPING).map(event =>
      transformEvent({
        event,
        ip: command.ip,
        userId: command.ctx.user.id,
        tokenPayload: command.ctx.tokenPayload
      })
    );

    if (transformedEvents.length === 0) {
      console.error('Empty batch event', command);
      return { events: [], activitySessions: [] };
    }

    // do not use transformedEvents becasuse it does not have previousEvent
    const { previousEvent } = command.events[0];

    if (transformedEvents.length === 1 && !previousEvent) {
      // We're dealing with the first event case from extension
      return { events: transformedEvents, activitySessions: [] };
    }

    const activitySessions = [];

    if (previousEvent) {
      // We're dealing with the case where we have a previous event
      // current event type: PAGE_OPEN | NO_ACTIVITY
      // if (transformedEvents[0].type !== EventType.PAGE_OPEN && transformedEvents[0].type !== EventType.NO_ACTIVITY) {
      // console.error('Invalid current single event', transformedEvents[0]);
      // }

      const transformedPreviousEvent = transformEvent({
        event: previousEvent as NoActivityEvent, // Need to cast to NoActivityEvent because previousEvent is of type BaseEvent
        ip: command.ip,
        userId: command.ctx.user.id,
        tokenPayload: command.ctx.tokenPayload
      })

      const activitySession = this.createActivitySession(transformedPreviousEvent)

      const firstTimestamp = new Date(transformedPreviousEvent.clientTimestamp).getTime();
      const lastTimestamp = new Date(transformedEvents[0].clientTimestamp).getTime();
      let durationSeconds = Math.round((lastTimestamp - firstTimestamp) / 1000);

      if (durationSeconds > 0) {
        // We will create an activity session in case of > 0 second

        if (durationSeconds > 40) {
          // console.error('Duration is too long', durationSeconds, command.events);
          durationSeconds = 30;
        }

        Object.assign(activitySession, {
          startTime: new Date(firstTimestamp).toISOString(),
          endTime: new Date(lastTimestamp).toISOString(),
          duration: durationSeconds
        });

        activitySessions.push(activitySession);
      }
    }

    if (transformedEvents.length > 1) {
      // We're dealing with the case where we have multiple events
      const activitySession = this.createActivitySession(transformedEvents[0])

      const firstTimestamp = new Date(transformedEvents[0].clientTimestamp).getTime();
      const lastTimestamp = new Date(transformedEvents[transformedEvents.length - 1].clientTimestamp).getTime();
      let durationSeconds = Math.round((lastTimestamp - firstTimestamp) / 1000);

      if (durationSeconds > 40) {
        // console.error('Duration is too long', durationSeconds, command.events);
        durationSeconds = 30;
      }

      Object.assign(activitySession, {
        startTime: new Date(firstTimestamp).toISOString(),
        endTime: new Date(lastTimestamp).toISOString(),
        duration: durationSeconds
      });

      activitySessions.push(activitySession);
    }

    return { events: transformedEvents, activitySessions };
  }

  // TODO: Create TransformedEvent type
  createActivitySession(sampleEvent: any) {
    return {
      sessionId: sampleEvent.sessionId,
      tabId: sampleEvent.tabId,
      userId: sampleEvent.userId,
      domain: sampleEvent.domain,
      url: sampleEvent.url,
      fullDomain: new URL(sampleEvent.url).hostname.replace('www.', ''),
      productId: sampleEvent.productId || '',
      role: sampleEvent.role,
      orgId: sampleEvent.orgId,
      schoolId: sampleEvent.schoolId,
      grade: sampleEvent.grade,
      schoolYear: sampleEvent.schoolYear,
      clientTimezone: sampleEvent.clientTimezone,
      serverTimestamp: new Date().toISOString(),
      createdAt: new Date().toISOString()
    };
  }
}