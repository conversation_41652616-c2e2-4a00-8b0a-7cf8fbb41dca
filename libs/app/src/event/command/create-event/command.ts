import { ReqContext } from '@goteacher/app/auth';
import { IngestionEvent } from '@goteacher/app/event/event.types';

export class CreateEventCommand {
  constructor(obj: CreateEventCommand) {
    Object.assign(this, obj);
  }

  ip: string;
  event: IngestionEvent;
  ctx: ReqContext;
}

export class CreateBatchEventCommand {
  constructor(obj: CreateBatchEventCommand) {
    Object.assign(this, obj);
  }

  ip: string;
  events: IngestionEvent[];
  ctx: ReqContext;
}
