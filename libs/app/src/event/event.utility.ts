import { IJwtPayload } from '@goteacher/app/auth/service/jwt.service';
import {
  EventName,
  EventType,
  IdlePingEvent,
  IngestionEvent,
  InteractionSummaryEvent,
  KeydownEvent,
  PageScrapingEvent,
  ScrollEvent,
  VideoEndedEvent,
  VideoPauseEvent,
  VideoPlayEvent,
  VideoSeekEvent,
  VideoVolumeEvent,
} from '@goteacher/app/event/event.types';
import { getAcademicYear, timezoneOffset } from '@goteacher/app/utility';
import { cleanUrl } from '@goteacher/app/utility/url';
import { v4 as uuidv4, v5 as uuidv5 } from 'uuid';

export const transformEvent = (args: {
  event: IngestionEvent;
  ip: string;
  userId: string;
  tokenPayload?: IJwtPayload;
}) => {
  const orgId = args.tokenPayload?.orgIds?.[0];
  const schoolId = args.tokenPayload?.schoolIds?.[0];
  const grade = args.tokenPayload?.grade;
  const role = args.tokenPayload?.role;
  const actualSchoolYear = getAcademicYear(
    new Date(args.event.created_at),
  );
  const commonOptions = {
    ip: args.ip,
    sessionId: args.event.sessionId,
    userId: args.userId,
    deviceId: args.event.browserVersion,
    clientTimestamp: new Date(args.event.created_at).toISOString(),
    clientTimezone: timezoneOffset(
      args.event.clientTimezone || 'America/New_York',
    ),
    serverTimestamp: new Date().toISOString(),
    // WTF is this uuidv5.URL?
    tabId: uuidv5(args.event.tabId.toString(16), uuidv5.URL),
    domain: args.event.domain,
    fullDomain: new URL(args.event.url).hostname.replace('www.', ''),
    url: cleanUrl(args.event.url),
    browserType: args.event.browserBrand,
    platform: args.event.platform,
    traceId: uuidv4(),
    productId: args.event.productId, // retrived from blacklist api
    // enriched user info
    orgId: orgId,
    schoolId: schoolId,
    grade: grade,
    role: role,
    schoolYear: actualSchoolYear,
    extensionVersion: args.event.extensionVersion,
  };

  let specificProperties: any;

  switch (args.event.type) {
    case EventType.CLICK:
      specificProperties = {
        eventName: EventName.PAGE_CLICK,
        clickXCoordinate: args.event.posX,
        clickYCoordinate: args.event.posY,
      };
      break;
    case EventType.KEYDOWN:
      specificProperties = {
        eventName: EventName.TEXT_CHANGE,
        textLetterFrequency: (args.event as KeydownEvent).key,
      };
      break;
    case EventType.SCROLL:
      specificProperties = {
        eventName: EventName.PAGE_SCROLL,
        scrollDirection: 'N/A',
        scrollDistance: (args.event as ScrollEvent).scrollPercentage,
        scrollCurrentPosition: Math.round(
          (args.event as ScrollEvent).scrollTop,
        ),
        scrollViewPortSize: (args.event as ScrollEvent).winHeight,
        scrollTotalPageHeight: (args.event as ScrollEvent).docHeight,
      };
      break;
    case EventType.PAGE_OPEN:
      specificProperties = {
        eventName: EventName.PAGE_OPEN,
      };
      break;
    case EventType.PAGE_CLOSE:
      specificProperties = {
        eventName: EventName.PAGE_CLOSE,
      };
      break;
    case EventType.VIDEO_PLAY:
      specificProperties = {
        eventName: EventName.MEDIA_START,
        mediaType: 'VIDEO',
        mediaSource: args.event.url,
        mediaLength: Math.round((args.event as VideoPlayEvent).videoDuration),
      };
      break;
    case EventType.VIDEO_PAUSE:
      specificProperties = {
        eventName: EventName.MEDIA_PAUSE,
        mediaType: 'VIDEO',
        mediaSource: args.event.url,
        mediaLength: Math.round((args.event as VideoPauseEvent).videoDuration),
      };
      break;
    case EventType.VIDEO_SEEK:
      specificProperties = {
        eventName: EventName.MEDIA_SEEK_CHANGE,
        mediaType: 'VIDEO',
        mediaSource: args.event.url,
        mediaSeekTo: Math.round((args.event as VideoSeekEvent).videoTimeTo),
        mediaSeekFrom: Math.round((args.event as VideoSeekEvent).videoTimeFrom),
        mediaLength: Math.round((args.event as VideoSeekEvent).videoDuration),
      };
      break;
    case EventType.VIDEO_ENDED:
      specificProperties = {
        eventName: EventName.MEDIA_STOP,
        mediaType: 'VIDEO',
        mediaSource: args.event.url,
        mediaLength: Math.round((args.event as VideoEndedEvent).videoDuration),
      };
      break;
    case EventType.VIDEO_VOLUME:
      specificProperties = {
        eventName: EventName.MEDIA_VOLUME_CHANGE,
        mediaType: 'VIDEO',
        mediaSource: args.event.url,
        mediaVolume: Math.round((args.event as VideoVolumeEvent).videoVolume),
      };
      break;
    case EventType.PAGE_SCRAPING:
      specificProperties = {
        eventName: EventName.PAGE_SCRAPING,
        contents: (args.event as PageScrapingEvent).contents,
        metadata: (args.event as PageScrapingEvent).metadata,
        screenshot: (args.event as PageScrapingEvent).screenshot,
        headers: (args.event as PageScrapingEvent).headers,
        ldJson: (args.event as PageScrapingEvent).ldJson,
      };
      break;
    case EventType.NO_ACTIVITY:
      specificProperties = {
        eventName: 'NO_ACTIVITY',
      };
      break;
    case EventType.INTERACTION_SUMMARY:
      specificProperties = transformInteractionSummary(args.event as InteractionSummaryEvent);
      break;
    case EventType.IDLE_PING:
      specificProperties = transformIdlePing(args.event as IdlePingEvent);
      break;
    default:
      specificProperties = {};
      break;
  }

  return { ...commonOptions, ...specificProperties };
};

export const transformInteractionSummary = (event: InteractionSummaryEvent) => {
  return {
    eventName: 'INTERACTION_SUMMARY',
    startTime: new Date(event.startTime).toISOString(),
    endTime: new Date(event.endTime).toISOString(),
    duration: Math.max(0, Math.round(event.duration || 0)),
    clickCount: Math.max(0, Math.round(event.clickCount || 0)),
    scrollEventCount: Math.max(0, Math.round(event.scrollEventCount || 0)),
    keypressCount: Math.max(0, Math.round(event.keypressCount || 0)),
    mouseMoveDuration: Math.max(0, Math.round(event.mouseMoveDuration || 0)),
    maxScrollDepth: Math.min(100, Math.max(0, Math.round(event.maxScrollDepth || 0))),
    isVideoPlaying: Boolean(event.isVideoPlaying),
    flushReason: event.flushReason || 'timeout',
    eventQuality: event.eventQuality || 'normal',
  };
};

export const transformIdlePing = (event: IdlePingEvent) => {
  return {
    eventName: 'IDLE_PING',
    startTime: new Date(event.startTime).toISOString(),
    endTime: new Date(event.endTime).toISOString(),
    duration: Math.max(0, Math.round(event.duration || 0)),
    idleDuration: Math.max(0, Math.round(event.idleDuration || 0)),
    pingSequence: Math.max(0, Math.round(event.pingSequence || 0)),
    lastActivityTime: new Date(event.lastActivityTime).toISOString(),
  };
};
