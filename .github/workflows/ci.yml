name: Continous Integration

on:
  pull_request:

env:
  NODE_VERSION: 20.12.2

jobs:
  api:
    name: API
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR ${{ github.event.pull_request.title }} - COMMIT ${{ github.event.pull_request.head.ref }}:${{ github.event.pull_request.head.sha }}
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install NPM packages
        run: npm ci
      - name: Test build
        run: npm run api:build
      - name: Test docker build
        run: docker build -f ./docker/images/Dockerfile.api .

  export:
    name: EXPORT
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR ${{ github.event.pull_request.title }} - COMMIT ${{ github.event.pull_request.head.ref }}:${{ github.event.pull_request.head.sha }}
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install NPM packages
        run: npm ci
      - name: Test build
        run: npm run export:build
      - name: Test docker build
        run: docker build -f ./docker/images/Dockerfile.export .

  ingestion:
    name: INGESTION
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR ${{ github.event.pull_request.title }} - COMMIT ${{ github.event.pull_request.head.ref }}:${{ github.event.pull_request.head.sha }}
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install NPM packages
        run: npm ci
      - name: Test build
        run: npm run ingestion:build
      - name: Test docker build
        run: docker build -f ./docker/images/Dockerfile.ingestion .


  scraping_worker:
    name: SCRAPING-WORKER
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR ${{ github.event.pull_request.title }} - COMMIT ${{ github.event.pull_request.head.ref }}:${{ github.event.pull_request.head.sha }}
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install NPM packages
        run: npm ci
      - name: Test build
        run: npm run scraping_worker:build
      - name: Test docker build
        run: docker build -f ./docker/images/Dockerfile.scraping_worker .

  seeds:
    name: SEEDS
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR ${{ github.event.pull_request.title }} - COMMIT ${{ github.event.pull_request.head.ref }}:${{ github.event.pull_request.head.sha }}
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install NPM packages
        run: npm ci
      - name: Test build
        run: npm run seeds:build
      - name: Test docker build
        run: docker build -f ./docker/images/Dockerfile.seeds .

  migrations:
    name: MIGRATIONS
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR ${{ github.event.pull_request.title }} - COMMIT ${{ github.event.pull_request.head.ref }}:${{ github.event.pull_request.head.sha }}
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install NPM packages
        run: npm ci
      - name: Test docker build
        run: docker build -f ./docker/images/Dockerfile.migrations .
