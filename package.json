{"name": "new_api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"api:build": "nest build api", "seeds:build": "nest build seeds", "export:build": "nest build export", "ingestion:build": "nest build ingestion", "enrichment_worker:build": "nest build enrichment_worker", "scraping_worker:build": "nest build scraping_worker", "api:start:debug": "nest start --tsc --debug 0.0.0.0:$DEBUG_PORT --watch api", "seeds:start:debug": "nest start --tsc --debug 0.0.0.0:$DEBUG_PORT --watch seeds", "export:start:debug": "nest start --tsc --debug 0.0.0.0:$DEBUG_PORT --watch export", "ingestion:start:debug": "nest start --tsc --debug 0.0.0.0:$DEBUG_PORT --watch ingestion", "enrichment_worker:start:debug": "nest start --tsc --debug 0.0.0.0:$DEBUG_PORT --watch enrichment_worker", "scraping_worker:start:debug": "nest start --tsc --debug 0.0.0.0:$DEBUG_PORT --watch scraping_worker", "api:start:prod": "node dist/apps/api/apps/api/src/main", "seeds:start:prod": "node dist/apps/seeds/apps/seeds/src/main", "export:start:prod": "node dist/apps/export/apps/export/src/main", "ingestion:start:prod": "node dist/apps/ingestion/apps/ingestion/src/main", "enrichment_worker:start:prod": "node dist/apps/enrichment_worker/apps/enrichment_worker/src/main", "scraping_worker:start:prod": "node dist/apps/scraping_worker/apps/scraping_worker/src/main", "migrations:clickhouse:create": "sh ./scripts/create-migration.sh clickhouse", "migrations:clickhouse:run": "ts-node ./migrations/clickhouse.ts", "migrations:sequelize:create": "sh ./scripts/create-migration.sh sequelize", "migrations:sequelize:run": "ts-node ./migrations/sequelize.ts", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/new_api/test/jest-e2e.json"}, "dependencies": {"@arendajaelu/nestjs-passport-apple": "^2.0.3", "@aws-sdk/client-s3": "^3.616.0", "@aws-sdk/credential-providers": "^3.609.0", "@aws-sdk/s3-request-presigner": "^3.616.0", "@clickhouse/client": "^1.4.0", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "^8.3.1", "@fastify/static": "^7.0.4", "@google-cloud/storage": "^7.16.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/cqrs": "^10.2.7", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.3.10", "@nestjs/mongoose": "^10.0.10", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-fastify": "^10.3.9", "@nestjs/schedule": "^4.1.1", "@nestjs/sequelize": "^10.0.1", "@nestjs/swagger": "^7.3.1", "@nestjs/terminus": "^10.2.3", "@sendgrid/mail": "^8.1.5", "axios": "^1.7.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "clickhouse-migrations": "^0.1.14", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "fastify": "4.28.0", "google-auth-library": "^9.11.0", "googleapis": "^140.0.1", "handlebars": "^4.7.8", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "luxon": "^3.4.4", "mongoose": "^8.5.1", "nestjs-cls": "^4.3.0", "openai": "^5.5.1", "passport": "^0.7.0", "passport-http-bearer": "^1.0.1", "passport-oauth2": "^1.8.0", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "puppeteer": "^22.13.1", "redis": "^4.6.15", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sequelize": "^6.37.3", "sequelize-typescript": "^2.1.6", "umzug": "^3.8.1", "uuid": "^10.0.0"}, "devDependencies": {"@aws-sdk/types": "^3.609.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/sequelize": "^4.28.20", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@goteacher/app(|/.*)$": "<rootDir>/libs/app/src/$1", "^@goteacher/infra(|/.*)$": "<rootDir>/libs/infra/src/$1"}}}