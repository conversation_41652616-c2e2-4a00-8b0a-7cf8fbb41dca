{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "esModuleInterop": true, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "paths": {"@goteacher/app": ["libs/app/src"], "@goteacher/app/*": ["libs/app/src/*"], "@goteacher/infra": ["libs/infra/src"], "@goteacher/infra/*": ["libs/infra/src/*"]}}}